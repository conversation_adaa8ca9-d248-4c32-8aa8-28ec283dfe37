import { describe, it, expect, beforeEach, vi } from 'vitest';
import { PDFProcessingEngine } from '../src/engine';
import type { ProcessingOptions, ProcessingProgressCallback } from '../src/types/processing';

// Mock PDF-lib
vi.mock('pdf-lib', () => {
  const mockPDFDocument = {
    load: vi.fn(),
    getPageCount: vi.fn(() => 5),
    getTitle: vi.fn(() => 'Test Document'),
    getAuthor: vi.fn(() => 'Test Author'),
    save: vi.fn(() => Promise.resolve(new ArrayBuffer(1000))),
    getPages: vi.fn(() => [
      {
        getSize: () => ({ width: 612, height: 792 }),
        drawText: vi.fn(),
        drawImage: vi.fn()
      }
    ]),
    embedPng: vi.fn(() => Promise.resolve({
      scale: vi.fn(() => ({})),
      scaleToFit: vi.fn(() => ({}))
    }))
  };

  return {
    PDFDocument: {
      load: vi.fn(() => Promise.resolve(mockPDFDocument))
    },
    rgb: vi.fn(() => ({ r: 0.5, g: 0.5, b: 0.5 }))
  };
});

describe('PDFProcessingEngine', () => {
  let engine: PDFProcessingEngine;
  let mockFile: File;

  beforeEach(() => {
    engine = PDFProcessingEngine.getInstance();
    
    // Create a mock PDF file with proper File API methods
    const mockPDFData = new Uint8Array([0x25, 0x50, 0x44, 0x46]); // %PDF header
    mockFile = new File([mockPDFData], 'test.pdf', { type: 'application/pdf' });
    
    // Mock the arrayBuffer method for Node.js environment
    Object.defineProperty(mockFile, 'arrayBuffer', {
      value: vi.fn(() => Promise.resolve(mockPDFData.buffer)),
      writable: true
    });
    
    vi.clearAllMocks();
  });

  describe('getInstance', () => {
    it('should return the same instance (singleton)', () => {
      const engine1 = PDFProcessingEngine.getInstance();
      const engine2 = PDFProcessingEngine.getInstance();
      
      expect(engine1).toBe(engine2);
    });
  });

  describe('processDocument', () => {
    it('should process a PDF document successfully', async () => {
      const options: ProcessingOptions = {
        targetSize: 1000000,
        qualityLevel: 80
      };

      const progressCallback: ProcessingProgressCallback = vi.fn();

      const result = await engine.processDocument(mockFile, options, progressCallback);

      expect(result).toBeDefined();
      expect(result.data).toBeInstanceOf(Uint8Array);
      expect(result.originalSize).toBeGreaterThan(0);
      expect(result.processedSize).toBeGreaterThan(0);
      expect(result.compressionRatio).toBeGreaterThan(0);
      expect(result.metadata).toBeDefined();
      expect(result.metadata?.pages).toBe(5);
      expect(result.metadata?.title).toBe('Test Document');
      expect(result.metadata?.author).toBe('Test Author');
      
      // Verify progress callback was called
      expect(progressCallback).toHaveBeenCalled();
    });

    it('should handle processing errors gracefully', async () => {
      const { PDFDocument } = await import('pdf-lib');
      (PDFDocument.load as any).mockRejectedValueOnce(new Error('Invalid PDF'));

      await expect(
        engine.processDocument(mockFile, {})
      ).rejects.toThrow('PDF processing failed');
    });
  });

  describe('compressToTarget', () => {
    it('should compress PDF to target size', async () => {
      const targetSize = 500000;
      const progressCallback: ProcessingProgressCallback = vi.fn();

      const result = await engine.compressToTarget(mockFile, targetSize, progressCallback);

      expect(result).toBeInstanceOf(Uint8Array);
      expect(progressCallback).toHaveBeenCalled();
    });

    it('should handle compression errors', async () => {
      const { PDFDocument } = await import('pdf-lib');
      (PDFDocument.load as any).mockRejectedValueOnce(new Error('Compression failed'));

      await expect(
        engine.compressToTarget(mockFile, 1000000)
      ).rejects.toThrow('PDF compression failed');
    });
  });

  describe('validatePDF', () => {
    it('should validate a valid PDF file', async () => {
      const isValid = await engine.validatePDF(mockFile);
      expect(isValid).toBe(true);
    });

    it('should reject invalid PDF file', async () => {
      const { PDFDocument } = await import('pdf-lib');
      (PDFDocument.load as any).mockRejectedValueOnce(new Error('Invalid PDF'));

      const isValid = await engine.validatePDF(mockFile);
      expect(isValid).toBe(false);
    });
  });

  describe('getPDFInfo', () => {
    it('should return PDF information', async () => {
      const info = await engine.getPDFInfo(mockFile);

      expect(info).toBeDefined();
      expect(info.pages).toBe(5);
      expect(info.size).toBeGreaterThan(0);
      expect(info.title).toBe('Test Document');
      expect(info.author).toBe('Test Author');
    });

    it('should handle info extraction errors', async () => {
      const { PDFDocument } = await import('pdf-lib');
      (PDFDocument.load as any).mockRejectedValueOnce(new Error('Info extraction failed'));

      await expect(
        engine.getPDFInfo(mockFile)
      ).rejects.toThrow('Failed to get PDF info');
    });
  });

  describe('addSignature', () => {
    it('should add signature to PDF', async () => {
      const signature = {
        imageData: new Uint8Array([1, 2, 3, 4]),
        width: 100,
        height: 50
      };

      const position = {
        x: 100,
        y: 200,
        page: 0
      };

      const result = await engine.addSignature(mockFile, signature, position);
      expect(result).toBeInstanceOf(Uint8Array);
    });

    it('should handle invalid page number', async () => {
      const signature = {
        imageData: new Uint8Array([1, 2, 3, 4]),
        width: 100,
        height: 50
      };

      const position = {
        x: 100,
        y: 200,
        page: 10 // Invalid page number
      };

      await expect(
        engine.addSignature(mockFile, signature, position)
      ).rejects.toThrow('Page 10 does not exist');
    });
  });

  describe('addWatermark', () => {
    it('should add text watermark to PDF', async () => {
      const watermark = {
        text: 'CONFIDENTIAL',
        position: 'center' as const,
        opacity: 0.5,
        fontSize: 50
      };

      const result = await engine.addWatermark(mockFile, watermark);
      expect(result).toBeInstanceOf(Uint8Array);
    });

    it('should add image watermark to PDF', async () => {
      const watermark = {
        image: new Uint8Array([1, 2, 3, 4]),
        position: 'bottom-right' as const,
        opacity: 0.3
      };

      const result = await engine.addWatermark(mockFile, watermark);
      expect(result).toBeInstanceOf(Uint8Array);
    });
  });
});
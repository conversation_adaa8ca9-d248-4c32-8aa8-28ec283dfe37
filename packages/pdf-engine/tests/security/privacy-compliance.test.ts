import { describe, it, expect, beforeAll, afterAll, beforeEach, afterEach, vi } from 'vitest';
import { PDFEngine } from '../../src/engine';
import { NetworkMonitor } from '../utils/network-monitor';
import { MemoryInspector } from '../utils/memory-inspector';
import { StorageInspector } from '../utils/storage-inspector';
import type { PDFDocument } from '../../src/types/processing';

describe('Privacy Compliance and Security Tests', () => {
  let pdfEngine: PDFEngine;
  let networkMonitor: NetworkMonitor;
  let memoryInspector: MemoryInspector;
  let storageInspector: StorageInspector;
  let sensitiveDocument: PDFDocument;

  beforeAll(async () => {
    pdfEngine = new PDFEngine({
      enableNetworkMonitoring: true,
      enableMemoryTracking: true,
      enableStorageTracking: true,
      privacyMode: true
    });

    networkMonitor = new NetworkMonitor({
      trackAllRequests: true,
      detectDataLeaks: true,
      monitorHeaders: true
    });

    memoryInspector = new MemoryInspector({
      trackSensitiveData: true,
      enableDataWipe: true,
      scanInterval: 1000
    });

    storageInspector = new StorageInspector({
      monitorLocalStorage: true,
      monitorSessionStorage: true,
      monitorIndexedDB: true,
      monitorCookies: true
    });

    await pdfEngine.initialize();
  });

  afterAll(async () => {
    await pdfEngine.cleanup();
    networkMonitor.cleanup();
    memoryInspector.cleanup();
    storageInspector.cleanup();
  });

  beforeEach(() => {
    // Create sensitive test document with PII data
    sensitiveDocument = {
      id: 'sensitive-test-doc',
      buffer: new ArrayBuffer(2 * 1024 * 1024), // 2MB
      pageCount: 5,
      metadata: {
        title: 'Confidential Financial Report',
        author: 'John Smith',
        subject: 'Q4 2023 Financial Data',
        keywords: 'SSN:***********, Credit Card:4111-1111-1111-1111, confidential',
        creator: 'Financial System',
        producer: 'PDF Engine Tests',
        creationDate: new Date(),
        modificationDate: new Date()
      },
      encrypted: false,
      permissions: {
        canPrint: true,
        canModify: true,
        canCopy: true,
        canAnnotate: true
      },
      containsSensitiveData: true,
      sensitivePatterns: [
        'SSN:***********',
        'Credit Card:4111-1111-1111-1111',
        '<EMAIL>',
        'Account Balance: $125,000.50'
      ]
    };

    networkMonitor.startMonitoring();
    memoryInspector.startInspection();
    storageInspector.startMonitoring();
  });

  afterEach(async () => {
    const networkReport = networkMonitor.stopMonitoring();
    const memoryReport = memoryInspector.stopInspection();
    const storageReport = storageInspector.stopMonitoring();

    // Analyze reports for privacy violations
    console.log('Network Activity Report:', networkReport);
    console.log('Memory Inspection Report:', memoryReport);
    console.log('Storage Activity Report:', storageReport);

    vi.clearAllMocks();
    
    // Perform secure cleanup
    await pdfEngine.performSecureCleanup();
  });

  describe('Zero Server Communication Verification', () => {
    it('should not transmit any file content to external servers during client-side processing', async () => {
      const suspiciousRequests: Array<{
        url: string;
        method: string;
        headers: Record<string, string>;
        bodySize: number;
        containsSensitiveData: boolean;
      }> = [];

      networkMonitor.onRequest = (request) => {
        // Check if request contains file data or sensitive information
        const bodySize = request.bodySize || 0;
        const containsSensitive = sensitiveDocument.sensitivePatterns?.some(pattern => 
          request.url.includes(pattern) || 
          request.headers['content-type']?.includes('pdf') ||
          bodySize > 1024 // Suspicious if sending large data
        );

        if (containsSensitive || bodySize > 0) {
          suspiciousRequests.push({
            url: request.url,
            method: request.method,
            headers: request.headers,
            bodySize: bodySize,
            containsSensitiveData: containsSensitive
          });
        }
      };

      vi.spyOn(pdfEngine, 'processDocument').mockImplementation(async (doc, operation) => {
        // Simulate client-side processing without any network calls
        await new Promise(resolve => setTimeout(resolve, 2000)); // Processing time
        
        return {
          success: true,
          result: new ArrayBuffer(doc.buffer.byteLength * 0.8), // Compressed
          processingLocation: 'client',
          networkRequestsMade: 0,
          dataTransmitted: 0
        };
      });

      const result = await pdfEngine.processDocument(sensitiveDocument, {
        type: 'compression',
        options: { enableImageCompression: true }
      });

      // Verify no suspicious network activity
      expect(suspiciousRequests).toHaveLength(0);
      expect(result.networkRequestsMade).toBe(0);
      expect(result.dataTransmitted).toBe(0);

      // Verify processing was truly client-side
      expect(result.processingLocation).toBe('client');
    });

    it('should not leak sensitive data through error reporting or analytics', async () => {
      const analyticsRequests: any[] = [];
      const errorReportRequests: any[] = [];

      networkMonitor.onRequest = (request) => {
        if (request.url.includes('analytics') || request.url.includes('tracking')) {
          analyticsRequests.push(request);
        }
        if (request.url.includes('error') || request.url.includes('logging')) {
          errorReportRequests.push(request);
        }
      };

      // Trigger an error scenario
      vi.spyOn(pdfEngine, 'processDocument').mockRejectedValue(
        new Error('Memory allocation failed during text extraction')
      );

      try {
        await pdfEngine.processDocument(sensitiveDocument, {
          type: 'text-extraction'
        });
      } catch (error) {
        // Error is expected
      }

      // Verify no sensitive data in analytics/error reports
      for (const request of [...analyticsRequests, ...errorReportRequests]) {
        for (const sensitivePattern of sensitiveDocument.sensitivePatterns || []) {
          expect(request.body).not.toContain(sensitivePattern);
          expect(request.url).not.toContain(sensitivePattern);
        }
      }
    });

    it('should handle server-side processing with explicit user consent and data minimization', async () => {
      const serverRequests: Array<{
        url: string;
        dataSize: number;
        encryptedPayload: boolean;
        userConsent: boolean;
        dataMinimized: boolean;
      }> = [];

      networkMonitor.onRequest = (request) => {
        if (request.url.includes('api/process')) {
          serverRequests.push({
            url: request.url,
            dataSize: request.bodySize || 0,
            encryptedPayload: request.headers['content-encoding'] === 'encrypted',
            userConsent: request.headers['x-user-consent'] === 'true',
            dataMinimized: request.headers['x-data-minimized'] === 'true'
          });
        }
      };

      vi.spyOn(pdfEngine, 'processWithServerFallback').mockImplementation(async (doc, operation, userConsent) => {
        if (!userConsent) {
          throw new Error('Server processing requires explicit user consent');
        }

        // Simulate server processing with data minimization
        const minimizedData = new ArrayBuffer(Math.min(doc.buffer.byteLength, 100 * 1024)); // Max 100KB
        
        // Mock server request
        networkMonitor.simulateRequest({
          url: 'https://api.pdfengine.com/process',
          method: 'POST',
          headers: {
            'content-type': 'application/octet-stream',
            'content-encoding': 'encrypted',
            'x-user-consent': 'true',
            'x-data-minimized': 'true'
          },
          bodySize: minimizedData.byteLength
        });

        return {
          success: true,
          result: new ArrayBuffer(doc.buffer.byteLength),
          processingLocation: 'server',
          userConsentGiven: true,
          dataMinimized: true,
          encryptedTransmission: true
        };
      });

      // Test server processing with consent
      const resultWithConsent = await pdfEngine.processWithServerFallback(
        sensitiveDocument, 
        { type: 'advanced-ocr' }, 
        true // User consent
      );

      expect(resultWithConsent.userConsentGiven).toBe(true);
      expect(resultWithConsent.dataMinimized).toBe(true);
      expect(resultWithConsent.encryptedTransmission).toBe(true);

      // Verify server requests meet privacy requirements
      expect(serverRequests).toHaveLength(1);
      const serverRequest = serverRequests[0];
      expect(serverRequest.userConsent).toBe(true);
      expect(serverRequest.dataMinimized).toBe(true);
      expect(serverRequest.encryptedPayload).toBe(true);
      expect(serverRequest.dataSize).toBeLessThanOrEqual(100 * 1024); // Data minimization

      // Test rejection without consent
      await expect(
        pdfEngine.processWithServerFallback(sensitiveDocument, { type: 'advanced-ocr' }, false)
      ).rejects.toThrow('Server processing requires explicit user consent');
    });
  });

  describe('Memory Security and Data Cleanup', () => {
    it('should securely wipe sensitive data from memory after processing', async () => {
      const memorySnapshots: Array<{
        timestamp: number;
        sensitiveDataFound: string[];
        totalSensitiveBytes: number;
      }> = [];

      memoryInspector.onMemoryScan = (scanResult) => {
        const foundSensitive = sensitiveDocument.sensitivePatterns?.filter(pattern =>
          scanResult.memoryContent.includes(pattern)
        ) || [];

        memorySnapshots.push({
          timestamp: Date.now(),
          sensitiveDataFound: foundSensitive,
          totalSensitiveBytes: scanResult.totalSensitiveBytes
        });
      };

      vi.spyOn(pdfEngine, 'processSecurely').mockImplementation(async (doc) => {
        // Simulate processing that temporarily stores sensitive data in memory
        const tempBuffer = new ArrayBuffer(doc.buffer.byteLength);
        const tempView = new Uint8Array(tempBuffer);
        tempView.set(new Uint8Array(doc.buffer));

        // Process for 3 seconds
        await new Promise(resolve => setTimeout(resolve, 3000));

        // Perform secure cleanup
        await pdfEngine.performSecureMemoryWipe(tempBuffer);
        
        return {
          success: true,
          result: new ArrayBuffer(doc.buffer.byteLength),
          secureCleanupPerformed: true
        };
      });

      const result = await pdfEngine.processSecurely(sensitiveDocument);

      expect(result.secureCleanupPerformed).toBe(true);

      // Check that sensitive data was eventually wiped from memory
      const finalSnapshot = memorySnapshots[memorySnapshots.length - 1];
      expect(finalSnapshot.sensitiveDataFound).toHaveLength(0);
      expect(finalSnapshot.totalSensitiveBytes).toBe(0);
    });

    it('should prevent memory dumps from containing sensitive document data', async () => {
      const memoryDumpData: ArrayBuffer[] = [];

      vi.spyOn(pdfEngine, 'captureMemoryDump').mockImplementation(() => {
        // Simulate memory dump capture
        const dumpSize = 10 * 1024 * 1024; // 10MB dump
        const dump = new ArrayBuffer(dumpSize);
        memoryDumpData.push(dump);
        return dump;
      });

      // Process sensitive document
      await pdfEngine.processDocument(sensitiveDocument, {
        type: 'text-extraction'
      });

      // Capture memory dump after processing
      const memoryDump = pdfEngine.captureMemoryDump();

      // Analyze dump for sensitive data
      const dumpContent = new Uint8Array(memoryDump);
      const dumpText = new TextDecoder().decode(dumpContent);

      for (const sensitivePattern of sensitiveDocument.sensitivePatterns || []) {
        expect(dumpText).not.toContain(sensitivePattern);
      }
    });

    it('should implement secure random memory overwriting for cleanup', async () => {
      const cleanupOperations: Array<{
        memoryRegion: ArrayBuffer;
        overwritePasses: number;
        randomPatternUsed: boolean;
        verificationPassed: boolean;
      }> = [];

      vi.spyOn(pdfEngine, 'performSecureMemoryWipe').mockImplementation(async (memoryRegion) => {
        const passes = 3; // DoD 5220.22-M standard
        let verificationPassed = true;

        for (let pass = 0; pass < passes; pass++) {
          // Simulate random overwrite
          const randomPattern = new Uint8Array(memoryRegion.byteLength);
          crypto.getRandomValues(randomPattern);
          
          // Overwrite memory region
          new Uint8Array(memoryRegion).set(randomPattern);
        }

        // Final verification pass
        const finalView = new Uint8Array(memoryRegion);
        const hasNullBytes = finalView.some(byte => byte === 0);
        const hasPatternedData = finalView.some((byte, index, array) => 
          index > 0 && byte === array[index - 1] && array.length > 10
        );

        verificationPassed = !hasNullBytes && !hasPatternedData;

        cleanupOperations.push({
          memoryRegion,
          overwritePasses: passes,
          randomPatternUsed: true,
          verificationPassed
        });

        return verificationPassed;
      });

      const testBuffer = new ArrayBuffer(1024 * 1024); // 1MB test buffer
      // Fill with sensitive-like data
      const testView = new Uint8Array(testBuffer);
      testView.fill(0x41); // Fill with 'A' characters

      const cleanupSuccess = await pdfEngine.performSecureMemoryWipe(testBuffer);

      expect(cleanupSuccess).toBe(true);
      expect(cleanupOperations).toHaveLength(1);
      
      const operation = cleanupOperations[0];
      expect(operation.overwritePasses).toBe(3);
      expect(operation.randomPatternUsed).toBe(true);
      expect(operation.verificationPassed).toBe(true);
    });
  });

  describe('Local Storage Privacy Compliance', () => {
    it('should not persist sensitive document data in local storage', async () => {
      const storageEntries: Array<{
        storageType: string;
        key: string;
        value: any;
        containsSensitiveData: boolean;
      }> = [];

      storageInspector.onStorageWrite = (entry) => {
        const valueStr = typeof entry.value === 'string' ? entry.value : JSON.stringify(entry.value);
        const containsSensitive = sensitiveDocument.sensitivePatterns?.some(pattern =>
          valueStr.includes(pattern) || entry.key.includes(pattern)
        ) || false;

        storageEntries.push({
          storageType: entry.storageType,
          key: entry.key,
          value: entry.value,
          containsSensitiveData: containsSensitive
        });
      };

      vi.spyOn(pdfEngine, 'processWithCaching').mockImplementation(async (doc, operation) => {
        // Simulate processing that might cache data
        const cacheKey = `pdf_process_${doc.id}`;
        const cacheData = {
          documentId: doc.id,
          operation: operation.type,
          processedAt: Date.now(),
          // Intentionally NOT including sensitive document content
          metadata: {
            pageCount: doc.pageCount,
            fileSize: doc.buffer.byteLength
          }
        };

        // Simulate storage write
        storageInspector.simulateStorageWrite({
          storageType: 'localStorage',
          key: cacheKey,
          value: cacheData
        });

        return {
          success: true,
          result: new ArrayBuffer(doc.buffer.byteLength),
          cachedData: cacheData
        };
      });

      await pdfEngine.processWithCaching(sensitiveDocument, {
        type: 'compression'
      });

      // Verify no sensitive data was stored
      const sensitiveEntries = storageEntries.filter(entry => entry.containsSensitiveData);
      expect(sensitiveEntries).toHaveLength(0);

      // Verify only safe metadata was stored
      const cacheEntries = storageEntries.filter(entry => entry.key.startsWith('pdf_process_'));
      expect(cacheEntries).toHaveLength(1);
      
      const cacheEntry = cacheEntries[0];
      expect(cacheEntry.value).not.toHaveProperty('sensitivePatterns');
      expect(cacheEntry.value.metadata).toHaveProperty('pageCount');
      expect(cacheEntry.value.metadata).toHaveProperty('fileSize');
    });

    it('should implement automatic cleanup of temporary storage on session end', async () => {
      const cleanupOperations: Array<{
        storageType: string;
        keysDeleted: string[];
        totalEntriesCleared: number;
      }> = [];

      vi.spyOn(pdfEngine, 'performStorageCleanup').mockImplementation(async () => {
        const localStorage_keys = ['pdf_process_temp_1', 'pdf_process_temp_2'];
        const sessionStorage_keys = ['pdf_session_data'];
        const indexedDB_keys = ['pdf_cache_db'];

        // Simulate cleanup operations
        for (const storageType of ['localStorage', 'sessionStorage', 'indexedDB']) {
          let keysToDelete: string[] = [];
          
          switch (storageType) {
            case 'localStorage':
              keysToDelete = localStorage_keys;
              break;
            case 'sessionStorage':
              keysToDelete = sessionStorage_keys;
              break;
            case 'indexedDB':
              keysToDelete = indexedDB_keys;
              break;
          }

          cleanupOperations.push({
            storageType,
            keysDeleted: keysToDelete,
            totalEntriesCleared: keysToDelete.length
          });
        }

        return {
          success: true,
          totalItemsCleared: cleanupOperations.reduce((sum, op) => sum + op.totalEntriesCleared, 0)
        };
      });

      // Simulate session end
      const cleanupResult = await pdfEngine.performStorageCleanup();

      expect(cleanupResult.success).toBe(true);
      expect(cleanupResult.totalItemsCleared).toBeGreaterThan(0);
      expect(cleanupOperations).toHaveLength(3); // localStorage, sessionStorage, indexedDB
    });

    it('should respect user privacy preferences for data retention', async () => {
      const privacySettings = {
        allowCaching: false,
        allowAnalytics: false,
        clearDataOnExit: true,
        minimizeDataCollection: true
      };

      const dataCollectionAttempts: Array<{
        dataType: string;
        blocked: boolean;
        reason: string;
      }> = [];

      vi.spyOn(pdfEngine, 'setPrivacySettings').mockImplementation((settings) => {
        pdfEngine.privacySettings = settings;
        return { success: true, settingsApplied: settings };
      });

      vi.spyOn(pdfEngine, 'collectAnalyticsData').mockImplementation((dataType, data) => {
        const blocked = !pdfEngine.privacySettings?.allowAnalytics;
        
        dataCollectionAttempts.push({
          dataType,
          blocked,
          reason: blocked ? 'User privacy settings' : 'Allowed by user'
        });

        if (blocked) {
          return { success: false, reason: 'Analytics disabled by user privacy settings' };
        }

        return { success: true, dataCollected: data };
      });

      // Apply privacy settings
      const settingsResult = await pdfEngine.setPrivacySettings(privacySettings);
      expect(settingsResult.success).toBe(true);

      // Attempt to collect analytics data
      const analyticsResult = pdfEngine.collectAnalyticsData('document_processed', {
        documentType: 'pdf',
        processingTime: 2500
      });

      expect(analyticsResult.success).toBe(false);
      expect(dataCollectionAttempts).toHaveLength(1);
      expect(dataCollectionAttempts[0].blocked).toBe(true);
      expect(dataCollectionAttempts[0].reason).toBe('User privacy settings');
    });
  });

  describe('GDPR and CCPA Compliance', () => {
    it('should provide transparent data processing information', async () => {
      const dataProcessingInfo = {
        dataTypesCollected: [] as string[],
        processingPurposes: [] as string[],
        dataRetentionPeriod: '',
        thirdPartySharing: false,
        userRights: [] as string[]
      };

      vi.spyOn(pdfEngine, 'getDataProcessingInfo').mockImplementation(() => {
        return {
          dataTypesCollected: [
            'Document metadata (title, author, creation date)',
            'Processing preferences',
            'Performance metrics (anonymous)'
          ],
          processingPurposes: [
            'Document processing and editing',
            'Performance optimization',
            'Error reporting (anonymous)'
          ],
          dataRetentionPeriod: 'Session only - no persistent storage of document content',
          thirdPartySharing: false,
          userRights: [
            'Right to access data',
            'Right to delete data',
            'Right to data portability',
            'Right to object to processing'
          ],
          legalBasis: 'Legitimate interest for document processing functionality',
          contactInfo: '<EMAIL>'
        };
      });

      const info = pdfEngine.getDataProcessingInfo();

      expect(info.dataTypesCollected).not.toContain('Document content');
      expect(info.dataTypesCollected).not.toContain('Personal identifiable information');
      expect(info.thirdPartySharing).toBe(false);
      expect(info.dataRetentionPeriod).toContain('Session only');
      expect(info.userRights).toContain('Right to delete data');
    });

    it('should support user data deletion requests', async () => {
      const deletionOperations: Array<{
        dataType: string;
        deleted: boolean;
        verificationPassed: boolean;
      }> = [];

      vi.spyOn(pdfEngine, 'deleteUserData').mockImplementation(async (userId, dataTypes) => {
        for (const dataType of dataTypes) {
          // Simulate deletion operations
          let deleted = false;
          let verificationPassed = false;

          switch (dataType) {
            case 'processing_cache':
              // Clear processing cache
              deleted = true;
              verificationPassed = true;
              break;
            case 'preferences':
              // Clear user preferences
              deleted = true;
              verificationPassed = true;
              break;
            case 'session_data':
              // Clear session data
              deleted = true;
              verificationPassed = true;
              break;
            default:
              deleted = false;
              verificationPassed = false;
          }

          deletionOperations.push({
            dataType,
            deleted,
            verificationPassed
          });
        }

        return {
          success: true,
          deletionOperations: deletionOperations,
          verificationReport: deletionOperations.every(op => op.verificationPassed)
        };
      });

      const deletionResult = await pdfEngine.deleteUserData('user123', [
        'processing_cache',
        'preferences', 
        'session_data'
      ]);

      expect(deletionResult.success).toBe(true);
      expect(deletionResult.verificationReport).toBe(true);
      expect(deletionOperations).toHaveLength(3);
      expect(deletionOperations.every(op => op.deleted)).toBe(true);
    });

    it('should provide data portability for user preferences and metadata', async () => {
      const userDataExport = {
        exportFormat: 'json',
        dataTypes: [] as string[],
        exportedData: {} as Record<string, any>
      };

      vi.spyOn(pdfEngine, 'exportUserData').mockImplementation(async (userId, format) => {
        const exportData = {
          userId: userId,
          exportDate: new Date().toISOString(),
          format: format,
          data: {
            preferences: {
              defaultCompression: 'medium',
              autoSave: true,
              privacyMode: true
            },
            processingHistory: [
              {
                date: '2024-01-15T10:30:00Z',
                operation: 'compression',
                success: true,
                // Note: No document content, only metadata
                fileSize: 2048000,
                processingTime: 3500
              }
            ]
            // Explicitly NOT including sensitive document content
          }
        };

        return {
          success: true,
          exportFormat: format,
          dataSize: JSON.stringify(exportData).length,
          exportData: exportData,
          containsSensitiveContent: false
        };
      });

      const exportResult = await pdfEngine.exportUserData('user123', 'json');

      expect(exportResult.success).toBe(true);
      expect(exportResult.containsSensitiveContent).toBe(false);
      expect(exportResult.exportData.data).toHaveProperty('preferences');
      expect(exportResult.exportData.data).not.toHaveProperty('documentContent');
      expect(exportResult.exportData.data).not.toHaveProperty('sensitivePatterns');
    });
  });

  describe('Client-Side Encryption for Temporary Data', () => {
    it('should encrypt temporary document data in memory and storage', async () => {
      const encryptionOperations: Array<{
        dataType: string;
        encryptionAlgorithm: string;
        keyLength: number;
        encrypted: boolean;
      }> = [];

      vi.spyOn(pdfEngine, 'processWithEncryption').mockImplementation(async (doc, operation) => {
        // Simulate client-side encryption for temporary data
        const tempData = {
          documentId: doc.id,
          processingState: 'in-progress',
          temporaryResults: new ArrayBuffer(1024)
        };

        // Encrypt sensitive temporary data
        const encryptionKey = await crypto.subtle.generateKey(
          { name: 'AES-GCM', length: 256 },
          false,
          ['encrypt', 'decrypt']
        );

        const iv = crypto.getRandomValues(new Uint8Array(12));
        const encodedData = new TextEncoder().encode(JSON.stringify(tempData));
        
        const encryptedData = await crypto.subtle.encrypt(
          { name: 'AES-GCM', iv: iv },
          encryptionKey,
          encodedData
        );

        encryptionOperations.push({
          dataType: 'temporary_processing_data',
          encryptionAlgorithm: 'AES-256-GCM',
          keyLength: 256,
          encrypted: true
        });

        return {
          success: true,
          result: new ArrayBuffer(doc.buffer.byteLength),
          encryptionApplied: true,
          encryptedTempData: encryptedData
        };
      });

      const result = await pdfEngine.processWithEncryption(sensitiveDocument, {
        type: 'text-extraction'
      });

      expect(result.encryptionApplied).toBe(true);
      expect(encryptionOperations).toHaveLength(1);
      expect(encryptionOperations[0].encryptionAlgorithm).toBe('AES-256-GCM');
      expect(encryptionOperations[0].keyLength).toBe(256);
      expect(encryptionOperations[0].encrypted).toBe(true);
    });

    it('should automatically decrypt and wipe encryption keys after processing', async () => {
      const keyManagementOperations: Array<{
        operation: string;
        keyId: string;
        success: boolean;
        securelyWiped: boolean;
      }> = [];

      vi.spyOn(pdfEngine, 'processWithKeyManagement').mockImplementation(async (doc, operation) => {
        const keyId = 'temp-key-' + Date.now();

        // Generate temporary encryption key
        const encryptionKey = await crypto.subtle.generateKey(
          { name: 'AES-GCM', length: 256 },
          false,
          ['encrypt', 'decrypt']
        );

        keyManagementOperations.push({
          operation: 'key_generation',
          keyId: keyId,
          success: true,
          securelyWiped: false
        });

        // Use key for processing
        await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate processing

        // Securely wipe key after use
        // Note: In real implementation, this would overwrite key material in memory
        keyManagementOperations.push({
          operation: 'key_destruction',
          keyId: keyId,
          success: true,
          securelyWiped: true
        });

        return {
          success: true,
          result: new ArrayBuffer(doc.buffer.byteLength),
          keyManagementCompleted: true,
          temporaryKeysWiped: true
        };
      });

      const result = await pdfEngine.processWithKeyManagement(sensitiveDocument, {
        type: 'compression'
      });

      expect(result.keyManagementCompleted).toBe(true);
      expect(result.temporaryKeysWiped).toBe(true);
      expect(keyManagementOperations).toHaveLength(2);

      const keyGeneration = keyManagementOperations.find(op => op.operation === 'key_generation');
      const keyDestruction = keyManagementOperations.find(op => op.operation === 'key_destruction');

      expect(keyGeneration?.success).toBe(true);
      expect(keyDestruction?.success).toBe(true);
      expect(keyDestruction?.securelyWiped).toBe(true);
    });
  });
});
import { describe, it, expect, beforeAll, afterAll, beforeEach, afterEach, vi } from 'vitest';
import { MemoryManager } from '../../src/wasm/mupdf-wrapper';
import type { PDFDocument, MemoryPool, CacheEntry } from '../../src/types/processing';

describe('Memory Management System', () => {
  let memoryManager: MemoryManager;
  let mockDocument: PDFDocument;

  beforeAll(() => {
    memoryManager = new MemoryManager({
      initialSize: 64 * 1024 * 1024, // 64MB
      maxSize: 2 * 1024 * 1024 * 1024, // 2GB
      chunkSize: 16 * 1024 * 1024, // 16MB
      maxDocuments: 5,
      maxPages: 20,
      pageTTL: 5 * 60 * 1000 // 5 minutes
    });
  });

  afterAll(() => {
    memoryManager.cleanup();
  });

  beforeEach(() => {
    mockDocument = {
      id: 'memory-test-doc',
      buffer: new ArrayBuffer(10 * 1024 * 1024), // 10MB
      pageCount: 10,
      metadata: {
        title: 'Memory Test Document',
        author: 'Test Author',
        subject: 'Memory Management',
        keywords: 'memory, test',
        creator: 'Test Creator',
        producer: 'Test Producer',
        creationDate: new Date(),
        modificationDate: new Date()
      },
      encrypted: false,
      permissions: {
        canPrint: true,
        canModify: true,
        canCopy: true,
        canAnnotate: true
      }
    };
  });

  afterEach(() => {
    memoryManager.clearCache();
    vi.clearAllMocks();
  });

  describe('Memory Pool Management', () => {
    it('should initialize memory pool with correct parameters', () => {
      const pool = memoryManager.getMemoryPool();
      
      expect(pool.initialSize).toBe(64 * 1024 * 1024);
      expect(pool.maxSize).toBe(2 * 1024 * 1024 * 1024);
      expect(pool.chunkSize).toBe(16 * 1024 * 1024);
      expect(pool.currentSize).toBeLessThanOrEqual(pool.maxSize);
    });

    it('should allocate memory chunks efficiently', () => {
      const chunkSize = 16 * 1024 * 1024;
      const chunk = memoryManager.allocateChunk(chunkSize);
      
      expect(chunk).toBeDefined();
      expect(chunk.size).toBe(chunkSize);
      expect(chunk.address).toBeTypeOf('number');
      expect(chunk.inUse).toBe(true);
    });

    it('should deallocate memory chunks properly', () => {
      const chunk = memoryManager.allocateChunk(16 * 1024 * 1024);
      const success = memoryManager.deallocateChunk(chunk.address);
      
      expect(success).toBe(true);
      expect(chunk.inUse).toBe(false);
    });

    it('should expand memory pool when needed', async () => {
      const initialPool = memoryManager.getMemoryPool();
      const initialSize = initialPool.currentSize;
      
      // Allocate large amount to trigger expansion
      const largeChunks = Array.from({ length: 10 }, () => 
        memoryManager.allocateChunk(32 * 1024 * 1024)
      );
      
      const expandedPool = memoryManager.getMemoryPool();
      expect(expandedPool.currentSize).toBeGreaterThan(initialSize);
      
      // Cleanup
      largeChunks.forEach(chunk => memoryManager.deallocateChunk(chunk.address));
    });

    it('should respect maximum memory limit', () => {
      const maxSize = memoryManager.getMemoryPool().maxSize;
      const oversizedChunk = maxSize + 1024;
      
      expect(() => {
        memoryManager.allocateChunk(oversizedChunk);
      }).toThrow('Memory allocation exceeds maximum pool size');
    });

    it('should handle memory fragmentation efficiently', () => {
      // Allocate multiple small chunks
      const smallChunks = Array.from({ length: 20 }, (_, i) => 
        memoryManager.allocateChunk(1024 * 1024) // 1MB each
      );
      
      // Deallocate every other chunk to create fragmentation
      smallChunks.forEach((chunk, index) => {
        if (index % 2 === 0) {
          memoryManager.deallocateChunk(chunk.address);
        }
      });
      
      // Should be able to allocate in fragmented space
      const newChunk = memoryManager.allocateChunk(512 * 1024); // 512KB
      expect(newChunk).toBeDefined();
      
      // Cleanup
      smallChunks.forEach(chunk => {
        if (chunk.inUse) {
          memoryManager.deallocateChunk(chunk.address);
        }
      });
      memoryManager.deallocateChunk(newChunk.address);
    });
  });

  describe('LRU Cache Implementation', () => {
    it('should cache documents with LRU policy', () => {
      memoryManager.cacheDocument(mockDocument);
      
      const cached = memoryManager.getCachedDocument(mockDocument.id);
      expect(cached).toBeDefined();
      expect(cached?.id).toBe(mockDocument.id);
    });

    it('should evict least recently used documents when cache is full', () => {
      // Fill cache to capacity
      const documents = Array.from({ length: 6 }, (_, i) => ({
        ...mockDocument,
        id: `doc-${i}`,
        buffer: new ArrayBuffer(5 * 1024 * 1024) // 5MB each
      }));
      
      documents.forEach(doc => memoryManager.cacheDocument(doc));
      
      // First document should be evicted
      const firstDoc = memoryManager.getCachedDocument('doc-0');
      expect(firstDoc).toBeNull();
      
      // Last document should still be cached
      const lastDoc = memoryManager.getCachedDocument('doc-5');
      expect(lastDoc).toBeDefined();
    });

    it('should update access time when document is retrieved', () => {
      memoryManager.cacheDocument(mockDocument);
      
      const beforeAccess = Date.now();
      const cached1 = memoryManager.getCachedDocument(mockDocument.id);
      const afterAccess = Date.now();
      
      expect(cached1?.lastAccessed).toBeGreaterThanOrEqual(beforeAccess);
      expect(cached1?.lastAccessed).toBeLessThanOrEqual(afterAccess);
    });

    it('should cache rendered pages with TTL', () => {
      const pageData = new ArrayBuffer(2 * 1024 * 1024); // 2MB page
      const pageKey = `${mockDocument.id}-page-1`;
      
      memoryManager.cacheRenderedPage(pageKey, pageData);
      
      const cachedPage = memoryManager.getCachedPage(pageKey);
      expect(cachedPage).toBeDefined();
      expect(cachedPage?.byteLength).toBe(2 * 1024 * 1024);
    });

    it('should expire cached pages after TTL', async () => {
      const pageData = new ArrayBuffer(1024 * 1024);
      const pageKey = `${mockDocument.id}-page-expired`;
      
      // Mock short TTL for testing
      const originalTTL = memoryManager.pageTTL;
      memoryManager.pageTTL = 100; // 100ms
      
      memoryManager.cacheRenderedPage(pageKey, pageData);
      
      // Wait for expiration
      await new Promise(resolve => setTimeout(resolve, 150));
      
      const expiredPage = memoryManager.getCachedPage(pageKey);
      expect(expiredPage).toBeNull();
      
      // Restore original TTL
      memoryManager.pageTTL = originalTTL;
    });

    it('should respect maximum page cache limit', () => {
      const maxPages = 20;
      const pages = Array.from({ length: maxPages + 5 }, (_, i) => ({
        key: `page-${i}`,
        data: new ArrayBuffer(512 * 1024) // 512KB each
      }));
      
      pages.forEach(page => {
        memoryManager.cacheRenderedPage(page.key, page.data);
      });
      
      // Should only have maxPages cached
      const cacheStats = memoryManager.getCacheStats();
      expect(cacheStats.pageCount).toBeLessThanOrEqual(maxPages);
      
      // Earliest pages should be evicted
      const firstPage = memoryManager.getCachedPage('page-0');
      expect(firstPage).toBeNull();
    });
  });

  describe('Streaming Processing for Large Documents', () => {
    it('should calculate optimal batch size based on document size', () => {
      const largeDoc = {
        ...mockDocument,
        buffer: new ArrayBuffer(500 * 1024 * 1024), // 500MB
        pageCount: 1000
      };
      
      const batchSize = memoryManager.calculateOptimalBatchSize(largeDoc);
      expect(batchSize).toBeGreaterThan(0);
      expect(batchSize).toBeLessThanOrEqual(50); // Reasonable batch size for large docs
    });

    it('should process document in batches without memory overflow', async () => {
      const largeDoc = {
        ...mockDocument,
        buffer: new ArrayBuffer(100 * 1024 * 1024), // 100MB
        pageCount: 200
      };
      
      const batchSize = memoryManager.calculateOptimalBatchSize(largeDoc);
      const batches = Math.ceil(largeDoc.pageCount / batchSize);
      
      let processedPages = 0;
      const initialMemory = memoryManager.getCurrentMemoryUsage();
      
      for (let i = 0; i < batches; i++) {
        const startPage = i * batchSize;
        const endPage = Math.min(startPage + batchSize, largeDoc.pageCount);
        
        // Simulate batch processing
        await memoryManager.processBatch(largeDoc, startPage, endPage);
        processedPages += (endPage - startPage);
        
        // Memory should not grow unbounded
        const currentMemory = memoryManager.getCurrentMemoryUsage();
        expect(currentMemory).toBeLessThan(initialMemory * 3);
        
        // Perform garbage collection between batches
        await memoryManager.performGarbageCollection();
      }
      
      expect(processedPages).toBe(largeDoc.pageCount);
    });

    it('should handle streaming with progress tracking', async () => {
      const doc = {
        ...mockDocument,
        pageCount: 50
      };
      
      const progressUpdates: number[] = [];
      const onProgress = (progress: number) => {
        progressUpdates.push(progress);
      };
      
      await memoryManager.processDocumentStreaming(doc, onProgress);
      
      expect(progressUpdates.length).toBeGreaterThan(0);
      expect(progressUpdates[0]).toBe(0);
      expect(progressUpdates[progressUpdates.length - 1]).toBe(100);
      
      // Progress should be monotonically increasing
      for (let i = 1; i < progressUpdates.length; i++) {
        expect(progressUpdates[i]).toBeGreaterThanOrEqual(progressUpdates[i - 1]);
      }
    });
  });

  describe('Garbage Collection Optimization', () => {
    it('should trigger garbage collection at optimal intervals', async () => {
      const gcSpy = vi.spyOn(memoryManager, 'performGarbageCollection');
      
      // Simulate memory pressure
      const chunks = Array.from({ length: 10 }, () => 
        memoryManager.allocateChunk(20 * 1024 * 1024)
      );
      
      // Should trigger GC
      expect(gcSpy).toHaveBeenCalled();
      
      // Cleanup
      chunks.forEach(chunk => memoryManager.deallocateChunk(chunk.address));
    });

    it('should free unreferenced memory during GC', async () => {
      const initialMemory = memoryManager.getCurrentMemoryUsage();
      
      // Create temporary allocations
      const tempChunks = Array.from({ length: 5 }, () => 
        memoryManager.allocateChunk(10 * 1024 * 1024)
      );
      
      const peakMemory = memoryManager.getCurrentMemoryUsage();
      expect(peakMemory).toBeGreaterThan(initialMemory);
      
      // Mark chunks as unreferenced
      tempChunks.forEach(chunk => {
        memoryManager.markUnreferenced(chunk.address);
      });
      
      // Perform garbage collection
      await memoryManager.performGarbageCollection();
      
      const afterGCMemory = memoryManager.getCurrentMemoryUsage();
      expect(afterGCMemory).toBeLessThan(peakMemory);
    });

    it('should preserve referenced memory during GC', async () => {
      const referencedChunk = memoryManager.allocateChunk(5 * 1024 * 1024);
      memoryManager.markReferenced(referencedChunk.address);
      
      // Create unreferenced chunks
      const unreferencedChunks = Array.from({ length: 3 }, () => {
        const chunk = memoryManager.allocateChunk(5 * 1024 * 1024);
        memoryManager.markUnreferenced(chunk.address);
        return chunk;
      });
      
      await memoryManager.performGarbageCollection();
      
      // Referenced chunk should still exist
      const chunk = memoryManager.getChunk(referencedChunk.address);
      expect(chunk).toBeDefined();
      expect(chunk?.inUse).toBe(true);
      
      // Cleanup
      memoryManager.deallocateChunk(referencedChunk.address);
    });

    it('should optimize GC timing based on memory pressure', async () => {
      const gcSpy = vi.spyOn(memoryManager, 'performGarbageCollection');
      
      // Low memory pressure - GC should be less frequent
      memoryManager.allocateChunk(1 * 1024 * 1024);
      const lowPressureGCCount = gcSpy.mock.calls.length;
      
      gcSpy.mockClear();
      
      // High memory pressure - GC should be more frequent
      Array.from({ length: 20 }, () => 
        memoryManager.allocateChunk(10 * 1024 * 1024)
      );
      const highPressureGCCount = gcSpy.mock.calls.length;
      
      expect(highPressureGCCount).toBeGreaterThan(lowPressureGCCount);
    });
  });

  describe('SharedArrayBuffer Utilization', () => {
    it('should use SharedArrayBuffer for concurrent operations when available', () => {
      if (typeof SharedArrayBuffer !== 'undefined') {
        const sharedBuffer = memoryManager.createSharedBuffer(1024 * 1024);
        
        expect(sharedBuffer).toBeInstanceOf(SharedArrayBuffer);
        expect(sharedBuffer.byteLength).toBe(1024 * 1024);
      } else {
        // Fallback to ArrayBuffer
        const buffer = memoryManager.createSharedBuffer(1024 * 1024);
        expect(buffer).toBeInstanceOf(ArrayBuffer);
      }
    });

    it('should synchronize access to shared memory', async () => {
      if (typeof SharedArrayBuffer !== 'undefined') {
        const sharedBuffer = memoryManager.createSharedBuffer(1024);
        const view = new Int32Array(sharedBuffer);
        
        // Simulate concurrent access
        const operations = Array.from({ length: 10 }, async (_, i) => {
          memoryManager.atomicWrite(view, i, i * 10);
        });
        
        await Promise.all(operations);
        
        // Verify all writes were atomic
        for (let i = 0; i < 10; i++) {
          expect(view[i]).toBe(i * 10);
        }
      }
    });

    it('should handle SharedArrayBuffer transfer between workers', () => {
      if (typeof SharedArrayBuffer !== 'undefined') {
        const sharedBuffer = memoryManager.createSharedBuffer(2048);
        const transferrable = memoryManager.prepareForWorkerTransfer(sharedBuffer);
        
        expect(transferrable.buffer).toBe(sharedBuffer);
        expect(transferrable.transferrable).toBe(true);
      }
    });
  });

  describe('Memory Monitoring and Statistics', () => {
    it('should provide accurate memory usage statistics', () => {
      const stats = memoryManager.getMemoryStats();
      
      expect(stats).toHaveProperty('totalAllocated');
      expect(stats).toHaveProperty('totalUsed');
      expect(stats).toHaveProperty('totalFree');
      expect(stats).toHaveProperty('fragmentationRatio');
      expect(stats).toHaveProperty('gcCount');
      expect(stats).toHaveProperty('cacheHitRatio');
      
      expect(stats.totalAllocated).toBeGreaterThanOrEqual(0);
      expect(stats.totalUsed).toBeLessThanOrEqual(stats.totalAllocated);
      expect(stats.fragmentationRatio).toBeGreaterThanOrEqual(0);
      expect(stats.fragmentationRatio).toBeLessThanOrEqual(1);
    });

    it('should track cache performance metrics', () => {
      // Perform cache operations
      memoryManager.cacheDocument(mockDocument);
      memoryManager.getCachedDocument(mockDocument.id); // Hit
      memoryManager.getCachedDocument('non-existent'); // Miss
      
      const stats = memoryManager.getCacheStats();
      
      expect(stats.hitCount).toBeGreaterThan(0);
      expect(stats.missCount).toBeGreaterThan(0);
      expect(stats.hitRatio).toBeGreaterThan(0);
      expect(stats.hitRatio).toBeLessThanOrEqual(1);
    });

    it('should provide memory leak detection', async () => {
      const initialStats = memoryManager.getMemoryStats();
      
      // Perform operations that should be cleaned up
      const tempDoc = { ...mockDocument, id: 'temp-doc' };
      memoryManager.cacheDocument(tempDoc);
      memoryManager.allocateChunk(1024 * 1024);
      
      // Cleanup
      memoryManager.removeCachedDocument('temp-doc');
      await memoryManager.performGarbageCollection();
      
      const finalStats = memoryManager.getMemoryStats();
      
      // Memory usage should return to baseline
      expect(finalStats.totalUsed).toBeLessThanOrEqual(initialStats.totalUsed + 1024); // Small tolerance
    });

    it('should alert on memory pressure thresholds', () => {
      const alertSpy = vi.fn();
      memoryManager.onMemoryPressure(alertSpy);
      
      // Trigger memory pressure
      const largeChunks = Array.from({ length: 30 }, () => 
        memoryManager.allocateChunk(32 * 1024 * 1024)
      );
      
      expect(alertSpy).toHaveBeenCalled();
      
      // Cleanup
      largeChunks.forEach(chunk => memoryManager.deallocateChunk(chunk.address));
    });
  });
});
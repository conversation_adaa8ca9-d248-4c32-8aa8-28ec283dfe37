import { describe, it, expect, beforeAll, afterAll, beforeEach, afterEach, vi } from 'vitest';
import { TextEditor } from '../../src/text/text-editor';
import { MuPDFWrapper } from '../../src/wasm/mupdf-wrapper';
import type { 
  PDFDocument, 
  TextBlock, 
  TextLayout, 
  TextModification, 
  TextRange, 
  TextStyle,
  Position,
  LayoutAdjustment 
} from '../../src/types/processing';

describe('Text Editing System', () => {
  let textEditor: TextEditor;
  let muPDFWrapper: MuPDFWrapper;
  let mockDocument: PDFDocument;

  beforeAll(async () => {
    muPDFWrapper = new MuPDFWrapper();
    textEditor = new TextEditor(muPDFWrapper);
  });

  afterAll(() => {
    if (muPDFWrapper) {
      muPDFWrapper.cleanup();
    }
  });

  beforeEach(() => {
    mockDocument = {
      id: 'text-editing-doc',
      buffer: new ArrayBuffer(1024 * 1024), // 1MB
      pageCount: 5,
      metadata: {
        title: 'Text Editing Test Document',
        author: 'Test Author',
        subject: 'Text Editing',
        keywords: 'text, editing, test',
        creator: 'Test Creator',
        producer: 'Test Producer',
        creationDate: new Date(),
        modificationDate: new Date()
      },
      encrypted: false,
      permissions: {
        canPrint: true,
        canModify: true,
        canCopy: true,
        canAnnotate: true
      }
    };
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Text Layout Analysis', () => {
    it('should extract text blocks with accurate positioning', async () => {
      const mockTextBlocks: TextBlock[] = [
        {
          text: 'Chapter 1: Introduction',
          x: 72,
          y: 720,
          width: 300,
          height: 24,
          fontName: 'Arial-Bold',
          fontSize: 18,
          color: '#000000',
          rotation: 0,
          opacity: 1.0
        },
        {
          text: 'This is the first paragraph of content with multiple lines of text that demonstrates proper layout analysis.',
          x: 72,
          y: 680,
          width: 450,
          height: 36,
          fontName: 'Arial',
          fontSize: 12,
          color: '#000000',
          rotation: 0,
          opacity: 1.0
        }
      ];

      vi.spyOn(textEditor, 'extractTextBlocks').mockResolvedValue(mockTextBlocks);

      const textBlocks = await textEditor.extractTextBlocks(mockDocument, 1);

      expect(textBlocks).toHaveLength(2);
      expect(textBlocks[0].text).toBe('Chapter 1: Introduction');
      expect(textBlocks[0].fontName).toBe('Arial-Bold');
      expect(textBlocks[0].fontSize).toBe(18);
      expect(textBlocks[1].text).toContain('first paragraph');
      expect(textBlocks[1].fontName).toBe('Arial');
    });

    it('should analyze text layout with proper spacing and formatting', async () => {
      const mockLayout: TextLayout = {
        pageNum: 1,
        textBlocks: [
          {
            text: 'Sample Text Block',
            x: 100,
            y: 200,
            width: 200,
            height: 20,
            fontName: 'Times-Roman',
            fontSize: 14,
            color: '#000000',
            rotation: 0,
            opacity: 1.0
          }
        ],
        lineHeight: 1.2,
        wordSpacing: 3.0,
        charSpacing: 0.8,
        paragraphSpacing: 12.0,
        margins: {
          top: 72,
          right: 72,
          bottom: 72,
          left: 72
        },
        columns: 1,
        textFlow: 'left-to-right'
      };

      vi.spyOn(textEditor, 'analyzeTextLayout').mockResolvedValue(mockLayout);

      const layout = await textEditor.analyzeTextLayout(mockDocument, 1);

      expect(layout.pageNum).toBe(1);
      expect(layout.lineHeight).toBe(1.2);
      expect(layout.wordSpacing).toBe(3.0);
      expect(layout.charSpacing).toBe(0.8);
      expect(layout.margins.left).toBe(72);
      expect(layout.textFlow).toBe('left-to-right');
    });

    it('should detect font characteristics and styling', async () => {
      const textBlock: TextBlock = {
        text: 'Styled Text Example',
        x: 150,
        y: 300,
        width: 180,
        height: 16,
        fontName: 'Helvetica-BoldOblique',
        fontSize: 16,
        color: '#0066CC',
        rotation: 0,
        opacity: 0.9
      };

      vi.spyOn(textEditor, 'detectFontCharacteristics').mockResolvedValue({
        family: 'Helvetica',
        weight: 'bold',
        style: 'oblique',
        variant: 'normal',
        stretch: 'normal',
        isEmbedded: true,
        isSubset: false,
        encoding: 'WinAnsiEncoding'
      });

      const fontInfo = await textEditor.detectFontCharacteristics(textBlock);

      expect(fontInfo.family).toBe('Helvetica');
      expect(fontInfo.weight).toBe('bold');
      expect(fontInfo.style).toBe('oblique');
      expect(fontInfo.isEmbedded).toBe(true);
    });

    it('should identify text hierarchies and structures', async () => {
      const mockStructure = {
        headings: [
          { level: 1, text: 'Main Title', x: 72, y: 750, fontSize: 20 },
          { level: 2, text: 'Subtitle', x: 72, y: 720, fontSize: 16 },
          { level: 3, text: 'Section Header', x: 72, y: 690, fontSize: 14 }
        ],
        paragraphs: [
          { text: 'First paragraph content...', x: 72, y: 650, fontSize: 12 },
          { text: 'Second paragraph content...', x: 72, y: 620, fontSize: 12 }
        ],
        lists: [
          {
            type: 'bulleted',
            items: [
              { text: 'First item', x: 84, y: 590, fontSize: 12 },
              { text: 'Second item', x: 84, y: 570, fontSize: 12 }
            ]
          }
        ]
      };

      vi.spyOn(textEditor, 'analyzeTextStructure').mockResolvedValue(mockStructure);

      const structure = await textEditor.analyzeTextStructure(mockDocument, 1);

      expect(structure.headings).toHaveLength(3);
      expect(structure.headings[0].level).toBe(1);
      expect(structure.paragraphs).toHaveLength(2);
      expect(structure.lists[0].type).toBe('bulleted');
      expect(structure.lists[0].items).toHaveLength(2);
    });
  });

  describe('Text Insertion and Modification', () => {
    it('should insert text at specific position with font matching', async () => {
      const position: Position = { x: 200, y: 400 };
      const text = 'This is new inserted text';
      const style: TextStyle = {
        fontName: 'Arial',
        fontSize: 12,
        color: '#000000',
        bold: false,
        italic: false,
        underline: false,
        strikethrough: false
      };

      vi.spyOn(textEditor, 'insertText').mockResolvedValue();
      vi.spyOn(textEditor, 'matchExistingFont').mockResolvedValue({
        fontName: 'Arial',
        fontSize: 12,
        color: '#000000',
        bold: false,
        italic: false,
        underline: false,
        strikethrough: false
      });

      await textEditor.insertText(mockDocument, 1, position, text, style);

      expect(textEditor.insertText).toHaveBeenCalledWith(mockDocument, 1, position, text, expect.objectContaining({
        fontName: 'Arial',
        fontSize: 12
      }));
    });

    it('should modify existing text while preserving layout', async () => {
      const textRange: TextRange = {
        startChar: 0,
        endChar: 25,
        pageNum: 1,
        startX: 100,
        startY: 200,
        endX: 300,
        endY: 220
      };

      const newText = 'Modified text with new content';
      const layoutAdjustment: LayoutAdjustment = {
        preserveSpacing: true,
        adjustLineHeight: false,
        reflowParagraph: true,
        maintainJustification: true
      };

      vi.spyOn(textEditor, 'modifyText').mockResolvedValue();

      await textEditor.modifyText(mockDocument, textRange, newText, layoutAdjustment);

      expect(textEditor.modifyText).toHaveBeenCalledWith(
        mockDocument, 
        textRange, 
        newText, 
        expect.objectContaining({
          preserveSpacing: true,
          reflowParagraph: true
        })
      );
    });

    it('should delete text and adjust surrounding layout automatically', async () => {
      const textRange: TextRange = {
        startChar: 10,
        endChar: 30,
        pageNum: 1,
        startX: 150,
        startY: 300,
        endX: 250,
        endY: 320
      };

      vi.spyOn(textEditor, 'deleteText').mockResolvedValue();
      vi.spyOn(textEditor, 'adjustSurroundingLayout').mockResolvedValue();

      await textEditor.deleteText(mockDocument, textRange);

      expect(textEditor.deleteText).toHaveBeenCalledWith(mockDocument, textRange);
      expect(textEditor.adjustSurroundingLayout).toHaveBeenCalled();
    });

    it('should handle multi-line text insertion with proper wrapping', async () => {
      const position: Position = { x: 72, y: 500 };
      const multiLineText = `This is a long text that should wrap across multiple lines and maintain proper formatting and spacing throughout the entire paragraph.`;
      
      const style: TextStyle = {
        fontName: 'Times-Roman',
        fontSize: 11,
        color: '#000000',
        bold: false,
        italic: false,
        underline: false,
        strikethrough: false
      };

      const wrapConfig = {
        maxWidth: 450,
        lineHeight: 1.3,
        alignment: 'justify' as const
      };

      vi.spyOn(textEditor, 'insertTextWithWrapping').mockResolvedValue();

      await textEditor.insertTextWithWrapping(mockDocument, 1, position, multiLineText, style, wrapConfig);

      expect(textEditor.insertTextWithWrapping).toHaveBeenCalledWith(
        mockDocument, 1, position, multiLineText, style, 
        expect.objectContaining({
          maxWidth: 450,
          lineHeight: 1.3,
          alignment: 'justify'
        })
      );
    });
  });

  describe('Font Management and Style Preservation', () => {
    it('should match fonts accurately from existing content', async () => {
      const targetStyle: TextStyle = {
        fontName: 'Arial-Bold',
        fontSize: 14,
        color: '#333333',
        bold: true,
        italic: false,
        underline: false,
        strikethrough: false
      };

      const existingFonts = [
        { fontName: 'Arial', available: true },
        { fontName: 'Arial-Bold', available: true },
        { fontName: 'Times-Roman', available: true }
      ];

      vi.spyOn(textEditor, 'getAvailableFonts').mockResolvedValue(existingFonts);
      vi.spyOn(textEditor, 'matchFont').mockResolvedValue({
        fontName: 'Arial-Bold',
        fontSize: 14,
        color: '#333333',
        bold: true,
        italic: false,
        underline: false,
        strikethrough: false,
        matchScore: 1.0
      });

      const matchedStyle = await textEditor.matchFont(mockDocument, targetStyle);

      expect(matchedStyle.fontName).toBe('Arial-Bold');
      expect(matchedStyle.matchScore).toBe(1.0);
      expect(matchedStyle.bold).toBe(true);
    });

    it('should preserve text styling during modifications', async () => {
      const originalStyle: TextStyle = {
        fontName: 'Times-Italic',
        fontSize: 12,
        color: '#0066CC',
        bold: false,
        italic: true,
        underline: true,
        strikethrough: false
      };

      const modifications: TextModification[] = [
        {
          type: 'modify',
          textRange: {
            startChar: 0,
            endChar: 10,
            pageNum: 1,
            startX: 100,
            startY: 200,
            endX: 200,
            endY: 220
          },
          newText: 'Updated',
          preserveStyle: true
        }
      ];

      vi.spyOn(textEditor, 'applyModifications').mockResolvedValue();

      await textEditor.applyModifications(mockDocument, 1, modifications);

      expect(textEditor.applyModifications).toHaveBeenCalledWith(
        mockDocument, 1,
        expect.arrayContaining([
          expect.objectContaining({
            type: 'modify',
            preserveStyle: true
          })
        ])
      );
    });

    it('should handle font substitution when original fonts unavailable', async () => {
      const unavailableFont: TextStyle = {
        fontName: 'CustomProprietaryFont',
        fontSize: 14,
        color: '#000000',
        bold: false,
        italic: false,
        underline: false,
        strikethrough: false
      };

      const substitutionMap = {
        'CustomProprietaryFont': 'Arial',
        'AnotherUnavailableFont': 'Times-Roman'
      };

      vi.spyOn(textEditor, 'substituteFont').mockResolvedValue({
        fontName: 'Arial',
        fontSize: 14,
        color: '#000000',
        bold: false,
        italic: false,
        underline: false,
        strikethrough: false,
        substituted: true,
        originalFont: 'CustomProprietaryFont'
      });

      const substitutedStyle = await textEditor.substituteFont(unavailableFont, substitutionMap);

      expect(substitutedStyle.fontName).toBe('Arial');
      expect(substitutedStyle.substituted).toBe(true);
      expect(substitutedStyle.originalFont).toBe('CustomProprietaryFont');
    });

    it('should handle advanced typography features', async () => {
      const advancedText = 'Text with ligatures: fi fl ffi ffl, and kerning adjustments.';
      const typographyConfig = {
        enableLigatures: true,
        enableKerning: true,
        enableHyphenation: true,
        language: 'en-US'
      };

      vi.spyOn(textEditor, 'applyAdvancedTypography').mockResolvedValue();

      await textEditor.applyAdvancedTypography(mockDocument, 1, advancedText, typographyConfig);

      expect(textEditor.applyAdvancedTypography).toHaveBeenCalledWith(
        mockDocument, 1, advancedText,
        expect.objectContaining({
          enableLigatures: true,
          enableKerning: true,
          enableHyphenation: true
        })
      );
    });
  });

  describe('Layout Adjustment and Reflow', () => {
    it('should reflow text when content changes', async () => {
      const originalLayout: TextLayout = {
        pageNum: 1,
        textBlocks: [
          {
            text: 'Original short text',
            x: 100,
            y: 300,
            width: 150,
            height: 20,
            fontName: 'Arial',
            fontSize: 12,
            color: '#000000',
            rotation: 0,
            opacity: 1.0
          }
        ],
        lineHeight: 1.2,
        wordSpacing: 2.5,
        charSpacing: 0.5,
        paragraphSpacing: 12.0,
        margins: { top: 72, right: 72, bottom: 72, left: 72 },
        columns: 1,
        textFlow: 'left-to-right'
      };

      const newText = 'This is much longer replacement text that will require reflow and adjustment of the layout to accommodate the additional content properly.';

      vi.spyOn(textEditor, 'reflowText').mockResolvedValue({
        ...originalLayout,
        textBlocks: [
          {
            ...originalLayout.textBlocks[0],
            text: newText,
            width: 400,
            height: 48 // Multi-line height
          }
        ]
      });

      const reflowedLayout = await textEditor.reflowText(mockDocument, originalLayout, newText);

      expect(reflowedLayout.textBlocks[0].text).toBe(newText);
      expect(reflowedLayout.textBlocks[0].width).toBe(400);
      expect(reflowedLayout.textBlocks[0].height).toBe(48);
    });

    it('should maintain justification during text modifications', async () => {
      const justifiedText = 'This paragraph should maintain its justified alignment even after modifications are made to the content.';
      const alignment = 'justify';

      vi.spyOn(textEditor, 'maintainJustification').mockResolvedValue();

      await textEditor.maintainJustification(mockDocument, 1, justifiedText, alignment);

      expect(textEditor.maintainJustification).toHaveBeenCalledWith(
        mockDocument, 1, justifiedText, 'justify'
      );
    });

    it('should adjust line heights and spacing automatically', async () => {
      const textBlock: TextBlock = {
        text: 'Text requiring line height adjustment',
        x: 72,
        y: 400,
        width: 300,
        height: 60,
        fontName: 'Times-Roman',
        fontSize: 14,
        color: '#000000',
        rotation: 0,
        opacity: 1.0
      };

      const spacingConfig = {
        lineHeight: 1.5,
        paragraphSpacing: 18,
        wordSpacing: 'auto',
        letterSpacing: 0
      };

      vi.spyOn(textEditor, 'adjustSpacing').mockResolvedValue();

      await textEditor.adjustSpacing(mockDocument, 1, textBlock, spacingConfig);

      expect(textEditor.adjustSpacing).toHaveBeenCalledWith(
        mockDocument, 1, textBlock,
        expect.objectContaining({
          lineHeight: 1.5,
          paragraphSpacing: 18
        })
      );
    });

    it('should handle text that spans multiple columns', async () => {
      const multiColumnLayout: TextLayout = {
        pageNum: 1,
        textBlocks: [],
        lineHeight: 1.2,
        wordSpacing: 2.0,
        charSpacing: 0.5,
        paragraphSpacing: 12.0,
        margins: { top: 72, right: 72, bottom: 72, left: 72 },
        columns: 2,
        columnGap: 24,
        textFlow: 'left-to-right'
      };

      const longText = 'This is a very long text that should flow across multiple columns and maintain proper formatting and readability throughout the entire layout system.';

      vi.spyOn(textEditor, 'layoutMultiColumnText').mockResolvedValue();

      await textEditor.layoutMultiColumnText(mockDocument, 1, longText, multiColumnLayout);

      expect(textEditor.layoutMultiColumnText).toHaveBeenCalledWith(
        mockDocument, 1, longText,
        expect.objectContaining({
          columns: 2,
          columnGap: 24
        })
      );
    });
  });

  describe('Undo/Redo System', () => {
    it('should track text editing operations for undo/redo', async () => {
      const operation1 = {
        type: 'insert' as const,
        position: { x: 100, y: 200 },
        text: 'First insertion',
        timestamp: Date.now()
      };

      const operation2 = {
        type: 'modify' as const,
        textRange: {
          startChar: 0,
          endChar: 14,
          pageNum: 1,
          startX: 100,
          startY: 200,
          endX: 200,
          endY: 220
        },
        oldText: 'First insertion',
        newText: 'Modified text',
        timestamp: Date.now()
      };

      vi.spyOn(textEditor, 'recordOperation').mockImplementation();
      vi.spyOn(textEditor, 'canUndo').mockReturnValue(true);

      await textEditor.insertText(mockDocument, 1, operation1.position, operation1.text);
      textEditor.recordOperation(operation1);

      await textEditor.modifyText(mockDocument, operation2.textRange, operation2.newText);
      textEditor.recordOperation(operation2);

      expect(textEditor.canUndo()).toBe(true);
      expect(textEditor.recordOperation).toHaveBeenCalledTimes(2);
    });

    it('should perform undo operations correctly', async () => {
      const insertOperation = {
        type: 'insert' as const,
        position: { x: 150, y: 250 },
        text: 'Text to be undone',
        pageNum: 1
      };

      vi.spyOn(textEditor, 'undo').mockResolvedValue(true);
      vi.spyOn(textEditor, 'canUndo').mockReturnValue(true);

      // Simulate operation
      await textEditor.insertText(mockDocument, 1, insertOperation.position, insertOperation.text);
      
      // Undo should be possible
      expect(textEditor.canUndo()).toBe(true);
      
      const undoResult = await textEditor.undo();
      expect(undoResult).toBe(true);
      expect(textEditor.undo).toHaveBeenCalled();
    });

    it('should perform redo operations correctly', async () => {
      vi.spyOn(textEditor, 'redo').mockResolvedValue(true);
      vi.spyOn(textEditor, 'canRedo').mockReturnValue(true);

      // Simulate undo first
      await textEditor.undo();

      // Now redo should be possible
      expect(textEditor.canRedo()).toBe(true);
      
      const redoResult = await textEditor.redo();
      expect(redoResult).toBe(true);
      expect(textEditor.redo).toHaveBeenCalled();
    });

    it('should limit undo history size to prevent memory issues', async () => {
      const maxHistorySize = 50;
      
      // Simulate many operations
      for (let i = 0; i < 60; i++) {
        const operation = {
          type: 'insert' as const,
          position: { x: 100 + i, y: 200 + i },
          text: `Operation ${i}`,
          timestamp: Date.now()
        };
        textEditor.recordOperation(operation);
      }

      vi.spyOn(textEditor, 'getHistorySize').mockReturnValue(maxHistorySize);
      const historySize = textEditor.getHistorySize();
      
      expect(historySize).toBeLessThanOrEqual(maxHistorySize);
    });
  });

  describe('Text Selection and Manipulation', () => {
    it('should select text ranges accurately', async () => {
      const selectionStart: Position = { x: 100, y: 300 };
      const selectionEnd: Position = { x: 250, y: 320 };

      const expectedSelection = {
        startChar: 5,
        endChar: 25,
        pageNum: 1,
        startX: 100,
        startY: 300,
        endX: 250,
        endY: 320,
        selectedText: 'selected text content'
      };

      vi.spyOn(textEditor, 'selectText').mockResolvedValue(expectedSelection);

      const selection = await textEditor.selectText(mockDocument, 1, selectionStart, selectionEnd);

      expect(selection.selectedText).toBe('selected text content');
      expect(selection.startChar).toBe(5);
      expect(selection.endChar).toBe(25);
    });

    it('should handle text selection across multiple lines', async () => {
      const multiLineSelection = {
        startChar: 15,
        endChar: 85,
        pageNum: 1,
        startX: 100,
        startY: 400,
        endX: 180,
        endY: 450,
        selectedText: 'This selection spans\nacross multiple lines\nof text content',
        lineCount: 3
      };

      vi.spyOn(textEditor, 'selectMultiLineText').mockResolvedValue(multiLineSelection);

      const selection = await textEditor.selectMultiLineText(mockDocument, 1, 
        { x: 100, y: 400 }, { x: 180, y: 450 });

      expect(selection.lineCount).toBe(3);
      expect(selection.selectedText).toContain('\n');
    });

    it('should copy and paste text with formatting preservation', async () => {
      const selectedText = {
        text: 'Copied text with formatting',
        style: {
          fontName: 'Arial-Bold',
          fontSize: 14,
          color: '#0066CC',
          bold: true,
          italic: false,
          underline: true,
          strikethrough: false
        },
        formatting: {
          alignment: 'center',
          lineHeight: 1.3,
          letterSpacing: 0.5
        }
      };

      const pastePosition: Position = { x: 200, y: 500 };

      vi.spyOn(textEditor, 'copyText').mockResolvedValue(selectedText);
      vi.spyOn(textEditor, 'pasteText').mockResolvedValue();

      const copiedText = await textEditor.copyText(mockDocument, 1, {
        startChar: 10, endChar: 37, pageNum: 1, 
        startX: 150, startY: 300, endX: 350, endY: 320
      });

      await textEditor.pasteText(mockDocument, 1, pastePosition, copiedText);

      expect(copiedText.text).toBe('Copied text with formatting');
      expect(copiedText.style.bold).toBe(true);
      expect(textEditor.pasteText).toHaveBeenCalledWith(
        mockDocument, 1, pastePosition, selectedText
      );
    });
  });

  describe('Performance and Optimization', () => {
    it('should handle large text operations efficiently', async () => {
      const largeText = 'A'.repeat(10000); // 10KB of text
      const startTime = Date.now();

      vi.spyOn(textEditor, 'insertText').mockImplementation(async () => {
        // Simulate processing time
        await new Promise(resolve => setTimeout(resolve, 10));
      });

      await textEditor.insertText(mockDocument, 1, { x: 100, y: 200 }, largeText);

      const endTime = Date.now();
      const processingTime = endTime - startTime;

      // Should complete within reasonable time
      expect(processingTime).toBeLessThan(1000); // 1 second
    });

    it('should batch multiple text operations for efficiency', async () => {
      const operations: TextModification[] = [
        {
          type: 'insert',
          position: { x: 100, y: 200 },
          newText: 'First insertion'
        },
        {
          type: 'modify',
          textRange: {
            startChar: 20,
            endChar: 30,
            pageNum: 1,
            startX: 200,
            startY: 250,
            endX: 300,
            endY: 270
          },
          newText: 'Modified'
        },
        {
          type: 'delete',
          textRange: {
            startChar: 40,
            endChar: 50,
            pageNum: 1,
            startX: 150,
            startY: 300,
            endX: 200,
            endY: 320
          }
        }
      ];

      vi.spyOn(textEditor, 'batchOperations').mockResolvedValue();

      await textEditor.batchOperations(mockDocument, 1, operations);

      expect(textEditor.batchOperations).toHaveBeenCalledWith(
        mockDocument, 1, expect.arrayContaining(operations)
      );
    });

    it('should optimize font loading and caching', async () => {
      const fontName = 'Arial-Bold';
      
      vi.spyOn(textEditor, 'loadFont').mockResolvedValue(true);
      vi.spyOn(textEditor, 'isFontCached').mockReturnValue(false);

      // First load
      const loaded1 = await textEditor.loadFont(fontName);
      expect(textEditor.loadFont).toHaveBeenCalledWith(fontName);
      expect(loaded1).toBe(true);

      // Mock font as cached after first load
      vi.mocked(textEditor.isFontCached).mockReturnValue(true);
      vi.mocked(textEditor.loadFont).mockClear();

      // Second load should use cache
      await textEditor.loadFont(fontName);
      expect(textEditor.loadFont).toHaveBeenCalledTimes(0); // Should not load again
    });
  });
});
import { describe, it, expect, beforeAll, afterAll, beforeEach, afterEach, vi } from 'vitest';
import { MuPDFWrapper } from '../../src/wasm/mupdf-wrapper';
import { ProgressiveLoader } from '../../src/wasm/progressive-loader';
import type { PDFDocument, ProcessingOptions, TextModification } from '../../src/types/processing';

describe('MuPDF WASM Integration', () => {
  let muPDFWrapper: MuPDFWrapper;
  let progressiveLoader: ProgressiveLoader;
  let mockDocument: PDFDocument;

  beforeAll(async () => {
    progressiveLoader = new ProgressiveLoader();
    muPDFWrapper = new MuPDFWrapper();
  });

  afterAll(() => {
    if (muPDFWrapper) {
      muPDFWrapper.cleanup();
    }
  });

  beforeEach(() => {
    mockDocument = {
      id: 'test-doc-1',
      buffer: new ArrayBuffer(1024),
      pageCount: 3,
      metadata: {
        title: 'Test Document',
        author: 'Test Author',
        subject: 'Test Subject',
        keywords: 'test, pdf',
        creator: 'Test Creator',
        producer: 'Test Producer',
        creationDate: new Date(),
        modificationDate: new Date()
      },
      encrypted: false,
      permissions: {
        canPrint: true,
        canModify: true,
        canCopy: true,
        canAnnotate: true
      }
    };
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Progressive Loading System', () => {
    it('should detect browser capabilities correctly', async () => {
      const capabilities = await progressiveLoader.detectCapabilities();
      
      expect(capabilities).toHaveProperty('webAssembly');
      expect(capabilities).toHaveProperty('simd');
      expect(capabilities).toHaveProperty('threads');
      expect(capabilities).toHaveProperty('sharedArrayBuffer');
      expect(typeof capabilities.webAssembly).toBe('boolean');
      expect(typeof capabilities.simd).toBe('boolean');
      expect(typeof capabilities.threads).toBe('boolean');
      expect(typeof capabilities.sharedArrayBuffer).toBe('boolean');
    });

    it('should select optimal WASM build based on capabilities', async () => {
      const mockCapabilities = {
        webAssembly: true,
        simd: true,
        threads: true,
        sharedArrayBuffer: true
      };

      const buildPath = await progressiveLoader.selectOptimalBuild(mockCapabilities);
      expect(buildPath).toContain('mupdf-threads-simd.wasm');
    });

    it('should fallback to basic build when advanced features unavailable', async () => {
      const mockCapabilities = {
        webAssembly: true,
        simd: false,
        threads: false,
        sharedArrayBuffer: false
      };

      const buildPath = await progressiveLoader.selectOptimalBuild(mockCapabilities);
      expect(buildPath).toContain('mupdf-basic.wasm');
    });

    it('should initialize WASM module with proper configuration', async () => {
      vi.spyOn(progressiveLoader, 'initializeModule').mockResolvedValue(true);
      
      const result = await progressiveLoader.initialize();
      expect(result).toBe(true);
      expect(progressiveLoader.initializeModule).toHaveBeenCalled();
    });

    it('should handle WASM loading failures gracefully', async () => {
      vi.spyOn(progressiveLoader, 'initializeModule').mockRejectedValue(new Error('WASM Load Failed'));
      
      const result = await progressiveLoader.initialize();
      expect(result).toBe(false);
    });
  });

  describe('Document Management', () => {
    it('should load PDF document from ArrayBuffer', async () => {
      const buffer = new ArrayBuffer(2048);
      vi.spyOn(muPDFWrapper, 'loadDocument').mockResolvedValue(mockDocument);

      const document = await muPDFWrapper.loadDocument(buffer);
      
      expect(document).toBeDefined();
      expect(document.id).toBe('test-doc-1');
      expect(document.pageCount).toBe(3);
      expect(document.buffer).toBe(buffer);
    });

    it('should handle password-protected documents', async () => {
      const buffer = new ArrayBuffer(2048);
      const password = 'test-password';
      const encryptedDoc = { ...mockDocument, encrypted: true };
      
      vi.spyOn(muPDFWrapper, 'loadDocument').mockResolvedValue(encryptedDoc);

      const document = await muPDFWrapper.loadDocument(buffer, password);
      
      expect(document.encrypted).toBe(true);
      expect(muPDFWrapper.loadDocument).toHaveBeenCalledWith(buffer, password);
    });

    it('should save document with specified options', async () => {
      const saveOptions: ProcessingOptions = {
        compression: { enabled: true, quality: 85 },
        optimization: { images: true, fonts: true }
      };
      const expectedBuffer = new ArrayBuffer(1500);
      
      vi.spyOn(muPDFWrapper, 'saveDocument').mockResolvedValue(expectedBuffer);

      const result = await muPDFWrapper.saveDocument(mockDocument, saveOptions);
      
      expect(result).toBe(expectedBuffer);
      expect(result.byteLength).toBe(1500);
    });

    it('should get document metadata accurately', async () => {
      vi.spyOn(muPDFWrapper, 'getMetadata').mockResolvedValue(mockDocument.metadata);

      const metadata = await muPDFWrapper.getMetadata(mockDocument);
      
      expect(metadata).toEqual(mockDocument.metadata);
      expect(metadata.title).toBe('Test Document');
      expect(metadata.author).toBe('Test Author');
    });

    it('should handle corrupted document loading', async () => {
      const corruptedBuffer = new ArrayBuffer(10);
      vi.spyOn(muPDFWrapper, 'loadDocument').mockRejectedValue(new Error('Document corrupted'));

      await expect(muPDFWrapper.loadDocument(corruptedBuffer)).rejects.toThrow('Document corrupted');
    });
  });

  describe('Text Operations', () => {
    it('should extract text blocks from page', async () => {
      const mockTextBlocks = [
        { text: 'Sample Text 1', x: 100, y: 200, width: 150, height: 20, fontName: 'Arial', fontSize: 12 },
        { text: 'Sample Text 2', x: 100, y: 250, width: 200, height: 20, fontName: 'Arial', fontSize: 12 }
      ];
      
      vi.spyOn(muPDFWrapper, 'extractTextBlocks').mockResolvedValue(mockTextBlocks);

      const textBlocks = await muPDFWrapper.extractTextBlocks(mockDocument, 1);
      
      expect(textBlocks).toHaveLength(2);
      expect(textBlocks[0].text).toBe('Sample Text 1');
      expect(textBlocks[1].text).toBe('Sample Text 2');
    });

    it('should get text layout information', async () => {
      const mockLayout = {
        pageNum: 1,
        textBlocks: [
          { text: 'Title', x: 100, y: 100, width: 200, height: 30, fontName: 'Arial-Bold', fontSize: 16 }
        ],
        lineHeight: 1.2,
        wordSpacing: 2.5,
        charSpacing: 0.5
      };
      
      vi.spyOn(muPDFWrapper, 'getTextLayout').mockResolvedValue(mockLayout);

      const layout = await muPDFWrapper.getTextLayout(mockDocument, 1);
      
      expect(layout.pageNum).toBe(1);
      expect(layout.textBlocks).toHaveLength(1);
      expect(layout.lineHeight).toBe(1.2);
    });

    it('should insert text at specified position', async () => {
      const position = { x: 150, y: 300 };
      const text = 'Inserted Text';
      const style = { fontName: 'Arial', fontSize: 14, color: '#000000' };
      
      vi.spyOn(muPDFWrapper, 'insertText').mockResolvedValue();

      await muPDFWrapper.insertText(mockDocument, 1, position, text, style);
      
      expect(muPDFWrapper.insertText).toHaveBeenCalledWith(mockDocument, 1, position, text, style);
    });

    it('should modify text layout with preservation', async () => {
      const modifications: TextModification[] = [
        {
          type: 'modify',
          textRange: { startChar: 0, endChar: 10, pageNum: 1 },
          newText: 'Modified Text',
          newStyle: { fontName: 'Arial-Bold', fontSize: 14, color: '#FF0000' },
          layoutAdjustment: {
            preserveSpacing: true,
            adjustLineHeight: false,
            reflowParagraph: true,
            maintainJustification: true
          }
        }
      ];
      
      vi.spyOn(muPDFWrapper, 'modifyTextLayout').mockResolvedValue();

      await muPDFWrapper.modifyTextLayout(mockDocument, 1, modifications);
      
      expect(muPDFWrapper.modifyTextLayout).toHaveBeenCalledWith(mockDocument, 1, modifications);
    });

    it('should delete text range and adjust layout', async () => {
      const textRange = { startChar: 5, endChar: 20, pageNum: 1 };
      
      vi.spyOn(muPDFWrapper, 'deleteText').mockResolvedValue();

      await muPDFWrapper.deleteText(mockDocument, 1, textRange);
      
      expect(muPDFWrapper.deleteText).toHaveBeenCalledWith(mockDocument, 1, textRange);
    });
  });

  describe('Advanced Processing Features', () => {
    it('should add annotations to document', async () => {
      const annotation = {
        type: 'highlight',
        page: 1,
        rect: { x: 100, y: 100, width: 200, height: 50 },
        color: '#FFFF00',
        opacity: 0.5,
        content: 'Important highlight'
      };
      
      vi.spyOn(muPDFWrapper, 'addAnnotation').mockResolvedValue();

      await muPDFWrapper.addAnnotation(mockDocument, 1, annotation);
      
      expect(muPDFWrapper.addAnnotation).toHaveBeenCalledWith(mockDocument, 1, annotation);
    });

    it('should extract form fields from document', async () => {
      const mockFormFields = [
        { name: 'firstName', type: 'text', value: '', required: true, page: 1 },
        { name: 'lastName', type: 'text', value: '', required: true, page: 1 },
        { name: 'subscribe', type: 'checkbox', value: false, required: false, page: 1 }
      ];
      
      vi.spyOn(muPDFWrapper, 'getFormFields').mockResolvedValue(mockFormFields);

      const formFields = await muPDFWrapper.getFormFields(mockDocument);
      
      expect(formFields).toHaveLength(3);
      expect(formFields[0].name).toBe('firstName');
      expect(formFields[2].type).toBe('checkbox');
    });

    it('should encrypt document with permissions', async () => {
      const userPassword = 'user-pass';
      const ownerPassword = 'owner-pass';
      const permissions = {
        canPrint: false,
        canModify: false,
        canCopy: false,
        canAnnotate: true
      };
      
      vi.spyOn(muPDFWrapper, 'encrypt').mockResolvedValue();

      await muPDFWrapper.encrypt(mockDocument, userPassword, ownerPassword, permissions);
      
      expect(muPDFWrapper.encrypt).toHaveBeenCalledWith(mockDocument, userPassword, ownerPassword, permissions);
    });
  });

  describe('Error Handling and Recovery', () => {
    it('should handle WASM memory exhaustion gracefully', async () => {
      const largeBuffer = new ArrayBuffer(2 * 1024 * 1024 * 1024); // 2GB
      vi.spyOn(muPDFWrapper, 'loadDocument').mockRejectedValue(new Error('Out of memory'));

      await expect(muPDFWrapper.loadDocument(largeBuffer)).rejects.toThrow('Out of memory');
    });

    it('should recover from WASM module crashes', async () => {
      vi.spyOn(muPDFWrapper, 'loadDocument').mockRejectedValue(new Error('WASM module crashed'));
      vi.spyOn(muPDFWrapper, 'reinitialize').mockResolvedValue(true);

      try {
        await muPDFWrapper.loadDocument(mockDocument.buffer);
      } catch (error) {
        const recovered = await muPDFWrapper.reinitialize();
        expect(recovered).toBe(true);
      }
    });

    it('should validate document integrity before processing', async () => {
      const invalidBuffer = new ArrayBuffer(0);
      vi.spyOn(muPDFWrapper, 'validateDocument').mockResolvedValue(false);

      const isValid = await muPDFWrapper.validateDocument(invalidBuffer);
      expect(isValid).toBe(false);
    });

    it('should provide detailed error information for debugging', async () => {
      const buffer = new ArrayBuffer(1024);
      const error = new Error('Invalid PDF structure');
      error.name = 'PDFValidationError';
      
      vi.spyOn(muPDFWrapper, 'loadDocument').mockRejectedValue(error);

      await expect(muPDFWrapper.loadDocument(buffer)).rejects.toMatchObject({
        name: 'PDFValidationError',
        message: 'Invalid PDF structure'
      });
    });
  });

  describe('Performance and Memory Management', () => {
    it('should manage memory efficiently during operations', async () => {
      const initialMemory = await muPDFWrapper.getMemoryUsage();
      
      await muPDFWrapper.loadDocument(mockDocument.buffer);
      const loadedMemory = await muPDFWrapper.getMemoryUsage();
      
      await muPDFWrapper.cleanup();
      const cleanedMemory = await muPDFWrapper.getMemoryUsage();
      
      expect(loadedMemory).toBeGreaterThan(initialMemory);
      expect(cleanedMemory).toBeLessThanOrEqual(loadedMemory);
    });

    it('should implement garbage collection correctly', async () => {
      vi.spyOn(muPDFWrapper, 'performGarbageCollection').mockResolvedValue();
      
      await muPDFWrapper.performGarbageCollection();
      
      expect(muPDFWrapper.performGarbageCollection).toHaveBeenCalled();
    });

    it('should handle concurrent operations safely', async () => {
      const operations = Array.from({ length: 5 }, (_, i) => 
        muPDFWrapper.extractTextBlocks(mockDocument, i + 1)
      );
      
      vi.spyOn(muPDFWrapper, 'extractTextBlocks').mockResolvedValue([]);

      const results = await Promise.all(operations);
      
      expect(results).toHaveLength(5);
      expect(muPDFWrapper.extractTextBlocks).toHaveBeenCalledTimes(5);
    });
  });
});
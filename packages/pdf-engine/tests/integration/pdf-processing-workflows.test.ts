/**
 * Integration Tests for Complete PDF Processing Workflows
 * 
 * Tests complete user workflows including form processing, hybrid routing,
 * privacy compliance, and performance benchmarking.
 */

import { describe, it, expect, beforeAll, afterAll, beforeEach, afterEach } from 'vitest';
import { PDFProcessingEngine } from '../../src/engine';
import { FormProcessor } from '../../src/forms/form-processor';
import { BillingService } from '../../src/billing/billing-service';
import { PerformanceBenchmarker } from '../../src/performance/performance-benchmarker';
import { PrivacyManager } from '../../src/privacy/privacy-manager';
import { PrivacyLogger } from '../../src/privacy/privacy-logger';

// Mock PDF file for testing
function createMockPDFFile(sizeKB: number = 100, pages: number = 5): File {
  const sizeBytes = sizeKB * 1024;
  const mockPDFHeader = '%PDF-1.4\n';
  const content = mockPDFHeader + 'A'.repeat(Math.max(0, sizeBytes - mockPDFHeader.length));
  const blob = new Blob([content], { type: 'application/pdf' });
  
  // Add pages property to mock
  Object.defineProperty(blob, 'pages', { value: pages });
  
  return new File([blob], `test-${sizeKB}kb-${pages}p.pdf`, { 
    type: 'application/pdf',
    lastModified: Date.now()
  });
}

describe('PDF Processing Integration Tests', () => {
  let engine: PDFProcessingEngine;
  let formProcessor: FormProcessor;
  let billingService: BillingService;
  let benchmarker: PerformanceBenchmarker;
  let privacyManager: PrivacyManager;
  let privacyLogger: PrivacyLogger;

  beforeAll(async () => {
    // Initialize all services
    engine = PDFProcessingEngine.getInstance();
    await engine.initialize();

    formProcessor = FormProcessor.getInstance();
    await formProcessor.initialize();

    billingService = BillingService.getInstance();
    benchmarker = PerformanceBenchmarker.getInstance();
    await benchmarker.profileDevice();

    privacyManager = PrivacyManager.getInstance();
    privacyLogger = PrivacyLogger.getInstance();
  });

  afterAll(async () => {
    await engine.cleanup();
    await formProcessor.cleanup();
    await privacyManager.cleanup();
    privacyLogger.cleanup();
  });

  beforeEach(() => {
    // Clear any state between tests
    privacyLogger.clearOldLogs(0); // Clear all logs
  });

  describe('Complete PDF Processing Workflow', () => {
    it('should process a PDF with compression, privacy compliance, and performance monitoring', async () => {
      const testFile = createMockPDFFile(500, 10); // 500KB, 10 pages
      
      // Track the start of processing
      const startTime = performance.now();
      
      let progressSteps: string[] = [];
      const progressCallback = (progress: any) => {
        progressSteps.push(`${progress.stage}: ${progress.currentStep}`);
      };

      // Process document with compression
      const result = await engine.processDocument(testFile, {
        targetSize: 250 * 1024, // Target 250KB
        compressionLevel: 7
      }, progressCallback);

      const endTime = performance.now();

      // Verify processing results
      expect(result).toBeDefined();
      expect(result.data).toBeInstanceOf(Uint8Array);
      expect(result.originalSize).toBe(testFile.size);
      expect(result.processedSize).toBeGreaterThan(0);
      expect(result.compressionRatio).toBeGreaterThan(1);
      expect(result.processingTimeMs).toBeGreaterThan(0);
      
      // Verify progress tracking
      expect(progressSteps.length).toBeGreaterThan(0);
      expect(progressSteps).toContain(expect.stringContaining('loading'));
      expect(progressSteps).toContain(expect.stringContaining('finalizing'));

      // Check privacy compliance
      const privacyReport = privacyManager.generatePrivacyReport();
      expect(privacyReport.overallStatus).not.toBe('violations');
      expect(privacyReport.serverCommunications).toBe(0); // Should be client-side only

      // Check performance logging
      const analyticsEvents = privacyLogger.exportLogs('info', 'performance');
      expect(analyticsEvents.length).toBeGreaterThan(0);
      
      const performanceEvent = analyticsEvents.find(e => e.message.includes('completed'));
      expect(performanceEvent).toBeDefined();
      expect(performanceEvent?.privacy.containsSensitiveData).toBe(false);
      
    }, 30000); // 30 second timeout for processing
  });

  describe('Form Processing Workflow', () => {
    it('should detect, fill, and export form data with validation', async () => {
      const testFile = createMockPDFFile(200, 3);
      
      // Step 1: Detect form fields
      const detectedFields = await formProcessor.detectFormFields(await testFile.arrayBuffer());
      
      // For a mock PDF, we expect empty array (no actual forms)
      expect(Array.isArray(detectedFields)).toBe(true);

      // Step 2: Create a form field
      const fieldOptions = {
        fieldType: 'text' as const,
        name: 'testField',
        position: { x: 100, y: 200 },
        size: { width: 150, height: 20 },
        value: 'Test Value',
        required: true
      };

      const documentWithField = await formProcessor.createFormField(
        await testFile.arrayBuffer(),
        fieldOptions
      );

      expect(documentWithField).toBeInstanceOf(ArrayBuffer);
      expect(documentWithField.byteLength).toBeGreaterThan(0);

      // Step 3: Fill form data
      const formData = {
        fields: {
          'testField': 'Updated Value'
        },
        metadata: {
          version: '1.0'
        }
      };

      const fillResult = await formProcessor.fillFormFields(
        documentWithField,
        formData,
        true // validate data
      );

      expect(fillResult.success).toBe(true);
      expect(fillResult.fieldsProcessed).toBeGreaterThanOrEqual(0);
      expect(fillResult.validationErrors).toHaveLength(0);

      // Step 4: Export form data
      const exportedJSON = await formProcessor.exportFormData(
        documentWithField,
        { format: 'json', includeMetadata: true }
      );

      expect(typeof exportedJSON).toBe('string');
      expect(() => JSON.parse(exportedJSON as string)).not.toThrow();

      const exportedCSV = await formProcessor.exportFormData(
        documentWithField,
        { format: 'csv' }
      );

      expect(typeof exportedCSV).toBe('string');
      expect((exportedCSV as string).includes('Field Name,Value')).toBe(true);

    }, 20000);
  });

  describe('Hybrid Processing Architecture', () => {
    it('should provide transparent processing options with cost information', async () => {
      const testFile = createMockPDFFile(1000, 50); // 1MB, 50 pages (complex document)

      // Get processing options
      const processingRouter = (engine as any).processingRouter;
      if (processingRouter) {
        const options = await processingRouter.getProcessingOptions(testFile, {
          targetSize: 500 * 1024,
          compressionLevel: 8
        });

        expect(options).toBeDefined();
        expect(options.recommended).toBeDefined();
        expect(options.recommended.method).toBeDefined();
        expect(options.recommended.estimatedTime).toBeGreaterThan(0);
        expect(options.recommended.reasoning).toBeInstanceOf(Array);

        expect(options.alternatives).toBeInstanceOf(Array);
        expect(options.alternatives.length).toBeGreaterThan(0);

        // Check that each alternative has required properties
        for (const alt of options.alternatives) {
          expect(alt.method).toBeDefined();
          expect(alt.description).toBeDefined();
          expect(alt.cost).toBeGreaterThanOrEqual(0);
          expect(alt.pros).toBeInstanceOf(Array);
          expect(alt.cons).toBeInstanceOf(Array);
          expect(typeof alt.available).toBe('boolean');
        }

        // Should prefer client-side processing by default (privacy-first)
        expect(options.recommended.method).toMatch(/client/);
      }
    });

    it('should calculate billing costs accurately', async () => {
      const testFileSize = 2 * 1024 * 1024; // 2MB
      const testPageCount = 20;

      const costEstimate = billingService.calculateCostEstimate(
        testFileSize,
        testPageCount,
        {
          useServerProcessing: true,
          premiumFeatures: false,
          operationType: 'compression',
          complexity: 2
        },
        'basic'
      );

      expect(costEstimate).toBeDefined();
      expect(costEstimate.totalCost).toBeGreaterThan(0);
      expect(costEstimate.baseOperationCost).toBeGreaterThanOrEqual(0);
      expect(costEstimate.fileSizeCost).toBeGreaterThanOrEqual(0);
      expect(costEstimate.pageCost).toBeGreaterThanOrEqual(0);
      expect(costEstimate.breakdown).toBeInstanceOf(Array);
      expect(costEstimate.breakdown.length).toBeGreaterThan(0);

      // Check affordability
      const affordability = await billingService.canAffordOperation(costEstimate);
      expect(affordability).toBeDefined();
      expect(affordability.availableCredits).toBeGreaterThanOrEqual(0);
      expect(affordability.requiredCredits).toBeGreaterThan(0);
      expect(typeof affordability.canAfford).toBe('boolean');
    });
  });

  describe('Performance Benchmarking', () => {
    it('should monitor performance and validate 10x target', async () => {
      const testFile = createMockPDFFile(100, 5);
      
      // Process document and monitor performance
      const startTime = performance.now();
      
      await engine.processDocument(testFile, {
        targetSize: 50 * 1024
      });
      
      const processingTime = performance.now() - startTime;
      
      // Monitor the operation
      await benchmarker.monitorOperation(
        'compression',
        testFile.size,
        5, // pages
        processingTime,
        10, // memory usage MB
        'client-js'
      );

      // Get performance stats
      const stats = benchmarker.getPerformanceStats();
      
      expect(stats).toBeDefined();
      expect(stats.totalBenchmarks).toBeGreaterThanOrEqual(1);
      expect(stats.averageSpeedImprovement).toBeGreaterThan(0);
      expect(typeof stats.percentage10xSuccess).toBe('number');
      expect(stats.topPerformingOperations).toBeInstanceOf(Array);

      // Generate competitive report
      const competitiveReport = benchmarker.generateCompetitiveReport();
      
      expect(competitiveReport).toBeDefined();
      expect(competitiveReport.summary).toBeDefined();
      expect(competitiveReport.detailedComparison).toBeInstanceOf(Array);
      expect(competitiveReport.recommendations).toBeInstanceOf(Array);
    });
  });

  describe('Privacy Compliance', () => {
    it('should maintain privacy compliance throughout processing', async () => {
      const testFile = createMockPDFFile(300, 8);
      
      // Clear previous events
      privacyManager.performSecureMemoryCleanup();
      
      // Process document
      await engine.processDocument(testFile, {
        targetSize: 150 * 1024
      });

      // Verify zero server communication
      const serverCommunicationCheck = await privacyManager.verifyZeroServerCommunication();
      expect(serverCommunicationCheck.hasServerCommunication).toBe(false);
      expect(serverCommunicationCheck.complianceStatus).toBe('compliant');

      // Check processing location indicators
      const locationIndicators = privacyManager.getProcessingLocationIndicators();
      expect(locationIndicators.currentLocation).toBe('client');
      expect(locationIndicators.networkActivity).toBe(false);
      expect(locationIndicators.complianceStatus).toBe('compliant');

      // Generate privacy report
      const privacyReport = privacyManager.generatePrivacyReport();
      expect(privacyReport.overallStatus).not.toBe('violations');
      expect(privacyReport.serverCommunications).toBe(0);

      // Check privacy logging
      const privacyLogs = privacyLogger.exportLogs(undefined, 'privacy');
      expect(privacyLogs.every(log => !log.privacy.containsSensitiveData)).toBe(true);
    });

    it('should encrypt and decrypt temporary data securely', async () => {
      const testData = new TextEncoder().encode('Sensitive PDF content data');
      const testBuffer = testData.buffer;
      
      // Encrypt data
      const identifier = await privacyManager.encryptData(testBuffer, 'test-data', 5); // 5 minutes expiry
      expect(identifier).toBe('test-data');

      // Decrypt data
      const decryptedBuffer = await privacyManager.decryptData(identifier);
      expect(decryptedBuffer).toBeInstanceOf(ArrayBuffer);
      
      const decryptedText = new TextDecoder().decode(decryptedBuffer);
      expect(decryptedText).toBe('Sensitive PDF content data');

      // Clean up
      const cleanupResult = await privacyManager.performSecureMemoryCleanup(['test-data']);
      expect(cleanupResult.itemsCleared).toBe(1);
      expect(cleanupResult.complianceStatus).toBe('compliant');
    });
  });

  describe('Error Handling and Resilience', () => {
    it('should handle processing errors gracefully with privacy-preserving logging', async () => {
      // Create an invalid file to trigger an error
      const invalidFile = new File(['Invalid PDF content'], 'invalid.pdf', {
        type: 'application/pdf'
      });

      try {
        await engine.processDocument(invalidFile, {
          targetSize: 100 * 1024
        });
        
        // If we get here, the test should fail because we expected an error
        expect.fail('Expected processing to fail with invalid PDF');
      } catch (error) {
        // Log the error through privacy logger
        privacyLogger.logError(error as Error, {
          operationType: 'compression',
          fileSize: invalidFile.size,
          processingMethod: 'client-js'
        });

        // Verify error was logged without sensitive data
        const errorLogs = privacyLogger.exportLogs('error', 'error');
        expect(errorLogs.length).toBeGreaterThan(0);
        
        const latestErrorLog = errorLogs[errorLogs.length - 1];
        expect(latestErrorLog.privacy.containsSensitiveData).toBe(false);
        expect(latestErrorLog.error).toBeDefined();
        expect(latestErrorLog.message).toBeDefined();
      }
    });

    it('should fallback gracefully when WASM is not available', async () => {
      const testFile = createMockPDFFile(100, 5);
      
      // Force JavaScript processing by simulating WASM unavailability
      // (In a real test, we would mock the WASM wrapper to return false for isReady)
      
      const result = await engine.processDocument(testFile, {
        targetSize: 50 * 1024
      });

      expect(result).toBeDefined();
      expect(result.data).toBeInstanceOf(Uint8Array);
      expect(result.metadata?.processor).toBeDefined();
      
      // Should still maintain privacy compliance even in fallback mode
      const privacyReport = privacyManager.generatePrivacyReport();
      expect(privacyReport.overallStatus).not.toBe('violations');
    });
  });

  describe('Analytics and Monitoring', () => {
    it('should provide comprehensive analytics without sensitive data', async () => {
      // Perform several operations to generate analytics data
      const files = [
        createMockPDFFile(50, 2),
        createMockPDFFile(200, 8),
        createMockPDFFile(500, 15)
      ];

      for (const file of files) {
        await engine.processDocument(file, { compressionLevel: 5 });
      }

      // Get analytics summary
      const analyticsSummary = privacyLogger.getAnalyticsSummary();
      
      expect(analyticsSummary).toBeDefined();
      expect(analyticsSummary.totalOperations).toBeGreaterThan(0);
      expect(analyticsSummary.successRate).toBeGreaterThanOrEqual(0);
      expect(analyticsSummary.successRate).toBeLessThanOrEqual(100);
      expect(analyticsSummary.averageProcessingTime).toBeGreaterThan(0);
      expect(analyticsSummary.mostCommonOperations).toBeInstanceOf(Array);
      expect(analyticsSummary.privacyCompliance).toBeDefined();
      expect(analyticsSummary.privacyCompliance.compliantEvents).toBeGreaterThanOrEqual(0);

      // Verify no sensitive data in analytics
      const allLogs = privacyLogger.exportLogs();
      expect(allLogs.every(log => !log.privacy.containsSensitiveData)).toBe(true);
    });
  });
});

describe('Large File Handling Tests', () => {
  let engine: PDFProcessingEngine;

  beforeAll(async () => {
    engine = PDFProcessingEngine.getInstance();
    await engine.initialize();
  });

  afterAll(async () => {
    await engine.cleanup();
  });

  it('should handle large files with memory profiling', async () => {
    // Create a larger mock file (simulate 10MB)
    const largeFile = createMockPDFFile(10 * 1024, 100); // 10MB, 100 pages

    const initialMemory = performance.memory ? (performance as any).memory.usedJSHeapSize : 0;
    
    const result = await engine.processDocument(largeFile, {
      targetSize: 5 * 1024 * 1024, // Target 5MB
      compressionLevel: 6
    });

    const finalMemory = performance.memory ? (performance as any).memory.usedJSHeapSize : 0;
    const memoryIncrease = finalMemory - initialMemory;

    expect(result).toBeDefined();
    expect(result.originalSize).toBe(largeFile.size);
    expect(result.processedSize).toBeGreaterThan(0);
    
    // Memory increase should be reasonable (less than 5x the file size)
    if (initialMemory > 0) {
      expect(memoryIncrease).toBeLessThan(largeFile.size * 5);
    }

    // Processing time should be reasonable (less than 30 seconds for 10MB in test environment)
    expect(result.processingTimeMs).toBeLessThan(30000);

  }, 45000); // 45 second timeout for large file processing
});
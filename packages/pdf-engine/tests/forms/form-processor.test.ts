/**
 * Form Processor Tests
 * 
 * Tests for the form field processing system including detection, creation, 
 * filling, validation, and export capabilities.
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { FormProcessor, FormData, FormCreationOptions, FormExportOptions } from '../../src/forms/form-processor';
import { FormField, FormValidation } from '../../src/types/processing';

describe('FormProcessor', () => {
  let formProcessor: FormProcessor;

  beforeEach(async () => {
    formProcessor = FormProcessor.getInstance();
    await formProcessor.initialize();
  });

  afterEach(async () => {
    await formProcessor.cleanup();
  });

  describe('Singleton Pattern', () => {
    it('should return the same instance', () => {
      const instance1 = FormProcessor.getInstance();
      const instance2 = FormProcessor.getInstance();
      expect(instance1).toBe(instance2);
    });
  });

  describe('Form Field Detection', () => {
    it('should detect form fields in a PDF with forms', async () => {
      // Create a simple PDF buffer for testing
      const mockPDFBuffer = new ArrayBuffer(1024);
      
      // Mock the detection process
      const formFields = await formProcessor.detectFormFields(mockPDFBuffer);
      
      expect(Array.isArray(formFields)).toBe(true);
      // In a real test, we would provide a PDF with known form fields
    });

    it('should handle PDFs without form fields', async () => {
      const mockPDFBuffer = new ArrayBuffer(512);
      
      const formFields = await formProcessor.detectFormFields(mockPDFBuffer);
      
      expect(formFields).toHaveLength(0);
    });

    it('should call progress callback during detection', async () => {
      const mockPDFBuffer = new ArrayBuffer(1024);
      const progressCalls: any[] = [];
      
      const progressCallback = (progress: any) => {
        progressCalls.push(progress);
      };
      
      await formProcessor.detectFormFields(mockPDFBuffer, progressCallback);
      
      expect(progressCalls.length).toBeGreaterThan(0);
      expect(progressCalls[0]).toHaveProperty('stage');
      expect(progressCalls[0]).toHaveProperty('percentage');
      expect(progressCalls[0]).toHaveProperty('currentStep');
    });
  });

  describe('Form Field Creation', () => {
    it('should create a text field', async () => {
      const mockPDFBuffer = new ArrayBuffer(1024);
      
      const fieldOptions: FormCreationOptions = {
        fieldType: 'text',
        name: 'testTextField',
        position: { x: 100, y: 200 },
        size: { width: 150, height: 20 },
        value: 'Default text',
        required: false
      };
      
      const processedDocument = await formProcessor.createFormField(mockPDFBuffer, fieldOptions);
      
      expect(processedDocument).toBeInstanceOf(ArrayBuffer);
      expect(processedDocument.byteLength).toBeGreaterThan(0);
    });

    it('should create a checkbox field', async () => {
      const mockPDFBuffer = new ArrayBuffer(1024);
      
      const fieldOptions: FormCreationOptions = {
        fieldType: 'checkbox',
        name: 'testCheckbox',
        position: { x: 100, y: 180 },
        size: { width: 20, height: 20 },
        value: true,
        required: false
      };
      
      const processedDocument = await formProcessor.createFormField(mockPDFBuffer, fieldOptions);
      
      expect(processedDocument).toBeInstanceOf(ArrayBuffer);
      expect(processedDocument.byteLength).toBeGreaterThan(0);
    });

    it('should create a dropdown field with options', async () => {
      const mockPDFBuffer = new ArrayBuffer(1024);
      
      const fieldOptions: FormCreationOptions = {
        fieldType: 'dropdown',
        name: 'testDropdown',
        position: { x: 100, y: 160 },
        size: { width: 120, height: 20 },
        options: ['Option 1', 'Option 2', 'Option 3'],
        value: 'Option 1',
        required: true
      };
      
      const processedDocument = await formProcessor.createFormField(mockPDFBuffer, fieldOptions);
      
      expect(processedDocument).toBeInstanceOf(ArrayBuffer);
      expect(processedDocument.byteLength).toBeGreaterThan(0);
    });

    it('should validate field creation options', async () => {
      const mockPDFBuffer = new ArrayBuffer(1024);
      
      // Missing name
      const invalidOptions: Partial<FormCreationOptions> = {
        fieldType: 'text',
        position: { x: 100, y: 200 },
        size: { width: 150, height: 20 }
      };
      
      await expect(
        formProcessor.createFormField(mockPDFBuffer, invalidOptions as FormCreationOptions)
      ).rejects.toThrow('Field name is required');
    });

    it('should require options for dropdown fields', async () => {
      const mockPDFBuffer = new ArrayBuffer(1024);
      
      const invalidOptions: FormCreationOptions = {
        fieldType: 'dropdown',
        name: 'testDropdown',
        position: { x: 100, y: 160 },
        size: { width: 120, height: 20 }
      };
      
      await expect(
        formProcessor.createFormField(mockPDFBuffer, invalidOptions)
      ).rejects.toThrow('Options array is required for dropdown fields');
    });
  });

  describe('Form Field Validation', () => {
    it('should validate required fields', async () => {
      const mockPDFBuffer = new ArrayBuffer(1024);
      
      const formData: FormData = {
        fields: {
          'optionalField': 'some value'
          // Missing required field
        },
        metadata: {
          version: '1.0'
        }
      };
      
      // Mock existing fields - one required, one optional
      const mockFields: FormField[] = [
        {
          id: 'field1',
          name: 'requiredField',
          type: 'text',
          pageNum: 1,
          bbox: { x: 0, y: 0, width: 100, height: 20 },
          required: true
        },
        {
          id: 'field2',
          name: 'optionalField',
          type: 'text',
          pageNum: 1,
          bbox: { x: 0, y: 30, width: 100, height: 20 },
          required: false
        }
      ];
      
      const result = await formProcessor.fillFormFields(mockPDFBuffer, formData, true);
      
      expect(result.success).toBe(false);
      expect(result.validationErrors.length).toBeGreaterThan(0);
      expect(result.validationErrors[0].fieldName).toBe('requiredField');
    });

    it('should validate email format', async () => {
      const mockPDFBuffer = new ArrayBuffer(1024);
      
      const formData: FormData = {
        fields: {
          'emailField': 'invalid-email'
        },
        metadata: {
          version: '1.0'
        }
      };
      
      const result = await formProcessor.fillFormFields(mockPDFBuffer, formData, true);
      
      // Validation would be performed if the field had email validation enabled
      expect(result).toBeDefined();
    });
  });

  describe('Form Data Export', () => {
    it('should export form data to JSON', async () => {
      const mockPDFBuffer = new ArrayBuffer(1024);
      
      const exportOptions: FormExportOptions = {
        format: 'json',
        includeMetadata: true
      };
      
      const exportResult = await formProcessor.exportFormData(mockPDFBuffer, exportOptions);
      
      expect(typeof exportResult).toBe('string');
      expect(() => JSON.parse(exportResult as string)).not.toThrow();
    });

    it('should export form data to CSV', async () => {
      const mockPDFBuffer = new ArrayBuffer(1024);
      
      const exportOptions: FormExportOptions = {
        format: 'csv',
        includeMetadata: false
      };
      
      const exportResult = await formProcessor.exportFormData(mockPDFBuffer, exportOptions);
      
      expect(typeof exportResult).toBe('string');
      expect((exportResult as string).includes('Field Name,Value')).toBe(true);
    });

    it('should export form data to XML', async () => {
      const mockPDFBuffer = new ArrayBuffer(1024);
      
      const exportOptions: FormExportOptions = {
        format: 'xml',
        includeMetadata: true
      };
      
      const exportResult = await formProcessor.exportFormData(mockPDFBuffer, exportOptions);
      
      expect(typeof exportResult).toBe('string');
      expect((exportResult as string).includes('<?xml version="1.0"')).toBe(true);
      expect((exportResult as string).includes('<form>')).toBe(true);
    });

    it('should export form data to FDF format', async () => {
      const mockPDFBuffer = new ArrayBuffer(1024);
      
      const exportOptions: FormExportOptions = {
        format: 'pdf-fdf'
      };
      
      const exportResult = await formProcessor.exportFormData(mockPDFBuffer, exportOptions);
      
      expect(exportResult).toBeInstanceOf(ArrayBuffer);
    });
  });

  describe('Form Submission Processing', () => {
    it('should process form submission successfully', async () => {
      const mockPDFBuffer = new ArrayBuffer(1024);
      
      const submissionData: FormData = {
        fields: {
          'name': 'John Doe',
          'email': '<EMAIL>',
          'agree': true
        },
        metadata: {
          version: '1.0'
        }
      };
      
      const workflowOptions = {
        validateRequired: false,
        generateReceipt: false
      };
      
      const result = await formProcessor.processFormSubmission(
        mockPDFBuffer, 
        submissionData, 
        workflowOptions
      );
      
      expect(result.success).toBe(true);
      expect(result.submissionId).toBeDefined();
      expect(result.submissionId.length).toBeGreaterThan(0);
      expect(result.processingTimeMs).toBeGreaterThan(0);
    });

    it('should generate submission receipt when requested', async () => {
      const mockPDFBuffer = new ArrayBuffer(1024);
      
      const submissionData: FormData = {
        fields: {
          'name': 'Jane Doe',
          'email': '<EMAIL>'
        },
        metadata: {
          version: '1.0'
        }
      };
      
      const workflowOptions = {
        validateRequired: false,
        generateReceipt: true
      };
      
      const result = await formProcessor.processFormSubmission(
        mockPDFBuffer, 
        submissionData, 
        workflowOptions
      );
      
      expect(result.success).toBe(true);
      expect(result.receipt).toBeDefined();
      expect(result.receipt).toBeInstanceOf(ArrayBuffer);
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid PDF buffers gracefully', async () => {
      const invalidBuffer = new ArrayBuffer(0); // Empty buffer
      
      const formFields = await formProcessor.detectFormFields(invalidBuffer);
      
      // Should return empty array rather than throwing
      expect(Array.isArray(formFields)).toBe(true);
    });

    it('should handle form processor cleanup', async () => {
      await expect(formProcessor.cleanup()).resolves.not.toThrow();
    });
  });

  describe('Statistics and Monitoring', () => {
    it('should provide processing statistics', () => {
      const stats = formProcessor.getProcessingStats();
      
      expect(stats).toHaveProperty('totalFormsProcessed');
      expect(stats).toHaveProperty('avgFieldsPerForm');
      expect(stats).toHaveProperty('mostCommonFieldType');
      expect(stats).toHaveProperty('successRate');
    });
  });
});

// Integration tests with actual PDF processing
describe('FormProcessor Integration Tests', () => {
  let formProcessor: FormProcessor;

  beforeEach(async () => {
    formProcessor = FormProcessor.getInstance();
    await formProcessor.initialize();
  });

  afterEach(async () => {
    await formProcessor.cleanup();
  });

  // These tests would use actual PDF files with forms
  it.skip('should detect fields in real PDF with forms', async () => {
    // Would use actual PDF file buffer
    // const pdfBuffer = await fs.readFile('test-form.pdf');
    // const fields = await formProcessor.detectFormFields(pdfBuffer);
    // expect(fields.length).toBeGreaterThan(0);
  });

  it.skip('should fill real form fields', async () => {
    // Would use actual PDF with form fields
    // const pdfBuffer = await fs.readFile('test-form.pdf');
    // const formData = { fields: { 'name': 'Test User' }, metadata: {} };
    // const result = await formProcessor.fillFormFields(pdfBuffer, formData);
    // expect(result.success).toBe(true);
  });
});
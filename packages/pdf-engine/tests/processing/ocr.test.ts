import { describe, it, expect, beforeAll, afterAll, beforeEach, afterEach, vi } from 'vitest';
import { OCRProcessor } from '../../src/ocr/ocr-processor';
import type { 
  PDFDocument, 
  OCROptions, 
  OCRResult, 
  TextBlock 
} from '../../src/types/processing';

describe('OCR Processing System', () => {
  let ocrProcessor: OCRProcessor;
  let mockDocument: PDFDocument;

  beforeAll(async () => {
    ocrProcessor = new OCRProcessor({
      tesseractCorePath: '/assets/tesseract-core.wasm.js',
      languages: ['eng', 'fra', 'deu', 'spa'],
      workerCount: 2,
      enablePreprocessing: true
    });
  });

  afterAll(() => {
    ocrProcessor.cleanup();
  });

  beforeEach(() => {
    mockDocument = {
      id: 'ocr-test-doc',
      buffer: new ArrayBuffer(2 * 1024 * 1024), // 2MB
      pageCount: 8,
      metadata: {
        title: 'OCR Test Document',
        author: 'Test Author',
        subject: 'OCR Testing',
        keywords: 'ocr, text recognition, test',
        creator: 'Test Creator',
        producer: 'Test Producer',
        creationDate: new Date(),
        modificationDate: new Date()
      },
      encrypted: false,
      permissions: {
        canPrint: true,
        canModify: true,
        canCopy: true,
        canAnnotate: true
      }
    };
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Scanned Document Detection', () => {
    it('should detect scanned documents accurately', async () => {
      const scannedDocAnalysis = {
        isScanned: true,
        confidence: 0.92,
        textToImageRatio: 0.05,
        hasTextLayer: false,
        imageQuality: 'high',
        ocrRecommended: true,
        estimatedAccuracy: 0.88
      };

      vi.spyOn(ocrProcessor, 'detectScannedDocument').mockResolvedValue(scannedDocAnalysis);

      const analysis = await ocrProcessor.detectScannedDocument(mockDocument);

      expect(analysis.isScanned).toBe(true);
      expect(analysis.confidence).toBe(0.92);
      expect(analysis.ocrRecommended).toBe(true);
      expect(analysis.hasTextLayer).toBe(false);
    });

    it('should detect documents with existing text layers', async () => {
      const textLayerDoc = {
        isScanned: false,
        confidence: 0.95,
        textToImageRatio: 0.85,
        hasTextLayer: true,
        imageQuality: 'n/a',
        ocrRecommended: false,
        existingTextAccuracy: 0.98
      };

      vi.spyOn(ocrProcessor, 'detectScannedDocument').mockResolvedValue(textLayerDoc);

      const analysis = await ocrProcessor.detectScannedDocument(mockDocument);

      expect(analysis.isScanned).toBe(false);
      expect(analysis.hasTextLayer).toBe(true);
      expect(analysis.ocrRecommended).toBe(false);
    });

    it('should analyze image quality for OCR suitability', async () => {
      const qualityAnalysis = {
        resolution: 300, // DPI
        contrast: 0.85,
        brightness: 0.7,
        skewAngle: 2.5,
        noiseLevel: 0.15,
        overallQuality: 'good',
        preprocessingNeeded: true,
        recommendedImprovements: ['deskew', 'contrast-enhancement']
      };

      vi.spyOn(ocrProcessor, 'analyzeImageQuality').mockResolvedValue(qualityAnalysis);

      const quality = await ocrProcessor.analyzeImageQuality(mockDocument, 1);

      expect(quality.resolution).toBe(300);
      expect(quality.overallQuality).toBe('good');
      expect(quality.recommendedImprovements).toContain('deskew');
      expect(quality.preprocessingNeeded).toBe(true);
    });
  });

  describe('Image Preprocessing', () => {
    it('should enhance image contrast for better OCR accuracy', async () => {
      const contrastOptions = {
        algorithm: 'adaptive-histogram-equalization',
        strength: 1.2,
        preserveGrayscale: true
      };

      const enhancementResult = {
        contrastImprovement: 0.35,
        brightnessAdjustment: 0.1,
        processingTime: 150,
        qualityScore: 0.88
      };

      vi.spyOn(ocrProcessor, 'enhanceContrast').mockResolvedValue(enhancementResult);

      const result = await ocrProcessor.enhanceContrast(mockDocument, 1, contrastOptions);

      expect(result.contrastImprovement).toBe(0.35);
      expect(result.qualityScore).toBe(0.88);
    });

    it('should perform document deskewing accurately', async () => {
      const skewDetection = {
        detectedAngle: -3.2,
        confidence: 0.94,
        correctionApplied: true,
        qualityImprovement: 0.25
      };

      vi.spyOn(ocrProcessor, 'detectAndCorrectSkew').mockResolvedValue(skewDetection);

      const result = await ocrProcessor.detectAndCorrectSkew(mockDocument, 1);

      expect(result.detectedAngle).toBe(-3.2);
      expect(result.correctionApplied).toBe(true);
      expect(result.qualityImprovement).toBe(0.25);
    });

    it('should reduce noise while preserving text quality', async () => {
      const noiseReductionOptions = {
        algorithm: 'bilateral-filter',
        strength: 0.8,
        preserveTextSharpness: true,
        removeSpeckles: true
      };

      const noiseReductionResult = {
        noiseReduced: 0.65,
        textSharpnessPreserved: 0.92,
        processingTime: 200,
        qualityImprovement: 0.18
      };

      vi.spyOn(ocrProcessor, 'reduceNoise').mockResolvedValue(noiseReductionResult);

      const result = await ocrProcessor.reduceNoise(mockDocument, 1, noiseReductionOptions);

      expect(result.noiseReduced).toBe(0.65);
      expect(result.textSharpnessPreserved).toBe(0.92);
    });

    it('should binarize images for optimal OCR processing', async () => {
      const binarizationOptions = {
        algorithm: 'otsu-threshold',
        adaptiveThreshold: true,
        localProcessing: true
      };

      const binarizationResult = {
        thresholdValue: 128,
        textSeparation: 0.91,
        backgroundCleanness: 0.96,
        processingTime: 80
      };

      vi.spyOn(ocrProcessor, 'binarizeImage').mockResolvedValue(binarizationResult);

      const result = await ocrProcessor.binarizeImage(mockDocument, 1, binarizationOptions);

      expect(result.textSeparation).toBe(0.91);
      expect(result.backgroundCleanness).toBe(0.96);
    });
  });

  describe('Multi-language Text Recognition', () => {
    it('should detect document language accurately', async () => {
      const languageDetection = {
        primaryLanguage: 'eng',
        secondaryLanguages: ['fra', 'deu'],
        confidence: 0.89,
        textSamples: [
          { text: 'The quick brown fox', language: 'eng', confidence: 0.95 },
          { text: 'Le renard brun rapide', language: 'fra', confidence: 0.87 }
        ]
      };

      vi.spyOn(ocrProcessor, 'detectLanguage').mockResolvedValue(languageDetection);

      const detection = await ocrProcessor.detectLanguage(mockDocument, 1);

      expect(detection.primaryLanguage).toBe('eng');
      expect(detection.secondaryLanguages).toContain('fra');
      expect(detection.confidence).toBe(0.89);
    });

    it('should perform OCR with multiple language support', async () => {
      const multiLangOptions: OCROptions = {
        languages: ['eng', 'fra', 'deu'],
        enableConfidenceScoring: true,
        enableWordBoxes: true,
        enableCharBoxes: false,
        psm: 6, // Uniform block of text
        oem: 3  // Default engine mode
      };

      const ocrResult: OCRResult = {
        text: 'Mixed language text with français and Deutsch words.',
        confidence: 0.86,
        processingTime: 2500,
        language: 'multilingual',
        textBlocks: [
          {
            text: 'Mixed language text with',
            x: 100,
            y: 200,
            width: 200,
            height: 20,
            fontName: 'Arial',
            fontSize: 12,
            color: '#000000',
            rotation: 0,
            opacity: 1.0,
            confidence: 0.92,
            language: 'eng'
          },
          {
            text: 'français',
            x: 320,
            y: 200,
            width: 60,
            height: 20,
            fontName: 'Arial',
            fontSize: 12,
            color: '#000000',
            rotation: 0,
            opacity: 1.0,
            confidence: 0.88,
            language: 'fra'
          }
        ],
        wordBoxes: [
          { text: 'Mixed', x: 100, y: 200, width: 35, height: 20, confidence: 0.95 },
          { text: 'language', x: 140, y: 200, width: 50, height: 20, confidence: 0.91 }
        ]
      };

      vi.spyOn(ocrProcessor, 'performOCR').mockResolvedValue(ocrResult);

      const result = await ocrProcessor.performOCR(mockDocument, 1, multiLangOptions);

      expect(result.confidence).toBe(0.86);
      expect(result.textBlocks).toHaveLength(2);
      expect(result.wordBoxes).toHaveLength(2);
      expect(result.language).toBe('multilingual');
    });

    it('should handle specialized character sets and symbols', async () => {
      const specialCharOptions = {
        languages: ['eng'],
        enableSpecialCharacters: true,
        recognizeNumbers: true,
        recognizePunctuation: true,
        customPatterns: ['email', 'phone', 'date']
      };

      const specialCharResult = {
        text: 'Contact: <EMAIL> or call (************* on 01/15/2024',
        extractedPatterns: [
          { type: 'email', value: '<EMAIL>', confidence: 0.94 },
          { type: 'phone', value: '(*************', confidence: 0.91 },
          { type: 'date', value: '01/15/2024', confidence: 0.89 }
        ],
        confidence: 0.87
      };

      vi.spyOn(ocrProcessor, 'recognizeSpecialCharacters').mockResolvedValue(specialCharResult);

      const result = await ocrProcessor.recognizeSpecialCharacters(mockDocument, 1, specialCharOptions);

      expect(result.extractedPatterns).toHaveLength(3);
      expect(result.extractedPatterns[0].type).toBe('email');
      expect(result.extractedPatterns[1].type).toBe('phone');
    });
  });

  describe('Confidence Scoring and Validation', () => {
    it('should provide accurate confidence scores for recognized text', async () => {
      const confidenceAnalysis = {
        overallConfidence: 0.84,
        wordConfidences: [
          { word: 'The', confidence: 0.98 },
          { word: 'quick', confidence: 0.92 },
          { word: 'brown', confidence: 0.87 },
          { word: 'fox', confidence: 0.79 }
        ],
        lowConfidenceWords: [
          { word: 'fox', confidence: 0.79, suggestions: ['for', 'box', 'fix'] }
        ],
        averageConfidence: 0.89
      };

      vi.spyOn(ocrProcessor, 'analyzeConfidence').mockResolvedValue(confidenceAnalysis);

      const analysis = await ocrProcessor.analyzeConfidence(mockDocument, 1);

      expect(analysis.overallConfidence).toBe(0.84);
      expect(analysis.lowConfidenceWords).toHaveLength(1);
      expect(analysis.lowConfidenceWords[0].suggestions).toContain('for');
    });

    it('should validate OCR results against known patterns', async () => {
      const validationRules = {
        spellCheck: true,
        grammarCheck: false,
        patternValidation: true,
        contextualValidation: true
      };

      const validationResult = {
        spellErrors: 2,
        patternMatches: 5,
        contextualScore: 0.91,
        suggestedCorrections: [
          { original: 'teh', suggested: 'the', confidence: 0.96 },
          { original: 'recieve', suggested: 'receive', confidence: 0.94 }
        ],
        validationScore: 0.87
      };

      vi.spyOn(ocrProcessor, 'validateOCRResults').mockResolvedValue(validationResult);

      const result = await ocrProcessor.validateOCRResults('Sample text with teh quick brown fox to recieve attention', validationRules);

      expect(result.spellErrors).toBe(2);
      expect(result.suggestedCorrections).toHaveLength(2);
      expect(result.validationScore).toBe(0.87);
    });

    it('should provide manual correction tools', async () => {
      const correctionTools = {
        highlightLowConfidence: true,
        enableInlineEditing: true,
        showAlternatives: true,
        trackChanges: true
      };

      const correctionInterface = {
        editableRegions: [
          { startChar: 15, endChar: 23, confidence: 0.65, alternatives: ['document', 'documents', 'documant'] }
        ],
        correctionsMade: 0,
        userFeedback: []
      };

      vi.spyOn(ocrProcessor, 'enableManualCorrection').mockResolvedValue(correctionInterface);

      const interface_ = await ocrProcessor.enableManualCorrection(mockDocument, 1, correctionTools);

      expect(interface_.editableRegions).toHaveLength(1);
      expect(interface_.editableRegions[0].alternatives).toContain('document');
    });
  });

  describe('Searchable PDF Creation', () => {
    it('should create invisible text overlay for searchable PDF', async () => {
      const overlayOptions = {
        preserveOriginalImage: true,
        textLayerOpacity: 0.0,
        fontMatching: true,
        positionAccuracy: 'high'
      };

      const overlayResult = {
        textLayerCreated: true,
        wordsPlaced: 145,
        positionAccuracy: 0.94,
        fontMatchingScore: 0.87,
        searchableText: 'The complete text content that was recognized and made searchable',
        processingTime: 1800
      };

      vi.spyOn(ocrProcessor, 'createInvisibleTextOverlay').mockResolvedValue(overlayResult);

      const result = await ocrProcessor.createInvisibleTextOverlay(mockDocument, 1, overlayOptions);

      expect(result.textLayerCreated).toBe(true);
      expect(result.wordsPlaced).toBe(145);
      expect(result.positionAccuracy).toBe(0.94);
    });

    it('should preserve original image quality while adding text layer', async () => {
      const preservationOptions = {
        imageCompressionLevel: 'none',
        maintainImageResolution: true,
        preserveColorSpace: true,
        addTextLayerOnly: true
      };

      const preservationResult = {
        originalImagePreserved: true,
        imageQualityLoss: 0.0,
        fileSizeIncrease: 0.15, // 15% increase due to text layer
        searchabilityAdded: true,
        textLayerSize: 25 * 1024 // 25KB text layer
      };

      vi.spyOn(ocrProcessor, 'preserveImageWithTextLayer').mockResolvedValue(preservationResult);

      const result = await ocrProcessor.preserveImageWithTextLayer(mockDocument, preservationOptions);

      expect(result.originalImagePreserved).toBe(true);
      expect(result.imageQualityLoss).toBe(0.0);
      expect(result.searchabilityAdded).toBe(true);
    });

    it('should enable full-text search functionality', async () => {
      const searchOptions = {
        enableCaseSensitiveSearch: false,
        enableWholeWordSearch: true,
        enableRegexSearch: true,
        indexCreation: true
      };

      const searchCapabilities = {
        searchIndexCreated: true,
        searchableWordsCount: 1250,
        indexSize: 45 * 1024, // 45KB
        averageSearchTime: 15, // ms
        searchAccuracy: 0.96
      };

      vi.spyOn(ocrProcessor, 'enableFullTextSearch').mockResolvedValue(searchCapabilities);

      const result = await ocrProcessor.enableFullTextSearch(mockDocument, searchOptions);

      expect(result.searchIndexCreated).toBe(true);
      expect(result.searchableWordsCount).toBe(1250);
      expect(result.searchAccuracy).toBe(0.96);
    });
  });

  describe('Advanced OCR Features', () => {
    it('should recognize table structures and preserve layout', async () => {
      const tableOptions = {
        detectTables: true,
        preserveTableStructure: true,
        extractCellData: true,
        outputFormat: 'html'
      };

      const tableRecognition = {
        tablesDetected: 2,
        tables: [
          {
            rows: 4,
            columns: 3,
            cellData: [
              ['Header 1', 'Header 2', 'Header 3'],
              ['Row 1 Col 1', 'Row 1 Col 2', 'Row 1 Col 3'],
              ['Row 2 Col 1', 'Row 2 Col 2', 'Row 2 Col 3'],
              ['Row 3 Col 1', 'Row 3 Col 2', 'Row 3 Col 3']
            ],
            confidence: 0.89
          }
        ],
        structurePreserved: true
      };

      vi.spyOn(ocrProcessor, 'recognizeTableStructures').mockResolvedValue(tableRecognition);

      const result = await ocrProcessor.recognizeTableStructures(mockDocument, 1, tableOptions);

      expect(result.tablesDetected).toBe(2);
      expect(result.tables[0].rows).toBe(4);
      expect(result.tables[0].columns).toBe(3);
      expect(result.structurePreserved).toBe(true);
    });

    it('should handle handwritten text recognition', async () => {
      const handwritingOptions = {
        enableHandwritingRecognition: true,
        language: 'eng',
        writingStyle: 'cursive',
        confidenceThreshold: 0.7
      };

      const handwritingResult = {
        handwritingDetected: true,
        recognizedText: 'Handwritten note content',
        confidence: 0.75,
        processingTime: 3500,
        qualityScore: 0.68
      };

      vi.spyOn(ocrProcessor, 'recognizeHandwriting').mockResolvedValue(handwritingResult);

      const result = await ocrProcessor.recognizeHandwriting(mockDocument, 1, handwritingOptions);

      expect(result.handwritingDetected).toBe(true);
      expect(result.confidence).toBe(0.75);
      expect(result.recognizedText).toBe('Handwritten note content');
    });

    it('should extract and recognize mathematical formulas', async () => {
      const mathOptions = {
        recognizeMathFormulas: true,
        outputFormat: 'latex',
        includeSymbols: true,
        preserveFormatting: true
      };

      const mathRecognition = {
        formulasDetected: 3,
        formulas: [
          { latex: 'E = mc^2', confidence: 0.94, type: 'equation' },
          { latex: '\\int_{0}^{\\infty} e^{-x} dx = 1', confidence: 0.87, type: 'integral' },
          { latex: '\\sum_{i=1}^{n} i = \\frac{n(n+1)}{2}', confidence: 0.91, type: 'summation' }
        ],
        averageConfidence: 0.91
      };

      vi.spyOn(ocrProcessor, 'recognizeMathFormulas').mockResolvedValue(mathRecognition);

      const result = await ocrProcessor.recognizeMathFormulas(mockDocument, 1, mathOptions);

      expect(result.formulasDetected).toBe(3);
      expect(result.formulas[0].latex).toBe('E = mc^2');
      expect(result.averageConfidence).toBe(0.91);
    });
  });

  describe('Performance Optimization', () => {
    it('should process OCR using Web Workers for non-blocking operation', async () => {
      const workerOptions = {
        useWebWorkers: true,
        workerCount: 2,
        enableParallelProcessing: true,
        loadBalancing: true
      };

      const workerResult = {
        workersUsed: 2,
        parallelPages: [1, 2],
        processingTime: 1800,
        speedImprovement: 0.65 // 65% faster
      };

      vi.spyOn(ocrProcessor, 'processWithWorkers').mockResolvedValue(workerResult);

      const result = await ocrProcessor.processWithWorkers(mockDocument, [1, 2], workerOptions);

      expect(result.workersUsed).toBe(2);
      expect(result.speedImprovement).toBe(0.65);
    });

    it('should optimize memory usage for large document OCR', async () => {
      const largeDocument = {
        ...mockDocument,
        pageCount: 100
      };

      const memoryOptions = {
        processInBatches: true,
        batchSize: 5,
        enableGarbageCollection: true,
        maxMemoryUsage: 512 * 1024 * 1024 // 512MB
      };

      const memoryResult = {
        batchesProcessed: 20,
        peakMemoryUsage: 480 * 1024 * 1024, // Under limit
        averageProcessingTime: 2200,
        memoryOptimized: true
      };

      vi.spyOn(ocrProcessor, 'processWithMemoryOptimization').mockResolvedValue(memoryResult);

      const result = await ocrProcessor.processWithMemoryOptimization(largeDocument, memoryOptions);

      expect(result.batchesProcessed).toBe(20);
      expect(result.peakMemoryUsage).toBeLessThan(memoryOptions.maxMemoryUsage);
      expect(result.memoryOptimized).toBe(true);
    });

    it('should cache OCR results for repeated processing', async () => {
      const cacheOptions = {
        enableResultCaching: true,
        cacheKey: 'page-1-hash-abc123',
        cacheExpiry: 3600 // 1 hour
      };

      // First call - should process
      vi.spyOn(ocrProcessor, 'getCachedResult').mockReturnValue(null);
      vi.spyOn(ocrProcessor, 'performOCR').mockResolvedValue({
        text: 'Cached OCR result',
        confidence: 0.89,
        processingTime: 2000,
        language: 'eng',
        textBlocks: [],
        wordBoxes: []
      });
      vi.spyOn(ocrProcessor, 'cacheResult').mockImplementation();

      const firstResult = await ocrProcessor.performOCR(mockDocument, 1);
      
      expect(ocrProcessor.performOCR).toHaveBeenCalled();
      expect(firstResult.processingTime).toBe(2000);

      // Second call - should use cache
      vi.spyOn(ocrProcessor, 'getCachedResult').mockReturnValue({
        text: 'Cached OCR result',
        confidence: 0.89,
        processingTime: 50, // Much faster from cache
        language: 'eng',
        textBlocks: [],
        wordBoxes: []
      });

      const cachedResult = await ocrProcessor.getCachedResult(cacheOptions.cacheKey);
      
      expect(cachedResult?.processingTime).toBe(50);
    });
  });
});
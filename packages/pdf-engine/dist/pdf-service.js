export class PDFService {
    static instance;
    worker = null;
    messageId = 0;
    pendingOperations = new Map();
    constructor() { }
    static getInstance() {
        if (!PDFService.instance) {
            PDFService.instance = new PDFService();
        }
        return PDFService.instance;
    }
    /**
     * Initialize the PDF service with Web Worker
     */
    async initialize() {
        if (this.worker) {
            return; // Already initialized
        }
        try {
            // Create worker from the worker file
            const workerBlob = new Blob([
            // We'll need to import the worker code here
            // For now, we'll create a URL that can be loaded
            ], { type: 'application/javascript' });
            this.worker = new Worker(URL.createObjectURL(workerBlob), {
                type: 'module'
            });
            this.worker.addEventListener('message', this.handleWorkerMessage.bind(this));
            this.worker.addEventListener('error', this.handleWorkerError.bind(this));
            // Test worker is working
            await this.testWorker();
        }
        catch (error) {
            throw new Error(`Failed to initialize PDF service: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    /**
     * Process a PDF document
     */
    async processDocument(file, options = {}, progressCallback) {
        await this.ensureWorkerReady();
        const fileArrayBuffer = await file.arrayBuffer();
        const messageId = this.generateMessageId();
        return this.sendWorkerMessage({
            id: messageId,
            type: 'process',
            payload: {
                file: fileArrayBuffer,
                fileName: file.name,
                options
            }
        }, progressCallback);
    }
    /**
     * Compress PDF to target size
     */
    async compressToTarget(file, targetSize, progressCallback) {
        await this.ensureWorkerReady();
        const fileArrayBuffer = await file.arrayBuffer();
        const messageId = this.generateMessageId();
        const result = await this.sendWorkerMessage({
            id: messageId,
            type: 'compress',
            payload: {
                file: fileArrayBuffer,
                fileName: file.name,
                targetSize
            }
        }, progressCallback);
        return result.data;
    }
    /**
     * Add signature to PDF
     */
    async addSignature(file, signature, position) {
        await this.ensureWorkerReady();
        const fileArrayBuffer = await file.arrayBuffer();
        const messageId = this.generateMessageId();
        const result = await this.sendWorkerMessage({
            id: messageId,
            type: 'addSignature',
            payload: {
                file: fileArrayBuffer,
                fileName: file.name,
                signature,
                position
            }
        });
        return result.data;
    }
    /**
     * Add watermark to PDF
     */
    async addWatermark(file, watermark) {
        await this.ensureWorkerReady();
        const fileArrayBuffer = await file.arrayBuffer();
        const messageId = this.generateMessageId();
        const result = await this.sendWorkerMessage({
            id: messageId,
            type: 'addWatermark',
            payload: {
                file: fileArrayBuffer,
                fileName: file.name,
                watermark
            }
        });
        return result.data;
    }
    /**
     * Validate if file is a valid PDF
     */
    async validatePDF(file) {
        await this.ensureWorkerReady();
        const fileArrayBuffer = await file.arrayBuffer();
        const messageId = this.generateMessageId();
        const result = await this.sendWorkerMessage({
            id: messageId,
            type: 'validate',
            payload: {
                file: fileArrayBuffer,
                fileName: file.name
            }
        });
        return result.valid;
    }
    /**
     * Get PDF information
     */
    async getPDFInfo(file) {
        await this.ensureWorkerReady();
        const fileArrayBuffer = await file.arrayBuffer();
        const messageId = this.generateMessageId();
        return this.sendWorkerMessage({
            id: messageId,
            type: 'getInfo',
            payload: {
                file: fileArrayBuffer,
                fileName: file.name
            }
        });
    }
    /**
     * Check memory usage and clean up if needed
     */
    getMemoryUsage() {
        if ('memory' in performance) {
            const memory = performance.memory;
            return {
                usedJSHeapSize: memory.usedJSHeapSize,
                totalJSHeapSize: memory.totalJSHeapSize,
                jsHeapSizeLimit: memory.jsHeapSizeLimit
            };
        }
        return {
            usedJSHeapSize: 0,
            totalJSHeapSize: 0,
            jsHeapSizeLimit: 0
        };
    }
    /**
     * Clean up resources
     */
    dispose() {
        if (this.worker) {
            this.worker.terminate();
            this.worker = null;
        }
        // Reject all pending operations
        for (const [id, operation] of this.pendingOperations) {
            operation.reject(new Error('PDF service was disposed'));
        }
        this.pendingOperations.clear();
    }
    async ensureWorkerReady() {
        if (!this.worker) {
            await this.initialize();
        }
    }
    generateMessageId() {
        return `msg_${++this.messageId}_${Date.now()}`;
    }
    sendWorkerMessage(message, progressCallback) {
        return new Promise((resolve, reject) => {
            if (!this.worker) {
                reject(new Error('Worker not initialized'));
                return;
            }
            this.pendingOperations.set(message.id, {
                resolve,
                reject,
                progressCallback
            });
            this.worker.postMessage(message);
        });
    }
    handleWorkerMessage(event) {
        const { id, type, payload } = event.data;
        const operation = this.pendingOperations.get(id);
        if (!operation) {
            console.warn(`Received message for unknown operation: ${id}`);
            return;
        }
        switch (type) {
            case 'success':
                this.pendingOperations.delete(id);
                operation.resolve(payload);
                break;
            case 'error':
                this.pendingOperations.delete(id);
                const error = new Error(payload.message);
                error.code = payload.code;
                error.name = payload.name;
                operation.reject(error);
                break;
            case 'progress':
                if (operation.progressCallback) {
                    operation.progressCallback(payload);
                }
                break;
            default:
                console.warn(`Unknown message type: ${type}`);
        }
    }
    handleWorkerError(error) {
        console.error('PDF Worker error:', error);
        // Reject all pending operations
        for (const [id, operation] of this.pendingOperations) {
            const processingError = new Error(`Worker error: ${error.message}`);
            processingError.code = 'WASM_ERROR';
            operation.reject(processingError);
        }
        this.pendingOperations.clear();
    }
    async testWorker() {
        // Simple test to ensure worker is responding
        // This would be implemented based on the actual worker setup
        return Promise.resolve();
    }
}

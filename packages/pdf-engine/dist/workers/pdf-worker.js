import { PDFProcessingEngine } from '../engine.js';
class PDFWorkerService {
    engine;
    activeProcesses = new Map();
    constructor() {
        this.engine = PDFProcessingEngine.getInstance();
        this.setupMessageHandler();
    }
    setupMessageHandler() {
        self.addEventListener('message', async (event) => {
            const { id, type, payload } = event.data;
            try {
                this.activeProcesses.set(id, true);
                switch (type) {
                    case 'process':
                        await this.handleProcess(id, payload);
                        break;
                    case 'compress':
                        await this.handleCompress(id, payload);
                        break;
                    case 'addSignature':
                        await this.handleAddSignature(id, payload);
                        break;
                    case 'addWatermark':
                        await this.handleAddWatermark(id, payload);
                        break;
                    case 'validate':
                        await this.handleValidate(id, payload);
                        break;
                    case 'getInfo':
                        await this.handleGetInfo(id, payload);
                        break;
                    default:
                        this.sendError(id, new Error(`Unknown message type: ${type}`));
                }
            }
            catch (error) {
                this.sendError(id, error instanceof Error ? error : new Error('Unknown error'));
            }
            finally {
                this.activeProcesses.delete(id);
            }
        });
    }
    async handleProcess(id, payload) {
        if (!payload.file || !payload.fileName) {
            throw new Error('File data and filename are required');
        }
        const file = new File([payload.file], payload.fileName, { type: 'application/pdf' });
        const options = payload.options || {};
        const progressCallback = (progress) => {
            this.sendProgress(id, progress);
        };
        const result = await this.engine.processDocument(file, options, progressCallback);
        this.sendSuccess(id, result);
    }
    async handleCompress(id, payload) {
        if (!payload.file || !payload.fileName || !payload.targetSize) {
            throw new Error('File data, filename, and target size are required');
        }
        const file = new File([payload.file], payload.fileName, { type: 'application/pdf' });
        const progressCallback = (progress) => {
            this.sendProgress(id, progress);
        };
        const result = await this.engine.compressToTarget(file, payload.targetSize, progressCallback);
        this.sendSuccess(id, { data: result });
    }
    async handleAddSignature(id, payload) {
        if (!payload.file || !payload.fileName || !payload.signature || !payload.position) {
            throw new Error('File data, filename, signature, and position are required');
        }
        const file = new File([payload.file], payload.fileName, { type: 'application/pdf' });
        const result = await this.engine.addSignature(file, payload.signature, payload.position);
        this.sendSuccess(id, { data: result });
    }
    async handleAddWatermark(id, payload) {
        if (!payload.file || !payload.fileName || !payload.watermark) {
            throw new Error('File data, filename, and watermark options are required');
        }
        const file = new File([payload.file], payload.fileName, { type: 'application/pdf' });
        const result = await this.engine.addWatermark(file, payload.watermark);
        this.sendSuccess(id, { data: result });
    }
    async handleValidate(id, payload) {
        if (!payload.file || !payload.fileName) {
            throw new Error('File data and filename are required');
        }
        const file = new File([payload.file], payload.fileName, { type: 'application/pdf' });
        const isValid = await this.engine.validatePDF(file);
        this.sendSuccess(id, { valid: isValid });
    }
    async handleGetInfo(id, payload) {
        if (!payload.file || !payload.fileName) {
            throw new Error('File data and filename are required');
        }
        const file = new File([payload.file], payload.fileName, { type: 'application/pdf' });
        const info = await this.engine.getPDFInfo(file);
        this.sendSuccess(id, info);
    }
    sendSuccess(id, payload) {
        const response = {
            id,
            type: 'success',
            payload
        };
        self.postMessage(response);
    }
    sendError(id, error) {
        const response = {
            id,
            type: 'error',
            payload: {
                message: error.message,
                name: error.name,
                code: error.code || 'UNKNOWN_ERROR'
            }
        };
        self.postMessage(response);
    }
    sendProgress(id, progress) {
        const response = {
            id,
            type: 'progress',
            payload: progress
        };
        self.postMessage(response);
    }
}
// Initialize the worker service
new PDFWorkerService();

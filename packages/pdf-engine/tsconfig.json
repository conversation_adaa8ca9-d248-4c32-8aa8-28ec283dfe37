{"compilerOptions": {"target": "ES2022", "lib": ["ES2022", "DOM", "WebWorker"], "module": "ESNext", "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "strict": true, "noUnusedLocals": false, "noUnusedParameters": false, "noFallthroughCasesInSwitch": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "skipLibCheck": true, "outDir": "./dist", "rootDir": "./"}, "include": ["src/**/*", "tests/**/*"], "exclude": ["node_modules", "dist"]}
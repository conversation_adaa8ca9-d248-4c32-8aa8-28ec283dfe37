/**
 * Billing Service for Hybrid Processing Architecture
 * 
 * Manages credit-based billing for server-side processing operations
 * with transparent cost calculation and user consent handling.
 */

export interface BillingTier {
  name: string;
  costPerMB: number;
  costPerPage: number;
  costPerOperation: number;
  features: string[];
}

export interface ProcessingCostEstimate {
  baseOperationCost: number;
  fileSizeCost: number;
  pageCost: number;
  complexityCost: number;
  totalCost: number;
  currency: string;
  billingTier: string;
  breakdown: {
    description: string;
    cost: number;
  }[];
}

export interface UserCredits {
  availableCredits: number;
  monthlyAllowance: number;
  usedThisMonth: number;
  tier: string;
  renewalDate: Date;
}

export interface BillingTransaction {
  id: string;
  userId?: string;
  operationType: string;
  fileSizeBytes: number;
  pageCount: number;
  processingMethod: string;
  costEstimate: ProcessingCostEstimate;
  actualCost: number;
  creditsUsed: number;
  timestamp: Date;
  status: 'pending' | 'completed' | 'failed' | 'refunded';
}

/**
 * Credit-based billing service for server processing operations
 */
export class BillingService {
  private static instance: BillingService;
  private billingTiers: BillingTier[] = [];
  private currentUserCredits: UserCredits | null = null;
  private transactions: BillingTransaction[] = [];

  private constructor() {
    this.initializeBillingTiers();
  }

  public static getInstance(): BillingService {
    if (!BillingService.instance) {
      BillingService.instance = new BillingService();
    }
    return BillingService.instance;
  }

  /**
   * Initialize billing tiers and pricing structure
   */
  private initializeBillingTiers(): void {
    this.billingTiers = [
      {
        name: 'free',
        costPerMB: 0,
        costPerPage: 0,
        costPerOperation: 0,
        features: ['basic-compression', 'client-side-only']
      },
      {
        name: 'basic',
        costPerMB: 0.01, // $0.01 per MB
        costPerPage: 0.001, // $0.001 per page
        costPerOperation: 0.05, // $0.05 per operation
        features: ['server-compression', 'format-conversion', 'ocr-basic']
      },
      {
        name: 'premium',
        costPerMB: 0.02,
        costPerPage: 0.002,
        costPerOperation: 0.10,
        features: ['advanced-ocr', 'ai-optimization', 'batch-processing', 'priority-processing']
      },
      {
        name: 'enterprise',
        costPerMB: 0.005, // Volume discount
        costPerPage: 0.0005,
        costPerOperation: 0.03,
        features: ['all-features', 'api-access', 'custom-integrations', 'dedicated-resources']
      }
    ];
  }

  /**
   * Calculate processing cost estimate
   */
  public calculateCostEstimate(
    fileSizeBytes: number,
    pageCount: number,
    processingOptions: {
      useServerProcessing: boolean;
      premiumFeatures: boolean;
      operationType: string;
      complexity: number;
    },
    userTier: string = 'free'
  ): ProcessingCostEstimate {
    const tier = this.billingTiers.find(t => t.name === userTier) || this.billingTiers[0];
    const fileSizeMB = fileSizeBytes / (1024 * 1024);

    const baseOperationCost = tier.costPerOperation;
    const fileSizeCost = fileSizeMB * tier.costPerMB;
    const pageCost = pageCount * tier.costPerPage;
    
    // Complexity multiplier (1.0 to 3.0)
    const complexityMultiplier = 1.0 + (processingOptions.complexity * 0.5);
    const complexityCost = (baseOperationCost + fileSizeCost + pageCost) * (complexityMultiplier - 1);

    const totalCost = baseOperationCost + fileSizeCost + pageCost + complexityCost;

    const breakdown = [
      {
        description: `Base ${processingOptions.operationType} operation`,
        cost: baseOperationCost
      },
      {
        description: `File processing (${fileSizeMB.toFixed(2)} MB)`,
        cost: fileSizeCost
      },
      {
        description: `Page processing (${pageCount} pages)`,
        cost: pageCost
      }
    ];

    if (complexityCost > 0) {
      breakdown.push({
        description: `Complexity adjustment (${(complexityMultiplier * 100).toFixed(0)}%)`,
        cost: complexityCost
      });
    }

    return {
      baseOperationCost,
      fileSizeCost,
      pageCost,
      complexityCost,
      totalCost,
      currency: 'USD',
      billingTier: tier.name,
      breakdown
    };
  }

  /**
   * Check if user can afford the operation
   */
  public async canAffordOperation(costEstimate: ProcessingCostEstimate): Promise<{
    canAfford: boolean;
    availableCredits: number;
    requiredCredits: number;
    shortfall: number;
  }> {
    const userCredits = await this.getUserCredits();
    const requiredCredits = Math.ceil(costEstimate.totalCost * 100); // Convert to credits (100 credits = $1)
    
    return {
      canAfford: userCredits.availableCredits >= requiredCredits,
      availableCredits: userCredits.availableCredits,
      requiredCredits,
      shortfall: Math.max(0, requiredCredits - userCredits.availableCredits)
    };
  }

  /**
   * Reserve credits for an operation
   */
  public async reserveCredits(
    costEstimate: ProcessingCostEstimate,
    operationDetails: {
      operationType: string;
      fileSizeBytes: number;
      pageCount: number;
      processingMethod: string;
    }
  ): Promise<BillingTransaction> {
    const userCredits = await this.getUserCredits();
    const requiredCredits = Math.ceil(costEstimate.totalCost * 100);

    if (userCredits.availableCredits < requiredCredits) {
      throw new Error(`Insufficient credits. Required: ${requiredCredits}, Available: ${userCredits.availableCredits}`);
    }

    const transaction: BillingTransaction = {
      id: `txn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      operationType: operationDetails.operationType,
      fileSizeBytes: operationDetails.fileSizeBytes,
      pageCount: operationDetails.pageCount,
      processingMethod: operationDetails.processingMethod,
      costEstimate,
      actualCost: costEstimate.totalCost,
      creditsUsed: requiredCredits,
      timestamp: new Date(),
      status: 'pending'
    };

    // Reserve credits (reduce available balance)
    if (this.currentUserCredits) {
      this.currentUserCredits.availableCredits -= requiredCredits;
      this.currentUserCredits.usedThisMonth += requiredCredits;
    }

    this.transactions.push(transaction);
    
    return transaction;
  }

  /**
   * Complete a transaction (confirm credit usage)
   */
  public async completeTransaction(
    transactionId: string,
    actualProcessingCost?: number
  ): Promise<BillingTransaction> {
    const transaction = this.transactions.find(t => t.id === transactionId);
    
    if (!transaction) {
      throw new Error(`Transaction not found: ${transactionId}`);
    }

    if (transaction.status !== 'pending') {
      throw new Error(`Transaction ${transactionId} is not pending (status: ${transaction.status})`);
    }

    // Update transaction status
    transaction.status = 'completed';
    
    if (actualProcessingCost !== undefined && actualProcessingCost !== transaction.actualCost) {
      // Adjust credits if actual cost was different
      const actualCreditsUsed = Math.ceil(actualProcessingCost * 100);
      const creditDifference = actualCreditsUsed - transaction.creditsUsed;
      
      if (this.currentUserCredits) {
        this.currentUserCredits.availableCredits -= creditDifference;
        this.currentUserCredits.usedThisMonth += creditDifference;
      }
      
      transaction.actualCost = actualProcessingCost;
      transaction.creditsUsed = actualCreditsUsed;
    }

    console.log(`Transaction ${transactionId} completed. Credits used: ${transaction.creditsUsed}`);
    
    return transaction;
  }

  /**
   * Refund a transaction (return credits)
   */
  public async refundTransaction(
    transactionId: string,
    refundReason: string
  ): Promise<BillingTransaction> {
    const transaction = this.transactions.find(t => t.id === transactionId);
    
    if (!transaction) {
      throw new Error(`Transaction not found: ${transactionId}`);
    }

    if (transaction.status === 'refunded') {
      throw new Error(`Transaction ${transactionId} is already refunded`);
    }

    // Refund credits
    if (this.currentUserCredits) {
      this.currentUserCredits.availableCredits += transaction.creditsUsed;
      this.currentUserCredits.usedThisMonth -= transaction.creditsUsed;
    }

    transaction.status = 'refunded';
    
    console.log(`Transaction ${transactionId} refunded. Credits returned: ${transaction.creditsUsed}. Reason: ${refundReason}`);
    
    return transaction;
  }

  /**
   * Get user credit information
   */
  public async getUserCredits(): Promise<UserCredits> {
    if (!this.currentUserCredits) {
      // In a real app, this would fetch from a database/API
      // For now, return mock data
      this.currentUserCredits = {
        availableCredits: 1000, // $10 worth of credits
        monthlyAllowance: 500,  // $5 monthly allowance
        usedThisMonth: 0,
        tier: 'basic',
        renewalDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days from now
      };
    }

    return this.currentUserCredits;
  }

  /**
   * Get billing tier information
   */
  public getBillingTier(tierName: string): BillingTier | undefined {
    return this.billingTiers.find(tier => tier.name === tierName);
  }

  /**
   * Get all available billing tiers
   */
  public getAllBillingTiers(): BillingTier[] {
    return [...this.billingTiers];
  }

  /**
   * Check if user has access to specific features
   */
  public hasFeatureAccess(feature: string, userTier: string = 'free'): boolean {
    const tier = this.getBillingTier(userTier);
    return tier?.features.includes(feature) || tier?.features.includes('all-features') || false;
  }

  /**
   * Get transaction history
   */
  public getTransactionHistory(limit: number = 10): BillingTransaction[] {
    return this.transactions
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, limit);
  }

  /**
   * Get billing analytics
   */
  public getBillingAnalytics(): {
    totalTransactions: number;
    totalCreditsUsed: number;
    totalRevenue: number;
    averageTransactionSize: number;
    topOperationTypes: { type: string; count: number }[];
    monthlyUsage: number;
  } {
    const completedTransactions = this.transactions.filter(t => t.status === 'completed');
    const totalCreditsUsed = completedTransactions.reduce((sum, t) => sum + t.creditsUsed, 0);
    const totalRevenue = completedTransactions.reduce((sum, t) => sum + t.actualCost, 0);
    
    const operationCounts = completedTransactions.reduce((acc, t) => {
      acc[t.operationType] = (acc[t.operationType] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    const topOperationTypes = Object.entries(operationCounts)
      .map(([type, count]) => ({ type, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5);

    const currentMonth = new Date().getMonth();
    const monthlyUsage = completedTransactions
      .filter(t => t.timestamp.getMonth() === currentMonth)
      .reduce((sum, t) => sum + t.creditsUsed, 0);

    return {
      totalTransactions: completedTransactions.length,
      totalCreditsUsed,
      totalRevenue,
      averageTransactionSize: completedTransactions.length > 0 ? totalCreditsUsed / completedTransactions.length : 0,
      topOperationTypes,
      monthlyUsage
    };
  }

  /**
   * Generate cost transparency report for user
   */
  public generateCostTransparencyReport(costEstimate: ProcessingCostEstimate): {
    summary: string;
    detailedBreakdown: string[];
    alternatives: {
      method: string;
      description: string;
      cost: number;
      tradeoffs: string[];
    }[];
  } {
    const summary = `This ${costEstimate.billingTier} tier operation will cost ${costEstimate.totalCost.toFixed(3)} USD (${Math.ceil(costEstimate.totalCost * 100)} credits).`;
    
    const detailedBreakdown = costEstimate.breakdown.map(
      item => `• ${item.description}: $${item.cost.toFixed(3)}`
    );

    const alternatives = [
      {
        method: 'client-side',
        description: 'Process on your device (free)',
        cost: 0,
        tradeoffs: ['Slower processing', 'Uses device resources', 'Limited to simpler operations']
      },
      {
        method: 'basic-server',
        description: 'Basic server processing',
        cost: costEstimate.totalCost * 0.7,
        tradeoffs: ['Faster than client-side', 'Standard features only', 'No priority processing']
      },
      {
        method: 'premium-server',
        description: 'Premium server processing',
        cost: costEstimate.totalCost * 1.3,
        tradeoffs: ['Fastest processing', 'All features available', 'Priority queue', 'Advanced optimization']
      }
    ];

    return {
      summary,
      detailedBreakdown,
      alternatives: alternatives.filter(alt => alt.method !== `${costEstimate.billingTier}-server`)
    };
  }

  /**
   * Cleanup old transactions and reset monthly usage if needed
   */
  public async performHousekeeping(): Promise<void> {
    const now = new Date();
    const threeMonthsAgo = new Date(now.getTime() - (90 * 24 * 60 * 60 * 1000));
    
    // Remove transactions older than 3 months
    this.transactions = this.transactions.filter(t => t.timestamp > threeMonthsAgo);
    
    // Reset monthly usage if it's a new month
    if (this.currentUserCredits && now > this.currentUserCredits.renewalDate) {
      this.currentUserCredits.usedThisMonth = 0;
      this.currentUserCredits.availableCredits += this.currentUserCredits.monthlyAllowance;
      this.currentUserCredits.renewalDate = new Date(now.getTime() + (30 * 24 * 60 * 60 * 1000));
      
      console.log('Monthly billing cycle reset. New allowance added.');
    }
  }
}
import type { PDFDocument } from 'pdf-lib';

export interface ProcessingOptions {
  targetSize?: number;
  qualityLevel?: number;
  preserveMetadata?: boolean;
  compressionType?: 'lossless' | 'lossy' | 'adaptive';
  compressionLevel?: number;
  signature?: { data: SignatureData; position: Position };
  watermark?: WatermarkOptions;
}

export interface ProcessedResult {
  data: Uint8Array;
  originalSize: number;
  processedSize: number;
  compressionRatio: number;
  processingTimeMs: number;
  metadata?: {
    pages: number;
    title?: string;
    author?: string;
    textModifications?: number;
    processor?: string;
    processingMethod?: string;
  };
}

export interface SignatureData {
  imageData: Uint8Array;
  width: number;
  height: number;
}

export interface Position {
  x: number;
  y: number;
  page: number;
}

export interface WatermarkOptions {
  text?: string;
  image?: Uint8Array;
  opacity: number;
  position: 'center' | 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
  fontSize?: number;
  color?: string;
}

// MuPDF WASM Integration Types

/**
 * MuPDF WebAssembly module interface
 * Provides production-grade PDF processing capabilities
 */
export interface MuPDFModule {
  // Document Management
  loadDocument(buffer: A<PERSON>y<PERSON>uffer, password?: string): Promise<PDFDocument>;
  saveDocument(document: PDFDocument, options: SaveOptions): Promise<ArrayBuffer>;
  
  // Text Operations (Base-Level Editing)
  extractTextBlocks(document: PDFDocument, pageNum: number): Promise<TextBlock[]>;
  getTextLayout(document: PDFDocument, pageNum: number): Promise<TextLayout>;
  modifyTextLayout(document: PDFDocument, pageNum: number, modifications: TextModification[]): Promise<void>;
  insertText(document: PDFDocument, pageNum: number, position: Position, text: string, style: TextStyle): Promise<void>;
  deleteText(document: PDFDocument, pageNum: number, textRange: TextRange): Promise<void>;
  
  // Advanced Processing
  addAnnotation(document: PDFDocument, pageNum: number, annotation: Annotation): Promise<void>;
  getFormFields(document: PDFDocument): Promise<FormField[]>;
  encrypt(document: PDFDocument, userPassword: string, ownerPassword: string, permissions: Permissions): Promise<void>;
  
  // Performance and Memory
  getPageCount(document: PDFDocument): number;
  renderPage(document: PDFDocument, pageNum: number, scale: number): Promise<ImageData>;
  
  // Cleanup
  closeDocument(document: PDFDocument): void;
  cleanup(): void;
}

/**
 * WASM Loading Strategy Configuration
 */
export interface WASMLoadingConfig {
  buildType: 'basic' | 'simd' | 'threads' | 'threads-simd';
  progressive: boolean;
  fallbackEnabled: boolean;
  memoryInitialMB: number;
  memoryMaxMB: number;
}

/**
 * Browser Capabilities Detection
 */
export interface BrowserCapabilities {
  webAssembly: boolean;
  simd: boolean;
  threads: boolean;
  sharedArrayBuffer: boolean;
  workers: boolean;
}

/**
 * Text Block Structure from MuPDF
 */
export interface TextBlock {
  id: string;
  pageNum: number;
  bbox: BoundingBox;
  text: string;
  font: FontInfo;
  fontSize: number;
  color: ColorInfo;
  transform: TransformMatrix;
}

/**
 * Text Layout Information
 */
export interface TextLayout {
  pageNum: number;
  textBlocks: TextBlock[];
  lines: TextLine[];
  characters: TextCharacter[];
}

/**
 * Text Line Information
 */
export interface TextLine {
  id: string;
  bbox: BoundingBox;
  text: string;
  characters: TextCharacter[];
  lineHeight: number;
  baseline: number;
}

/**
 * Individual Character Information
 */
export interface TextCharacter {
  char: string;
  bbox: BoundingBox;
  font: FontInfo;
  fontSize: number;
  color: ColorInfo;
}

/**
 * Text Modification Operations
 */
export interface TextModification {
  type: 'insert' | 'delete' | 'modify' | 'reflow';
  textRange?: TextRange;
  position?: Position;
  newText?: string;
  newStyle?: TextStyle;
  layoutAdjustment?: LayoutAdjustment;
}

/**
 * Text Range Selection
 */
export interface TextRange {
  startPage: number;
  endPage: number;
  startChar: number;
  endChar: number;
  bbox: BoundingBox;
}

/**
 * Text Style Definition
 */
export interface TextStyle {
  fontFamily: string;
  fontSize: number;
  fontWeight: 'normal' | 'bold';
  fontStyle: 'normal' | 'italic';
  color: ColorInfo;
  underline?: boolean;
  strikethrough?: boolean;
}

/**
 * Layout Adjustment Options
 */
export interface LayoutAdjustment {
  preserveSpacing: boolean;
  adjustLineHeight: boolean;
  reflowParagraph: boolean;
  maintainJustification: boolean;
}

/**
 * Font Information
 */
export interface FontInfo {
  name: string;
  family: string;
  type: 'Type1' | 'TrueType' | 'OpenType' | 'CID';
  embedded: boolean;
  encoding: string;
}

/**
 * Color Information
 */
export interface ColorInfo {
  colorSpace: 'RGB' | 'CMYK' | 'Gray';
  values: number[];
}

/**
 * Transform Matrix (6-element array for PDF transforms)
 */
export type TransformMatrix = [number, number, number, number, number, number];

/**
 * Bounding Box
 */
export interface BoundingBox {
  x: number;
  y: number;
  width: number;
  height: number;
}

/**
 * Annotation Types
 */
export interface Annotation {
  type: 'highlight' | 'comment' | 'stamp' | 'freehand' | 'signature';
  id: string;
  pageNum: number;
  bbox: BoundingBox;
  content?: string;
  color?: ColorInfo;
  opacity?: number;
  data?: AnnotationData;
}

/**
 * Annotation-specific data
 */
export interface AnnotationData {
  // Highlight
  highlightColor?: ColorInfo;
  blendMode?: string;
  
  // Comment/Sticky Note
  commentText?: string;
  author?: string;
  createdDate?: Date;
  
  // Freehand Drawing
  strokeWidth?: number;
  strokeColor?: ColorInfo;
  pressureSensitive?: boolean;
  paths?: DrawingPath[];
  
  // Stamp/Signature
  imageData?: ArrayBuffer;
  stampType?: string;
}

/**
 * Drawing Path for Freehand Annotations
 */
export interface DrawingPath {
  points: Point[];
  strokeWidth: number;
  color: ColorInfo;
  pressure?: number[];
}

/**
 * Point Coordinate
 */
export interface Point {
  x: number;
  y: number;
}

/**
 * Form Field Information
 */
export interface FormField {
  id: string;
  name: string;
  type: 'text' | 'checkbox' | 'radio' | 'dropdown' | 'button';
  pageNum: number;
  bbox: BoundingBox;
  value?: string | boolean;
  options?: string[];
  required?: boolean;
  readonly?: boolean;
  validation?: FormValidation;
}

/**
 * Form Field Validation
 */
export interface FormValidation {
  pattern?: string;
  minLength?: number;
  maxLength?: number;
  numeric?: boolean;
  email?: boolean;
}

/**
 * Document Permissions
 */
export interface Permissions {
  print: boolean;
  modify: boolean;
  copy: boolean;
  annotate: boolean;
  form: boolean;
  accessibility: boolean;
  assemble: boolean;
  printHighQuality: boolean;
}

/**
 * Save Options for MuPDF
 */
export interface SaveOptions {
  compress: boolean;
  linearize: boolean;
  objectStreams: boolean;
  incrementalUpdate: boolean;
  encryption?: {
    userPassword: string;
    ownerPassword: string;
    permissions: Permissions;
  };
}

/**
 * Memory Pool Configuration
 */
export interface MemoryPoolConfig {
  initialSizeMB: number;
  maxSizeMB: number;
  chunkSizeMB: number;
  lruMaxDocuments: number;
  lruMaxPages: number;
  lruPageTTLMinutes: number;
}

/**
 * Processing Decision Factors
 */
export interface ProcessingDecisionFactors {
  documentComplexity: DocumentComplexity;
  clientResources: ClientResources;
  userPreferences: UserPreferences;
  featureRequirements: FeatureRequirements;
}

/**
 * Document Complexity Analysis
 */
export interface DocumentComplexity {
  pageCount: number;
  fileSize: number;
  hasImages: boolean;
  hasText: boolean;
  hasForms: boolean;
  hasAnnotations: boolean;
  isEncrypted: boolean;
  estimatedProcessingCost: number;
}

/**
 * Client Resources Assessment
 */
export interface ClientResources {
  availableRAM: number;
  cpuCores: number;
  isMobile: boolean;
  batteryLevel?: number;
  networkSpeed?: number;
}

/**
 * User Preferences
 */
export interface UserPreferences {
  privacyFirst: boolean;
  performanceOverPrivacy: boolean;
  maxProcessingTime: number;
  acceptServerProcessing: boolean;
}

/**
 * Feature Requirements
 */
export interface FeatureRequirements {
  basicEditing: boolean;
  advancedCompression: boolean;
  ocrProcessing: boolean;
  formatConversion: boolean;
  professionalFeatures: boolean;
}

/**
 * Processing Route Decision
 */
export interface ProcessingRoute {
  method: 'client-wasm' | 'client-js' | 'server-basic' | 'server-premium';
  confidence: number;
  estimatedTime: number;
  estimatedCost: number;
  reasoning: string[];
}

export interface ProcessingProgress {
  stage: 'loading' | 'processing' | 'compressing' | 'finalizing';
  percentage: number;
  currentStep: string;
  estimatedTimeRemaining?: number;
}

export type ProcessingProgressCallback = (progress: ProcessingProgress) => void;

export interface ProcessingError extends Error {
  code: 'INVALID_PDF' | 'MEMORY_EXCEEDED' | 'PROCESSING_FAILED' | 'WASM_ERROR';
  originalError?: Error;
  processingTimeMs?: number;
}
// Import and export main service class
import { PDFService } from './pdf-service.js';
export { PDFService } from './pdf-service.js';

// Export core engine (mainly for testing/advanced use)
export { PDFProcessingEngine } from './engine.js';

// Export MuPDF WASM components (advanced use)
export { MuPDFWASMWrapper } from './wasm/mupdf-wrapper.js';
export { ProgressiveWASMLoader, WASMBuildDetector, WASMPerformanceMonitor } from './wasm/progressive-loader.js';
export { PDFTextEditor } from './text/text-editor.js';
export { IntelligentCompressionEngine } from './compression/compression-engine.js';

// Export feature-specific components
export { PDFPageManager } from './pages/page-manager.js';
export { PDFAnnotationSystem } from './annotations/annotation-system.js';
export { PDFOCRProcessor } from './ocr/ocr-processor.js';
export { PDFFormatConverter } from './conversion/format-converter.js';
export { FormProcessor } from './forms/form-processor.js';

// Export all types
export type {
  ProcessingOptions,
  ProcessedResult,
  SignatureData,
  Position,
  WatermarkOptions,
  ProcessingProgress,
  ProcessingProgressCallback,
  ProcessingError,
  // MuPDF WASM Types
  MuPDFModule,
  WASMLoadingConfig,
  BrowserCapabilities,
  TextBlock,
  TextLayout,
  TextModification,
  TextRange,
  TextStyle,
  Annotation,
  FormField,
  FormValidation,
  Permissions,
  SaveOptions,
  ProcessingRoute,
  ProcessingDecisionFactors
} from './types/processing.js';

// Export worker types (for advanced integration)
export type {
  WorkerMessage,
  WorkerResponse
} from './workers/pdf-worker.js';

// Convenience function to get the singleton service instance
export const getPDFService = () => PDFService.getInstance();
import { 
  ProcessingDecisionFactors, 
  ProcessingRoute, 
  DocumentComplexity, 
  ClientResources, 
  UserPreferences, 
  FeatureRequirements,
  ProcessingOptions,
  ProcessedResult,
  ProcessingProgressCallback,
  BrowserCapabilities
} from '../types/processing';
import { MuPDFWASMWrapper } from '../wasm/mupdf-wrapper';

/**
 * Processing Router for Hybrid Client/Server Architecture
 * 
 * Intelligently routes PDF processing operations between:
 * 1. Client-side WASM (MuPDF) - High performance, privacy-first
 * 2. Client-side JavaScript (PDF-lib) - Reliable fallback
 * 3. Server-side processing - Complex operations, premium features
 */
export class ProcessingRouter {
  private static instance: ProcessingRouter;
  private wasmWrapper: MuPDFWASMWrapper | null = null;
  private javascriptProcessor: JavaScriptPDFProcessor | null = null;
  private serverProcessor: ServerPDFProcessor | null = null;
  private routingDecisions: ProcessingRoute[] = [];

  private constructor() {}

  public static getInstance(): ProcessingRouter {
    if (!ProcessingRouter.instance) {
      ProcessingRouter.instance = new ProcessingRouter();
    }
    return ProcessingRouter.instance;
  }

  /**
   * Initialize the processing router with all available processors
   */
  public async initialize(): Promise<void> {
    try {
      console.log('Initializing Processing Router...');

      // Initialize WASM processor (best performance)
      try {
        this.wasmWrapper = new MuPDFWASMWrapper({
          buildType: 'threads-simd',
          progressive: true,
          fallbackEnabled: true,
          memoryInitialMB: 64,
          memoryMaxMB: 2048
        });
        await this.wasmWrapper.initialize();
        console.log('WASM processor initialized successfully');
      } catch (error) {
        console.warn('WASM processor initialization failed:', error);
        this.wasmWrapper = null;
      }

      // Initialize JavaScript processor (reliable fallback)
      this.javascriptProcessor = new JavaScriptPDFProcessor();
      await this.javascriptProcessor.initialize();
      console.log('JavaScript processor initialized');

      // Initialize server processor (premium features)
      this.serverProcessor = new ServerPDFProcessor();
      console.log('Server processor initialized');

      console.log('Processing Router initialization complete');

    } catch (error) {
      console.error('Processing Router initialization failed:', error);
      throw error;
    }
  }

  /**
   * Route processing request to optimal processor
   */
  public async processDocument(
    file: File,
    options: ProcessingOptions,
    progressCallback?: ProcessingProgressCallback
  ): Promise<ProcessedResult> {
    const startTime = performance.now();

    try {
      // Analyze processing requirements
      const complexity = await this.analyzeDocumentComplexity(file, options);
      const clientResources = this.assessClientResources();
      const userPreferences = this.getUserPreferences();
      const featureRequirements = this.analyzeFeatureRequirements(options);

      // Make routing decision
      const route = this.makeRoutingDecision({
        documentComplexity: complexity,
        clientResources,
        userPreferences,
        featureRequirements
      });

      // Store decision for analytics
      this.routingDecisions.push(route);
      
      console.log('Routing decision made:', route);

      // Notify user of processing method if relevant
      if (route.method !== 'client-wasm' && progressCallback) {
        progressCallback({
          stage: 'loading',
          percentage: 0,
          currentStep: this.getRoutingMessage(route)
        });
      }

      // Route to appropriate processor
      let result: ProcessedResult;

      switch (route.method) {
        case 'client-wasm':
          result = await this.processWithWASM(file, options, progressCallback);
          break;
          
        case 'client-js':
          result = await this.processWithJavaScript(file, options, progressCallback);
          break;
          
        case 'server-basic':
          result = await this.processWithServer(file, options, progressCallback, false);
          break;
          
        case 'server-premium':
          result = await this.processWithServer(file, options, progressCallback, true);
          break;
          
        default:
          throw new Error(`Unknown processing method: ${route.method}`);
      }

      // Record performance metrics
      const processingTime = performance.now() - startTime;
      this.recordPerformanceMetrics(route, processingTime, true);

      return {
        ...result,
        processingTimeMs: processingTime,
        metadata: {
          pages: result.metadata?.pages || 1, // Ensure pages is defined
          ...result.metadata,
          processingMethod: route.method
        },
        routingReasoning: route.reasoning
      };

    } catch (error) {
      const processingTime = performance.now() - startTime;
      
      // Try fallback processing
      if (error instanceof ProcessingError && error.code !== 'ALL_METHODS_FAILED') {
        console.warn('Primary processing failed, attempting fallback:', error);
        
        try {
          const fallbackResult = await this.processWithFallback(file, options, progressCallback);
          this.recordPerformanceMetrics(null, processingTime, true, 'fallback-success');
          return fallbackResult;
        } catch (fallbackError) {
          this.recordPerformanceMetrics(null, processingTime, false, 'all-failed');
          throw new ProcessingError(`All processing methods failed. Original: ${error.message}, Fallback: ${fallbackError}`, 'ALL_METHODS_FAILED');
        }
      }

      this.recordPerformanceMetrics(null, processingTime, false);
      throw error;
    }
  }

  /**
   * Analyze document complexity for routing decisions
   */
  private async analyzeDocumentComplexity(file: File, options: ProcessingOptions): Promise<DocumentComplexity> {
    try {
      // Basic analysis from file properties
      const complexity: DocumentComplexity = {
        pageCount: 0,
        fileSize: file.size,
        hasImages: false,
        hasText: false,
        hasForms: false,
        hasAnnotations: false,
        isEncrypted: false,
        estimatedProcessingCost: 1
      };

      // Quick analysis if WASM is available
      if (this.wasmWrapper?.isReady) {
        try {
          const buffer = await file.arrayBuffer();
          const tempDoc = await this.wasmWrapper.loadDocument(buffer);
          
          complexity.pageCount = this.wasmWrapper.getPageCount(tempDoc);
          
          // Estimate complexity based on file size and page count
          const sizePerPage = file.size / Math.max(complexity.pageCount, 1);
          
          if (sizePerPage > 500000) { // > 500KB per page suggests images
            complexity.hasImages = true;
            complexity.estimatedProcessingCost += 2;
          }
          
          if (complexity.pageCount > 100) {
            complexity.estimatedProcessingCost += 1;
          }
          
          if (file.size > 50 * 1024 * 1024) { // > 50MB
            complexity.estimatedProcessingCost += 2;
          }

          this.wasmWrapper.closeDocument(tempDoc);
        } catch (error) {
          console.warn('Document analysis failed, using basic metrics:', error);
        }
      }

      // Analyze based on options
      if (options.targetSize && options.targetSize < file.size * 0.5) {
        complexity.estimatedProcessingCost += 2; // Aggressive compression
      }

      if (options.watermark || options.signature) {
        complexity.estimatedProcessingCost += 1;
      }

      return complexity;

    } catch (error) {
      console.warn('Document complexity analysis failed:', error);
      
      // Return safe defaults
      return {
        pageCount: 10, // Conservative estimate
        fileSize: file.size,
        hasImages: file.size > 1024 * 1024, // Assume images if > 1MB
        hasText: true,
        hasForms: false,
        hasAnnotations: false,
        isEncrypted: false,
        estimatedProcessingCost: Math.min(Math.ceil(file.size / (10 * 1024 * 1024)), 5) // Scale with file size
      };
    }
  }

  /**
   * Assess client device resources
   */
  private assessClientResources(): ClientResources {
    const resources: ClientResources = {
      availableRAM: 4096, // Default assumption: 4GB
      cpuCores: navigator.hardwareConcurrency || 4,
      isMobile: /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent),
      batteryLevel: undefined,
      networkSpeed: undefined
    };

    // Detect available memory (Chrome/Edge)
    if ('memory' in performance) {
      const memInfo = (performance as any).memory;
      if (memInfo.usedJSHeapSize && memInfo.totalJSHeapSize) {
        resources.availableRAM = Math.max(
          (memInfo.totalJSHeapSize - memInfo.usedJSHeapSize) / (1024 * 1024), // Available in MB
          512 // Minimum assumption
        );
      }
    }

    // Battery API (if available)
    if ('getBattery' in navigator) {
      (navigator as any).getBattery().then((battery: any) => {
        resources.batteryLevel = battery.level;
      }).catch(() => {
        // Battery API not available or denied
      });
    }

    // Network speed estimation (basic)
    if ('connection' in navigator) {
      const connection = (navigator as any).connection;
      if (connection.downlink) {
        resources.networkSpeed = connection.downlink * 1000; // Convert to kbps
      }
    }

    return resources;
  }

  /**
   * Get user preferences for processing routing
   */
  private getUserPreferences(): UserPreferences {
    // In a real app, this would come from user settings
    // For now, return privacy-first defaults
    return {
      privacyFirst: true,
      performanceOverPrivacy: false,
      maxProcessingTime: 60000, // 60 seconds
      acceptServerProcessing: false
    };
  }

  /**
   * Analyze feature requirements from processing options
   */
  private analyzeFeatureRequirements(options: ProcessingOptions): FeatureRequirements {
    return {
      basicEditing: !!(options.watermark || options.signature),
      advancedCompression: !!(options.targetSize && options.targetSize > 0),
      ocrProcessing: false, // Would be detected from options in real implementation
      formatConversion: false, // Would be detected from options
      professionalFeatures: false // Advanced features requiring server processing
    };
  }

  /**
   * Make intelligent routing decision based on all factors
   */
  private makeRoutingDecision(factors: ProcessingDecisionFactors): ProcessingRoute {
    const { documentComplexity, clientResources, userPreferences, featureRequirements } = factors;
    
    let confidence = 100;
    let method: ProcessingRoute['method'] = 'client-wasm';
    let estimatedTime = 5000; // Default 5 seconds
    let estimatedCost = 0;
    const reasoning: string[] = [];

    // Privacy-first decision making
    if (userPreferences.privacyFirst) {
      reasoning.push('Privacy-first preference detected');
      
      // Prefer client-side processing
      if (this.wasmWrapper?.isReady && documentComplexity.estimatedProcessingCost <= 3) {
        method = 'client-wasm';
        reasoning.push('Using WASM for optimal privacy and performance');
        estimatedTime = documentComplexity.estimatedProcessingCost * 2000;
      } else if (documentComplexity.estimatedProcessingCost <= 5) {
        method = 'client-js';
        reasoning.push('Using JavaScript fallback for privacy');
        estimatedTime = documentComplexity.estimatedProcessingCost * 4000;
        confidence -= 20;
      } else {
        // Complex document but privacy-first
        if (userPreferences.acceptServerProcessing) {
          method = 'server-basic';
          reasoning.push('Complex document requires server processing');
          estimatedCost = documentComplexity.estimatedProcessingCost * 0.1;
          confidence -= 30;
        } else {
          method = 'client-js';
          reasoning.push('Forcing client-side processing despite complexity');
          estimatedTime = documentComplexity.estimatedProcessingCost * 6000;
          confidence -= 40;
        }
      }
    } else if (userPreferences.performanceOverPrivacy) {
      reasoning.push('Performance-first preference detected');
      
      if (documentComplexity.estimatedProcessingCost > 4) {
        method = 'server-premium';
        reasoning.push('Using server processing for optimal performance');
        estimatedTime = 2000;
        estimatedCost = documentComplexity.estimatedProcessingCost * 0.2;
      } else if (this.wasmWrapper?.isReady) {
        method = 'client-wasm';
        reasoning.push('Using WASM for best client-side performance');
        estimatedTime = documentComplexity.estimatedProcessingCost * 1500;
      } else {
        method = 'client-js';
        reasoning.push('WASM not available, using JavaScript');
        estimatedTime = documentComplexity.estimatedProcessingCost * 3000;
        confidence -= 25;
      }
    }

    // Resource constraints
    if (clientResources.isMobile && documentComplexity.fileSize > 10 * 1024 * 1024) {
      if (method.startsWith('client')) {
        reasoning.push('Mobile device with large file - considering server processing');
        if (userPreferences.acceptServerProcessing) {
          method = 'server-basic';
          estimatedCost = documentComplexity.estimatedProcessingCost * 0.1;
        } else {
          confidence -= 30;
        }
      }
    }

    if (clientResources.availableRAM < 1024 && documentComplexity.fileSize > 50 * 1024 * 1024) {
      reasoning.push('Low memory device with large file');
      confidence -= 25;
    }

    // Feature requirements
    if (featureRequirements.professionalFeatures) {
      method = 'server-premium';
      reasoning.push('Professional features require server processing');
      estimatedCost = Math.max(estimatedCost, 0.5);
    }

    // Time constraints
    if (estimatedTime > userPreferences.maxProcessingTime) {
      if (userPreferences.acceptServerProcessing) {
        method = method.includes('server') ? method : 'server-basic';
        reasoning.push('Time constraint requires server processing');
        estimatedTime = Math.min(estimatedTime * 0.5, 10000);
        estimatedCost = Math.max(estimatedCost, 0.1);
      } else {
        reasoning.push('Processing may exceed time limit');
        confidence -= 20;
      }
    }

    // Final confidence adjustment
    confidence = Math.max(confidence, 10); // Minimum 10% confidence

    return {
      method,
      confidence,
      estimatedTime,
      estimatedCost,
      reasoning
    };
  }

  /**
   * Process with WASM (highest performance)
   */
  private async processWithWASM(
    file: File,
    options: ProcessingOptions,
    progressCallback?: ProcessingProgressCallback
  ): Promise<ProcessedResult> {
    if (!this.wasmWrapper?.isReady) {
      throw new ProcessingError('WASM processor not available', 'WASM_NOT_READY');
    }

    try {
      progressCallback?.({
        stage: 'loading',
        percentage: 0,
        currentStep: 'Loading document with WASM...'
      });

      const buffer = await file.arrayBuffer();
      const document = await this.wasmWrapper.loadDocument(buffer);

      progressCallback?.({
        stage: 'processing',
        percentage: 30,
        currentStep: 'Processing with MuPDF WASM...'
      });

      // Perform processing based on options
      if (options.targetSize) {
        // Advanced compression
        await this.performWASMCompression(document, options, progressCallback);
      }

      if (options.watermark) {
        await this.performWASMWatermark(document, options, progressCallback);
      }

      if (options.signature) {
        await this.performWASMSignature(document, options, progressCallback);
      }

      progressCallback?.({
        stage: 'finalizing',
        percentage: 90,
        currentStep: 'Saving document...'
      });

      const result = await this.wasmWrapper.saveDocument(document, {
        compress: true,
        linearize: false,
        objectStreams: true,
        incrementalUpdate: false
      });

      // Cleanup
      this.wasmWrapper.closeDocument(document);

      progressCallback?.({
        stage: 'finalizing',
        percentage: 100,
        currentStep: 'Complete!'
      });

      return {
        data: new Uint8Array(result),
        originalSize: file.size,
        processedSize: result.byteLength,
        compressionRatio: file.size / result.byteLength,
        processingTimeMs: 0, // Will be set by caller
        metadata: {
          pages: this.wasmWrapper.getPageCount(document),
          processor: 'MuPDF WASM'
        }
      };

    } catch (error) {
      throw new ProcessingError(`WASM processing failed: ${error}`, 'WASM_PROCESSING_FAILED', error);
    }
  }

  /**
   * Process with JavaScript fallback
   */
  private async processWithJavaScript(
    file: File,
    options: ProcessingOptions,
    progressCallback?: ProcessingProgressCallback
  ): Promise<ProcessedResult> {
    if (!this.javascriptProcessor) {
      throw new ProcessingError('JavaScript processor not available', 'JS_NOT_READY');
    }

    return this.javascriptProcessor.processDocument(file, options, progressCallback);
  }

  /**
   * Process with server
   */
  private async processWithServer(
    file: File,
    options: ProcessingOptions,
    progressCallback?: ProcessingProgressCallback,
    premium: boolean = false
  ): Promise<ProcessedResult> {
    if (!this.serverProcessor) {
      throw new ProcessingError('Server processor not available', 'SERVER_NOT_READY');
    }

    return this.serverProcessor.processDocument(file, options, progressCallback, premium);
  }

  /**
   * Process with fallback chain
   */
  private async processWithFallback(
    file: File,
    options: ProcessingOptions,
    progressCallback?: ProcessingProgressCallback
  ): Promise<ProcessedResult> {
    const fallbackChain = [
      () => this.processWithJavaScript(file, options, progressCallback),
      () => this.processWithServer(file, options, progressCallback, false)
    ];

    for (const fallback of fallbackChain) {
      try {
        return await fallback();
      } catch (error) {
        console.warn('Fallback method failed:', error);
        continue;
      }
    }

    throw new ProcessingError('All fallback methods failed', 'ALL_FALLBACKS_FAILED');
  }

  // Helper methods for WASM processing

  private async performWASMCompression(
    document: any,
    options: ProcessingOptions,
    progressCallback?: ProcessingProgressCallback
  ): Promise<void> {
    // Implement WASM-specific compression logic
    progressCallback?.({
      stage: 'compressing',
      percentage: 50,
      currentStep: 'Applying advanced compression...'
    });
  }

  private async performWASMWatermark(
    document: any,
    options: ProcessingOptions,
    progressCallback?: ProcessingProgressCallback
  ): Promise<void> {
    // Implement WASM-specific watermark logic
    progressCallback?.({
      stage: 'processing',
      percentage: 60,
      currentStep: 'Adding watermark...'
    });
  }

  private async performWASMSignature(
    document: any,
    options: ProcessingOptions,
    progressCallback?: ProcessingProgressCallback
  ): Promise<void> {
    // Implement WASM-specific signature logic
    progressCallback?.({
      stage: 'processing',
      percentage: 70,
      currentStep: 'Adding signature...'
    });
  }

  // Utility methods

  private getRoutingMessage(route: ProcessingRoute): string {
    switch (route.method) {
      case 'client-wasm':
        return 'Using high-performance WASM processing...';
      case 'client-js':
        return 'Using JavaScript processing for privacy...';
      case 'server-basic':
        return 'Using server processing for complex operations...';
      case 'server-premium':
        return 'Using premium server processing for best results...';
      default:
        return 'Processing document...';
    }
  }

  private recordPerformanceMetrics(
    route: ProcessingRoute | null,
    processingTime: number,
    success: boolean,
    notes?: string
  ): void {
    // Record metrics for analytics and optimization
    console.log('Performance metrics:', {
      method: route?.method || 'unknown',
      processingTime,
      success,
      notes
    });
  }

  /**
   * Get routing statistics
   */
  public getRoutingStats(): any {
    const stats = {
      totalDecisions: this.routingDecisions.length,
      methodCounts: {} as Record<string, number>,
      avgConfidence: 0,
      avgCost: 0
    };

    for (const decision of this.routingDecisions) {
      stats.methodCounts[decision.method] = (stats.methodCounts[decision.method] || 0) + 1;
      stats.avgConfidence += decision.confidence;
      stats.avgCost += decision.estimatedCost;
    }

    if (this.routingDecisions.length > 0) {
      stats.avgConfidence /= this.routingDecisions.length;
      stats.avgCost /= this.routingDecisions.length;
    }

    return stats;
  }

  /**
   * Clear routing history
   */
  public clearRoutingHistory(): void {
    this.routingDecisions.length = 0;
  }

  /**
   * Enhanced processing with billing integration and performance monitoring
   * AC: 9.3 - Server-side processing fallback with credit-based billing integration
   * AC: 9.5 - Performance benchmarking system to measure 10x advantage vs competitors
   */
  public async processDocumentWithBilling(
    file: File,
    options: ProcessingOptions,
    progressCallback?: ProcessingProgressCallback
  ): Promise<ProcessedResult & {
    billingTransaction?: any;
    performanceBenchmark?: any;
    costTransparency?: any;
  }> {
    const startTime = performance.now();

    try {
      // Import billing and benchmarking services dynamically
      const { BillingService } = await import('../billing/billing-service');
      const { PerformanceBenchmarker } = await import('../performance/performance-benchmarker');
      
      const billingService = BillingService.getInstance();
      const benchmarker = PerformanceBenchmarker.getInstance();

      // Analyze processing requirements
      const complexity = await this.analyzeDocumentComplexity(file, options);
      const clientResources = this.assessClientResources();
      const userPreferences = this.getUserPreferences();
      const featureRequirements = this.analyzeFeatureRequirements(options);

      // Make routing decision
      const route = this.makeRoutingDecision({
        documentComplexity: complexity,
        clientResources,
        userPreferences,
        featureRequirements
      });

      let billingTransaction: any = undefined;
      let costTransparency: any = undefined;

      // Handle billing for server processing
      if (route.method.includes('server')) {
        const costEstimate = billingService.calculateCostEstimate(
          file.size,
          complexity.pageCount,
          {
            useServerProcessing: true,
            premiumFeatures: route.method === 'server-premium',
            operationType: this.getOperationType(options),
            complexity: complexity.estimatedProcessingCost
          },
          userPreferences.billingTier || 'basic'
        );

        // Generate cost transparency report
        costTransparency = billingService.generateCostTransparencyReport(costEstimate);

        // Check if user can afford the operation
        const affordability = await billingService.canAffordOperation(costEstimate);
        
        if (!affordability.canAfford) {
          throw new Error(
            `Insufficient credits for server processing. Required: ${affordability.requiredCredits}, Available: ${affordability.availableCredits}. Shortfall: ${affordability.shortfall} credits.`
          );
        }

        // Inform user about cost (if callback provided)
        if (progressCallback) {
          progressCallback({
            stage: 'loading',
            percentage: 0,
            currentStep: `${costTransparency.summary} - Processing will begin in 3 seconds...`
          });
          
          // Give user time to see cost information
          await new Promise(resolve => setTimeout(resolve, 3000));
        }

        // Reserve credits before processing
        billingTransaction = await billingService.reserveCredits(costEstimate, {
          operationType: this.getOperationType(options),
          fileSizeBytes: file.size,
          pageCount: complexity.pageCount,
          processingMethod: route.method
        });
      }

      try {
        // Store decision for analytics
        this.routingDecisions.push(route);
        
        // Notify user of processing method
        if (progressCallback) {
          progressCallback({
            stage: 'loading',
            percentage: 5,
            currentStep: this.getRoutingMessage(route)
          });
        }

        // Route to appropriate processor
        let result: ProcessedResult;

        switch (route.method) {
          case 'client-wasm':
            result = await this.processWithWASM(file, options, progressCallback);
            break;
            
          case 'client-js':
            result = await this.processWithJavaScript(file, options, progressCallback);
            break;
            
          case 'server-basic':
            result = await this.processWithServer(file, options, progressCallback, false);
            break;
            
          case 'server-premium':
            result = await this.processWithServer(file, options, progressCallback, true);
            break;
            
          default:
            throw new Error(`Unknown processing method: ${route.method}`);
        }

        const totalProcessingTime = performance.now() - startTime;

        // Complete billing transaction if server processing was used
        if (billingTransaction) {
          await billingService.completeTransaction(billingTransaction.id, route.estimatedCost);
        }

        // Record performance benchmark
        const memoryUsage = this.getMemoryUsage();
        await benchmarker.monitorOperation(
          this.getOperationType(options),
          file.size,
          complexity.pageCount,
          totalProcessingTime,
          memoryUsage,
          route.method
        );

        // Get performance benchmark for this operation
        const performanceBenchmark = benchmarker.getPerformanceStats();

        return {
          ...result,
          processingTimeMs: totalProcessingTime,
          metadata: {
            ...result.metadata,
            processingMethod: route.method,
            performanceImprovement: performanceBenchmark.averageSpeedImprovement,
            meets10xTarget: performanceBenchmark.percentage10xSuccess >= 80
          },
          routingReasoning: route.reasoning,
          billingTransaction,
          performanceBenchmark: {
            speedImprovement: performanceBenchmark.averageSpeedImprovement,
            meets10xTarget: performanceBenchmark.percentage10xSuccess >= 80,
            overallScore: performanceBenchmark.averageOverallScore
          },
          costTransparency
        };

      } catch (processingError) {
        // Refund credits if server processing failed
        if (billingTransaction) {
          await billingService.refundTransaction(billingTransaction.id, `Processing failed: ${processingError}`);
        }
        
        // Try fallback processing
        console.warn('Primary processing failed, attempting fallback:', processingError);
        
        const fallbackResult = await this.processWithFallback(file, options, progressCallback);
        const totalProcessingTime = performance.now() - startTime;

        return {
          ...fallbackResult,
          processingTimeMs: totalProcessingTime,
          metadata: {
            ...fallbackResult.metadata,
            processingMethod: 'fallback',
            fallbackReason: processingError instanceof Error ? processingError.message : 'Unknown error'
          }
        };
      }

    } catch (error) {
      const processingTime = performance.now() - startTime;
      
      const processingError: ProcessingError = new Error(
        `Enhanced processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      ) as ProcessingError;
      processingError.code = 'ENHANCED_PROCESSING_FAILED';
      processingError.originalError = error instanceof Error ? error : undefined;
      processingError.processingTimeMs = processingTime;
      
      throw processingError;
    }
  }

  /**
   * Get transparent processing method selection with cost indication
   * AC: 9.4 - Processing method transparency with user choice and cost indication
   */
  public async getProcessingOptions(
    file: File,
    baseOptions: ProcessingOptions
  ): Promise<{
    recommended: {
      method: string;
      description: string;
      estimatedTime: number;
      cost: number;
      reasoning: string[];
    };
    alternatives: Array<{
      method: string;
      description: string;
      estimatedTime: number;
      cost: number;
      pros: string[];
      cons: string[];
      available: boolean;
    }>;
    userPreferences: any;
  }> {
    try {
      // Import billing service
      const { BillingService } = await import('../billing/billing-service');
      const billingService = BillingService.getInstance();

      // Analyze document and make routing decision
      const complexity = await this.analyzeDocumentComplexity(file, baseOptions);
      const clientResources = this.assessClientResources();
      const userPreferences = this.getUserPreferences();
      const featureRequirements = this.analyzeFeatureRequirements(baseOptions);

      const recommendedRoute = this.makeRoutingDecision({
        documentComplexity: complexity,
        clientResources,
        userPreferences,
        featureRequirements
      });

      // Calculate costs for all available methods
      const operationType = this.getOperationType(baseOptions);
      const userTier = userPreferences.billingTier || 'basic';

      const alternatives = [
        {
          method: 'client-wasm',
          description: 'Process on your device using WebAssembly (fastest client-side)',
          available: this.wasmWrapper?.isReady || false,
          pros: ['100% private', 'No additional cost', 'Fast processing'],
          cons: ['Uses device resources', 'May be slower for complex documents']
        },
        {
          method: 'client-js',
          description: 'Process on your device using JavaScript (compatible fallback)',
          available: !!this.javascriptProcessor,
          pros: ['100% private', 'No additional cost', 'Works on all devices'],
          cons: ['Slower processing', 'Limited features', 'Uses device resources']
        },
        {
          method: 'server-basic',
          description: 'Server processing with standard features',
          available: !!this.serverProcessor && userPreferences.acceptServerProcessing,
          pros: ['Faster processing', 'Handles complex documents', 'Doesn\'t use device resources'],
          cons: ['Costs credits', 'Requires internet', 'Data leaves device']
        },
        {
          method: 'server-premium',
          description: 'Premium server processing with advanced features',
          available: !!this.serverProcessor && userPreferences.acceptServerProcessing && billingService.hasFeatureAccess('premium-features', userTier),
          pros: ['Fastest processing', 'All features available', 'Best quality results', 'Priority processing'],
          cons: ['Higher cost', 'Requires internet', 'Data leaves device']
        }
      ];

      // Calculate costs and times for each method
      const processedAlternatives = await Promise.all(
        alternatives.map(async (alt) => {
          let cost = 0;
          let estimatedTime = 5000; // Default 5 seconds

          if (alt.method.includes('server')) {
            const costEstimate = billingService.calculateCostEstimate(
              file.size,
              complexity.pageCount,
              {
                useServerProcessing: true,
                premiumFeatures: alt.method === 'server-premium',
                operationType,
                complexity: complexity.estimatedProcessingCost
              },
              userTier
            );
            cost = costEstimate.totalCost;
            estimatedTime = complexity.estimatedProcessingCost * (alt.method === 'server-premium' ? 1000 : 2000);
          } else {
            // Client-side processing time estimation
            estimatedTime = complexity.estimatedProcessingCost * (alt.method === 'client-wasm' ? 2000 : 4000);
          }

          return {
            ...alt,
            cost,
            estimatedTime
          };
        })
      );

      return {
        recommended: {
          method: recommendedRoute.method,
          description: this.getMethodDescription(recommendedRoute.method),
          estimatedTime: recommendedRoute.estimatedTime,
          cost: recommendedRoute.estimatedCost,
          reasoning: recommendedRoute.reasoning
        },
        alternatives: processedAlternatives,
        userPreferences
      };

    } catch (error) {
      throw new Error(`Failed to get processing options: ${error}`);
    }
  }

  /**
   * Update user preferences for processing routing
   */
  public updateUserPreferences(preferences: Partial<{
    privacyFirst: boolean;
    performanceOverPrivacy: boolean;
    maxProcessingTime: number;
    acceptServerProcessing: boolean;
    billingTier: string;
    autoSelectOptimal: boolean;
  }>): void {
    // In a real app, this would persist to user settings
    // For now, store in memory (would be overwritten on reload)
    Object.assign(this.getUserPreferences(), preferences);
    console.log('User preferences updated:', preferences);
  }

  // Private helper methods for enhanced functionality

  private getOperationType(options: ProcessingOptions): string {
    if (options.targetSize) return 'compression';
    if (options.watermark) return 'watermark';
    if (options.signature) return 'signature';
    return 'basic-processing';
  }

  private getMethodDescription(method: string): string {
    switch (method) {
      case 'client-wasm':
        return 'High-performance WebAssembly processing on your device';
      case 'client-js':
        return 'JavaScript processing on your device (privacy-first)';
      case 'server-basic':
        return 'Server processing with standard features and speed';
      case 'server-premium':
        return 'Premium server processing with advanced features and priority handling';
      default:
        return 'PDF processing';
    }
  }

  private getMemoryUsage(): number {
    if ('memory' in performance) {
      const memInfo = (performance as any).memory;
      return memInfo.usedJSHeapSize / (1024 * 1024); // Convert to MB
    }
    return 0; // Fallback if memory API not available
  }

  /**
   * Enhanced user preferences with billing and performance options
   */
  private getUserPreferences(): any {
    // In a real app, this would come from user settings/database
    // Enhanced with billing and performance preferences
    return {
      privacyFirst: true,
      performanceOverPrivacy: false,
      maxProcessingTime: 60000, // 60 seconds
      acceptServerProcessing: false,
      billingTier: 'basic',
      autoSelectOptimal: true,
      maxCostPerOperation: 0.50, // $0.50 max per operation
      preferredPaymentMethod: 'credits'
    };
  }

  /**
   * Get comprehensive performance and billing analytics
   */
  public async getAnalytics(): Promise<{
    performance: any;
    billing: any;
    routing: any;
    recommendations: string[];
  }> {
    try {
      const { BillingService } = await import('../billing/billing-service');
      const { PerformanceBenchmarker } = await import('../performance/performance-benchmarker');
      
      const billingService = BillingService.getInstance();
      const benchmarker = PerformanceBenchmarker.getInstance();

      const performanceStats = benchmarker.getPerformanceStats();
      const billingAnalytics = billingService.getBillingAnalytics();
      const routingStats = this.getRoutingStats();

      const recommendations = this.generateRecommendations(performanceStats, billingAnalytics, routingStats);

      return {
        performance: performanceStats,
        billing: billingAnalytics,
        routing: routingStats,
        recommendations
      };
    } catch (error) {
      throw new Error(`Failed to get analytics: ${error}`);
    }
  }

  private generateRecommendations(performanceStats: any, billingAnalytics: any, routingStats: any): string[] {
    const recommendations: string[] = [];

    // Performance recommendations
    if (performanceStats.percentage10xSuccess < 80) {
      recommendations.push('Consider optimizing WASM processing for better 10x performance achievement');
    }

    if (performanceStats.performanceTrend === 'declining') {
      recommendations.push('Recent performance decline detected - investigate potential causes');
    }

    // Billing recommendations
    if (billingAnalytics.monthlyUsage > billingAnalytics.totalCreditsUsed * 0.8) {
      recommendations.push('High monthly usage detected - consider upgrading billing tier for better rates');
    }

    // Routing recommendations
    if (routingStats.methodCounts['server-premium'] > routingStats.methodCounts['client-wasm']) {
      recommendations.push('Heavy server usage detected - optimize client-side processing to reduce costs');
    }

    if (recommendations.length === 0) {
      recommendations.push('System is performing optimally - maintain current configuration');
    }

    return recommendations;
  }
}

// Supporting classes

class ProcessingError extends Error {
  constructor(
    message: string,
    public code: string,
    public originalError?: any
  ) {
    super(message);
    this.name = 'ProcessingError';
  }
}

class JavaScriptPDFProcessor {
  public async initialize(): Promise<void> {
    // Initialize PDF-lib or other JavaScript PDF processor
  }

  public async processDocument(
    file: File,
    options: ProcessingOptions,
    progressCallback?: ProcessingProgressCallback
  ): Promise<ProcessedResult> {
    // Implement JavaScript PDF processing
    // This would use the existing PDFProcessingEngine
    throw new Error('JavaScript processor not implemented yet');
  }
}

class ServerPDFProcessor {
  public async processDocument(
    file: File,
    options: ProcessingOptions,
    progressCallback?: ProcessingProgressCallback,
    premium: boolean = false
  ): Promise<ProcessedResult> {
    // Implement server-side PDF processing via API calls
    throw new Error('Server processor not implemented yet');
  }
}
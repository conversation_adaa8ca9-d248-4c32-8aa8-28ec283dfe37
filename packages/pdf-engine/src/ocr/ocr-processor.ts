import {
  ProcessingProgressCallback,
  BoundingBox,
  Position
} from '../types/processing';

/**
 * Advanced OCR Processing System with Tesseract.js Integration
 * 
 * Provides comprehensive OCR capabilities including:
 * - Multi-language OCR processing with confidence scoring
 * - Scanned document detection and preprocessing
 * - Searchable PDF creation with invisible text overlay
 * - OCR result correction tools and manual editing integration
 * - Performance optimization with worker threads
 */
export class PDFOCRProcessor {
  private document: any;
  private wasmWrapper: any;
  private tesseractWorker: any = null;
  private isInitialized = false;
  private supportedLanguages: string[] = [];
  private ocrCache = new Map<string, OCRResult>();
  private preprocessingEngine: ImagePreprocessor;

  constructor(document: any, wasmWrapper: any) {
    this.document = document;
    this.wasmWrapper = wasmWrapper;
    this.preprocessingEngine = new ImagePreprocessor();
  }

  /**
   * Initialize OCR processor with Tesseract.js
   */
  public async initialize(options: OCRInitOptions = {}): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      console.log('Initializing OCR processor with Tesseract.js...');

      // Dynamically import Tesseract.js to avoid bundling issues
      const { createWorker } = await import('tesseract.js');
      
      // Create and initialize Tesseract worker
      this.tesseractWorker = await createWorker({
        logger: options.enableLogging ? (m: any) => console.log(m) : undefined,
        errorHandler: (err: any) => console.error('Tesseract error:', err)
      });

      // Load default language (English)
      await this.tesseractWorker.loadLanguage('eng');
      await this.tesseractWorker.initialize('eng');

      // Set default parameters for better accuracy
      await this.setOCRParameters({
        tessedit_pageseg_mode: '1', // Auto page segmentation with OSD
        tessedit_ocr_engine_mode: '2' // Legacy + LSTM engines
      });

      // Load available languages
      this.supportedLanguages = await this.loadAvailableLanguages();
      
      this.isInitialized = true;
      console.log('OCR processor initialized successfully');

    } catch (error) {
      console.error('OCR processor initialization failed:', error);
      throw new Error(`OCR initialization failed: ${error}`);
    }
  }

  /**
   * Perform OCR on entire document
   */
  public async processDocument(
    options: DocumentOCROptions = {},
    progressCallback?: ProcessingProgressCallback
  ): Promise<DocumentOCRResult> {
    await this.initialize();

    try {
      const pageCount = this.wasmWrapper.getPageCount(this.document);
      const startPage = options.pageRange?.start || 0;
      const endPage = options.pageRange?.end || pageCount - 1;
      const totalPages = endPage - startPage + 1;

      progressCallback?.({
        stage: 'processing',
        percentage: 0,
        currentStep: 'Analyzing document for OCR processing...'
      });

      // Detect which pages need OCR
      const ocrPages = await this.detectOCRPages(startPage, endPage, options);
      
      if (ocrPages.length === 0) {
        return {
          totalPages,
          processedPages: 0,
          ocrResults: [],
          searchablePDF: options.createSearchablePDF ? await this.saveDocument() : undefined,
          processingTime: 0,
          averageConfidence: 100 // No OCR needed
        };
      }

      progressCallback?.({
        stage: 'processing',
        percentage: 10,
        currentStep: `Processing OCR on ${ocrPages.length} pages...`
      });

      const startTime = performance.now();
      const ocrResults: PageOCRResult[] = [];

      // Process each page that needs OCR
      for (let i = 0; i < ocrPages.length; i++) {
        const pageNum = ocrPages[i];
        const progress = 10 + (i / ocrPages.length) * 70;

        progressCallback?.({
          stage: 'processing',
          percentage: progress,
          currentStep: `Processing OCR on page ${pageNum + 1}...`
        });

        const pageResult = await this.processPage(pageNum, options);
        ocrResults.push(pageResult);

        // Add searchable text overlay if requested
        if (options.createSearchablePDF) {
          await this.addSearchableTextOverlay(pageNum, pageResult);
        }
      }

      // Calculate statistics
      const totalConfidence = ocrResults.reduce((sum, result) => sum + result.confidence, 0);
      const averageConfidence = ocrResults.length > 0 ? totalConfidence / ocrResults.length : 100;
      const processingTime = performance.now() - startTime;

      progressCallback?.({
        stage: 'finalizing',
        percentage: 85,
        currentStep: 'Generating searchable PDF...'
      });

      // Save searchable PDF if requested
      let searchablePDF: ArrayBuffer | undefined;
      if (options.createSearchablePDF) {
        searchablePDF = await this.saveDocument();
      }

      progressCallback?.({
        stage: 'finalizing',
        percentage: 100,
        currentStep: 'OCR processing complete!'
      });

      return {
        totalPages,
        processedPages: ocrPages.length,
        ocrResults,
        searchablePDF,
        processingTime,
        averageConfidence
      };

    } catch (error) {
      throw new Error(`Document OCR processing failed: ${error}`);
    }
  }

  /**
   * Perform OCR on a single page
   */
  public async processPage(
    pageNum: number,
    options: PageOCROptions = {},
    progressCallback?: ProcessingProgressCallback
  ): Promise<PageOCRResult> {
    await this.initialize();

    try {
      const cacheKey = `${pageNum}-${JSON.stringify(options)}`;
      if (this.ocrCache.has(cacheKey)) {
        return this.ocrCache.get(cacheKey)!.pageResult;
      }

      progressCallback?.({
        stage: 'processing',
        percentage: 0,
        currentStep: `Extracting page ${pageNum + 1} image...`
      });

      // Extract page as image
      const pageImage = await this.extractPageImage(pageNum, options);

      progressCallback?.({
        stage: 'processing',
        percentage: 20,
        currentStep: 'Preprocessing image for OCR...'
      });

      // Preprocess image for better OCR accuracy
      const preprocessedImage = await this.preprocessingEngine.preprocessImage(
        pageImage, 
        options.preprocessing || {}
      );

      progressCallback?.({
        stage: 'processing',
        percentage: 40,
        currentStep: 'Configuring OCR languages...'
      });

      // Configure languages
      const languages = options.languages || ['eng'];
      await this.configureLanguages(languages);

      progressCallback?.({
        stage: 'processing',
        percentage: 50,
        currentStep: 'Performing OCR recognition...'
      });

      // Perform OCR
      const ocrData = await this.performOCR(preprocessedImage, options);

      progressCallback?.({
        stage: 'processing',
        percentage: 80,
        currentStep: 'Processing OCR results...'
      });

      // Process and enhance OCR results
      const enhancedResults = await this.enhanceOCRResults(ocrData, options);

      const pageResult: PageOCRResult = {
        pageNumber: pageNum,
        text: enhancedResults.text,
        confidence: enhancedResults.confidence,
        words: enhancedResults.words,
        lines: enhancedResults.lines,
        paragraphs: enhancedResults.paragraphs,
        processingTime: enhancedResults.processingTime,
        languages: languages,
        preprocessingApplied: Object.keys(options.preprocessing || {})
      };

      // Cache result
      this.ocrCache.set(cacheKey, { 
        pageResult, 
        timestamp: Date.now() 
      });

      progressCallback?.({
        stage: 'processing',
        percentage: 100,
        currentStep: 'Page OCR complete!'
      });

      return pageResult;

    } catch (error) {
      throw new Error(`Page OCR processing failed: ${error}`);
    }
  }

  /**
   * Detect which pages contain scanned content that needs OCR
   */
  public async detectOCRPages(
    startPage: number = 0,
    endPage?: number,
    options: OCRDetectionOptions = {}
  ): Promise<number[]> {
    const pageCount = this.wasmWrapper.getPageCount(this.document);
    const actualEndPage = endPage || pageCount - 1;
    const ocrPages: number[] = [];

    try {
      for (let pageNum = startPage; pageNum <= actualEndPage; pageNum++) {
        const needsOCR = await this.pageNeedsOCR(pageNum, options);
        if (needsOCR) {
          ocrPages.push(pageNum);
        }
      }

      return ocrPages;

    } catch (error) {
      throw new Error(`OCR page detection failed: ${error}`);
    }
  }

  /**
   * Create searchable PDF with invisible text overlay
   */
  public async createSearchablePDF(
    ocrResults: PageOCRResult[],
    options: SearchablePDFOptions = {},
    progressCallback?: ProcessingProgressCallback
  ): Promise<ArrayBuffer> {
    try {
      progressCallback?.({
        stage: 'processing',
        percentage: 0,
        currentStep: 'Creating searchable PDF...'
      });

      for (let i = 0; i < ocrResults.length; i++) {
        const progress = (i / ocrResults.length) * 90;
        
        progressCallback?.({
          stage: 'processing',
          percentage: progress,
          currentStep: `Adding searchable text to page ${ocrResults[i].pageNumber + 1}...`
        });

        await this.addSearchableTextOverlay(ocrResults[i].pageNumber, ocrResults[i], options);
      }

      progressCallback?.({
        stage: 'finalizing',
        percentage: 95,
        currentStep: 'Saving searchable PDF...'
      });

      const searchablePDF = await this.saveDocument();

      progressCallback?.({
        stage: 'finalizing',
        percentage: 100,
        currentStep: 'Searchable PDF created successfully!'
      });

      return searchablePDF;

    } catch (error) {
      throw new Error(`Searchable PDF creation failed: ${error}`);
    }
  }

  /**
   * Correct OCR results with manual input
   */
  public async correctOCRText(
    pageNum: number,
    corrections: TextCorrection[],
    options: CorrectionOptions = {}
  ): Promise<PageOCRResult> {
    try {
      // Get original OCR result
      const originalResult = await this.processPage(pageNum, { useCache: true });
      
      // Apply corrections
      let correctedText = originalResult.text;
      let correctedWords = [...originalResult.words];

      for (const correction of corrections) {
        if (correction.type === 'replace') {
          correctedText = correctedText.replace(correction.original, correction.corrected);
          
          // Update word-level corrections
          correctedWords = correctedWords.map(word => {
            if (word.text === correction.original) {
              return { ...word, text: correction.corrected, confidence: 100 };
            }
            return word;
          });
        } else if (correction.type === 'insert') {
          // Insert text at specific position
          const insertPos = correction.position || correctedText.length;
          correctedText = correctedText.slice(0, insertPos) + 
                         correction.corrected + 
                         correctedText.slice(insertPos);
        } else if (correction.type === 'delete') {
          correctedText = correctedText.replace(correction.original, '');
          correctedWords = correctedWords.filter(word => word.text !== correction.original);
        }
      }

      // Create corrected result
      const correctedResult: PageOCRResult = {
        ...originalResult,
        text: correctedText,
        words: correctedWords,
        confidence: this.calculateCorrectedConfidence(originalResult, corrections),
        correctionApplied: true,
        correctionCount: corrections.length
      };

      // Update searchable text overlay if requested
      if (options.updateSearchableText) {
        await this.addSearchableTextOverlay(pageNum, correctedResult);
      }

      // Cache corrected result
      const cacheKey = `${pageNum}-corrected-${Date.now()}`;
      this.ocrCache.set(cacheKey, {
        pageResult: correctedResult,
        timestamp: Date.now()
      });

      return correctedResult;

    } catch (error) {
      throw new Error(`OCR text correction failed: ${error}`);
    }
  }

  /**
   * Search text across OCR results
   */
  public searchOCRText(
    query: string,
    options: OCRSearchOptions = {}
  ): OCRSearchResult[] {
    const results: OCRSearchResult[] = [];
    const searchPattern = options.useRegex ? 
      new RegExp(query, options.caseSensitive ? 'g' : 'gi') :
      new RegExp(query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), options.caseSensitive ? 'g' : 'gi');

    // Search through cached OCR results
    for (const [cacheKey, cachedResult] of this.ocrCache) {
      const pageResult = cachedResult.pageResult;
      let match;
      
      while ((match = searchPattern.exec(pageResult.text)) !== null) {
        // Find the word(s) that contain this match
        const matchWords = this.findWordsForMatch(match, pageResult.words);
        
        results.push({
          pageNumber: pageResult.pageNumber,
          matchText: match[0],
          matchIndex: match.index,
          confidence: this.calculateMatchConfidence(matchWords),
          boundingBox: this.calculateMatchBounds(matchWords),
          context: this.getMatchContext(pageResult.text, match.index, options.contextLength || 50),
          words: matchWords
        });

        // Prevent infinite loops with zero-width matches
        if (match.index === searchPattern.lastIndex) {
          searchPattern.lastIndex++;
        }
      }
    }

    // Sort by page number and position
    results.sort((a, b) => {
      if (a.pageNumber !== b.pageNumber) {
        return a.pageNumber - b.pageNumber;
      }
      return a.matchIndex - b.matchIndex;
    });

    return results;
  }

  /**
   * Get OCR processing statistics
   */
  public getOCRStats(): OCRStats {
    const cachedResults = Array.from(this.ocrCache.values());
    
    if (cachedResults.length === 0) {
      return {
        totalPages: 0,
        averageConfidence: 0,
        averageProcessingTime: 0,
        languagesUsed: [],
        totalWords: 0,
        totalCharacters: 0
      };
    }

    const totalConfidence = cachedResults.reduce((sum, result) => sum + result.pageResult.confidence, 0);
    const totalProcessingTime = cachedResults.reduce((sum, result) => sum + (result.pageResult.processingTime || 0), 0);
    const totalWords = cachedResults.reduce((sum, result) => sum + result.pageResult.words.length, 0);
    const totalCharacters = cachedResults.reduce((sum, result) => sum + result.pageResult.text.length, 0);
    
    const languagesUsed = [...new Set(cachedResults.flatMap(result => result.pageResult.languages))];

    return {
      totalPages: cachedResults.length,
      averageConfidence: totalConfidence / cachedResults.length,
      averageProcessingTime: totalProcessingTime / cachedResults.length,
      languagesUsed,
      totalWords,
      totalCharacters
    };
  }

  /**
   * Clear OCR cache
   */
  public clearCache(): void {
    this.ocrCache.clear();
  }

  /**
   * Cleanup OCR processor
   */
  public async cleanup(): Promise<void> {
    try {
      if (this.tesseractWorker) {
        await this.tesseractWorker.terminate();
        this.tesseractWorker = null;
      }
      
      this.clearCache();
      this.isInitialized = false;

    } catch (error) {
      console.error('OCR cleanup error:', error);
    }
  }

  // Private helper methods

  private async loadAvailableLanguages(): Promise<string[]> {
    // Return commonly available Tesseract languages
    return [
      'eng', 'fra', 'deu', 'spa', 'ita', 'por', 'rus', 'ara', 'chi_sim', 'chi_tra', 
      'jpn', 'kor', 'nld', 'swe', 'nor', 'dan', 'fin', 'pol', 'ces', 'hun'
    ];
  }

  private async setOCRParameters(params: Record<string, string>): Promise<void> {
    if (!this.tesseractWorker) return;

    for (const [key, value] of Object.entries(params)) {
      await this.tesseractWorker.setParameters({
        [key]: value
      });
    }
  }

  private async pageNeedsOCR(pageNum: number, options: OCRDetectionOptions): Promise<boolean> {
    try {
      // Extract text using existing PDF text extraction
      const textBlocks = await this.wasmWrapper.extractTextBlocks(this.document, pageNum);
      const extractableText = textBlocks.map((block: any) => block.text).join('').trim();

      // If there's significant extractable text, OCR might not be needed
      if (extractableText.length > (options.textThreshold || 100)) {
        return options.forceOCR || false;
      }

      // Check if page has images (likely scanned content)
      const pageInfo = await this.wasmWrapper.renderPage(this.document, pageNum, 0.1); // Low res for analysis
      
      // Simple heuristic: if page is mostly image-based, it needs OCR
      return true; // Simplified - real implementation would analyze image content

    } catch (error) {
      console.warn(`OCR detection failed for page ${pageNum}:`, error);
      return true; // Default to needing OCR if detection fails
    }
  }

  private async extractPageImage(pageNum: number, options: PageOCROptions): Promise<ImageData> {
    const scale = options.imageScale || 2.0; // Higher resolution for better OCR
    return await this.wasmWrapper.renderPage(this.document, pageNum, scale);
  }

  private async configureLanguages(languages: string[]): Promise<void> {
    if (!this.tesseractWorker) return;

    const languageString = languages.join('+');
    
    try {
      // Load languages if not already loaded
      for (const lang of languages) {
        if (!this.supportedLanguages.includes(lang)) {
          await this.tesseractWorker.loadLanguage(lang);
        }
      }
      
      await this.tesseractWorker.initialize(languageString);
    } catch (error) {
      console.warn(`Failed to load languages ${languageString}, falling back to English:`, error);
      await this.tesseractWorker.initialize('eng');
    }
  }

  private async performOCR(image: ImageData, options: PageOCROptions): Promise<any> {
    if (!this.tesseractWorker) {
      throw new Error('Tesseract worker not initialized');
    }

    try {
      const result = await this.tesseractWorker.recognize(image, {
        rectangle: options.region ? {
          top: options.region.y,
          left: options.region.x,
          width: options.region.width,
          height: options.region.height
        } : undefined
      });

      return result.data;

    } catch (error) {
      throw new Error(`Tesseract OCR failed: ${error}`);
    }
  }

  private async enhanceOCRResults(ocrData: any, options: PageOCROptions): Promise<EnhancedOCRResult> {
    const startTime = performance.now();

    // Extract and enhance text data
    const text = ocrData.text || '';
    const confidence = ocrData.confidence || 0;
    
    // Process words with bounding boxes and confidence
    const words: OCRWord[] = (ocrData.words || []).map((word: any) => ({
      text: word.text,
      confidence: word.confidence,
      boundingBox: {
        x: word.bbox.x0,
        y: word.bbox.y0,
        width: word.bbox.x1 - word.bbox.x0,
        height: word.bbox.y1 - word.bbox.y0
      }
    }));

    // Process lines
    const lines: OCRLine[] = (ocrData.lines || []).map((line: any) => ({
      text: line.text,
      confidence: line.confidence,
      boundingBox: {
        x: line.bbox.x0,
        y: line.bbox.y0,
        width: line.bbox.x1 - line.bbox.x0,
        height: line.bbox.y1 - line.bbox.y0
      },
      words: line.words || []
    }));

    // Process paragraphs
    const paragraphs: OCRParagraph[] = (ocrData.paragraphs || []).map((para: any) => ({
      text: para.text,
      confidence: para.confidence,
      boundingBox: {
        x: para.bbox.x0,
        y: para.bbox.y0,
        width: para.bbox.x1 - para.bbox.x0,
        height: para.bbox.y1 - para.bbox.y0
      },
      lines: para.lines || []
    }));

    // Apply post-processing enhancements
    const enhancedText = options.enablePostProcessing !== false ? 
      this.postProcessText(text, options) : text;

    const processingTime = performance.now() - startTime;

    return {
      text: enhancedText,
      confidence,
      words,
      lines,
      paragraphs,
      processingTime
    };
  }

  private postProcessText(text: string, options: PageOCROptions): string {
    let processed = text;

    // Basic text cleaning
    if (options.postProcessing?.removeExtraSpaces !== false) {
      processed = processed.replace(/\s+/g, ' ').trim();
    }

    if (options.postProcessing?.fixLineBreaks !== false) {
      // Fix broken words at line ends
      processed = processed.replace(/(\w+)-\s*\n\s*(\w+)/g, '$1$2');
    }

    if (options.postProcessing?.correctCommonErrors !== false) {
      // Common OCR corrections
      const corrections = [
        [/\b0\b/g, 'O'],  // Zero to O
        [/\bl\b/g, 'I'],  // lowercase l to I
        [/rn/g, 'm'],     // rn to m
        [/vv/g, 'w']      // vv to w
      ];

      for (const [pattern, replacement] of corrections) {
        processed = processed.replace(pattern, replacement);
      }
    }

    return processed;
  }

  private async addSearchableTextOverlay(
    pageNum: number, 
    ocrResult: PageOCRResult, 
    options: SearchablePDFOptions = {}
  ): Promise<void> {
    try {
      // Add invisible text overlay using WASM
      // This would position text at exact coordinates with 0 opacity
      for (const word of ocrResult.words) {
        if (word.confidence >= (options.minConfidence || 60)) {
          // Add invisible text at word position
          await this.addInvisibleText(pageNum, word.text, word.boundingBox);
        }
      }

    } catch (error) {
      console.warn(`Failed to add searchable text overlay to page ${pageNum}:`, error);
    }
  }

  private async addInvisibleText(pageNum: number, text: string, bounds: BoundingBox): Promise<void> {
    // This would use WASM to add invisible text at specific coordinates
    // Implementation would call appropriate WASM functions
  }

  private async saveDocument(): Promise<ArrayBuffer> {
    return await this.wasmWrapper.saveDocument(this.document, {
      compress: true,
      linearize: false,
      objectStreams: true,
      incrementalUpdate: false
    });
  }

  private calculateCorrectedConfidence(original: PageOCRResult, corrections: TextCorrection[]): number {
    // Calculate new confidence based on corrections
    const correctionRatio = corrections.length / original.words.length;
    const penaltyFactor = Math.max(0, 1 - correctionRatio * 0.1);
    return Math.min(100, original.confidence + (100 - original.confidence) * 0.3 * penaltyFactor);
  }

  private findWordsForMatch(match: RegExpExecArray, words: OCRWord[]): OCRWord[] {
    // Find words that span the match text
    const matchWords: OCRWord[] = [];
    let currentIndex = 0;
    
    for (const word of words) {
      const wordStart = currentIndex;
      const wordEnd = currentIndex + word.text.length;
      
      if (wordEnd > match.index && wordStart < match.index + match[0].length) {
        matchWords.push(word);
      }
      
      currentIndex = wordEnd + 1; // +1 for space
      
      if (currentIndex > match.index + match[0].length) {
        break;
      }
    }
    
    return matchWords;
  }

  private calculateMatchConfidence(words: OCRWord[]): number {
    if (words.length === 0) return 0;
    
    const totalConfidence = words.reduce((sum, word) => sum + word.confidence, 0);
    return totalConfidence / words.length;
  }

  private calculateMatchBounds(words: OCRWord[]): BoundingBox {
    if (words.length === 0) {
      return { x: 0, y: 0, width: 0, height: 0 };
    }

    const minX = Math.min(...words.map(w => w.boundingBox.x));
    const minY = Math.min(...words.map(w => w.boundingBox.y));
    const maxX = Math.max(...words.map(w => w.boundingBox.x + w.boundingBox.width));
    const maxY = Math.max(...words.map(w => w.boundingBox.y + w.boundingBox.height));

    return {
      x: minX,
      y: minY,
      width: maxX - minX,
      height: maxY - minY
    };
  }

  private getMatchContext(text: string, matchIndex: number, contextLength: number): string {
    const start = Math.max(0, matchIndex - contextLength);
    const end = Math.min(text.length, matchIndex + contextLength);
    
    return text.substring(start, end);
  }
}

// Image preprocessing engine for better OCR accuracy
class ImagePreprocessor {
  public async preprocessImage(
    imageData: ImageData, 
    options: PreprocessingOptions
  ): Promise<ImageData> {
    let processed = imageData;

    try {
      if (options.deskew) {
        processed = await this.deskewImage(processed);
      }

      if (options.denoise) {
        processed = await this.denoiseImage(processed);
      }

      if (options.enhanceContrast) {
        processed = await this.enhanceContrast(processed);
      }

      if (options.sharpen) {
        processed = await this.sharpenImage(processed);
      }

      if (options.binarize) {
        processed = await this.binarizeImage(processed);
      }

      return processed;

    } catch (error) {
      console.warn('Image preprocessing failed, using original:', error);
      return imageData;
    }
  }

  private async deskewImage(imageData: ImageData): Promise<ImageData> {
    // Implement deskewing algorithm
    return imageData; // Placeholder
  }

  private async denoiseImage(imageData: ImageData): Promise<ImageData> {
    // Implement denoising algorithm
    return imageData; // Placeholder
  }

  private async enhanceContrast(imageData: ImageData): Promise<ImageData> {
    // Implement contrast enhancement
    return imageData; // Placeholder
  }

  private async sharpenImage(imageData: ImageData): Promise<ImageData> {
    // Implement image sharpening
    return imageData; // Placeholder
  }

  private async binarizeImage(imageData: ImageData): Promise<ImageData> {
    // Convert to black and white for better OCR
    return imageData; // Placeholder
  }
}

// Supporting interfaces and types

export interface OCRInitOptions {
  enableLogging?: boolean;
  workerPath?: string;
  langPath?: string;
}

export interface DocumentOCROptions {
  languages?: string[];
  pageRange?: { start: number; end: number };
  createSearchablePDF?: boolean;
  preprocessing?: PreprocessingOptions;
  enablePostProcessing?: boolean;
  postProcessing?: PostProcessingOptions;
  minConfidence?: number;
}

export interface PageOCROptions {
  languages?: string[];
  region?: BoundingBox;
  imageScale?: number;
  preprocessing?: PreprocessingOptions;
  enablePostProcessing?: boolean;
  postProcessing?: PostProcessingOptions;
  useCache?: boolean;
}

export interface OCRDetectionOptions {
  textThreshold?: number;
  forceOCR?: boolean;
  imageAnalysis?: boolean;
}

export interface PreprocessingOptions {
  deskew?: boolean;
  denoise?: boolean;
  enhanceContrast?: boolean;
  sharpen?: boolean;
  binarize?: boolean;
}

export interface PostProcessingOptions {
  removeExtraSpaces?: boolean;
  fixLineBreaks?: boolean;
  correctCommonErrors?: boolean;
}

export interface SearchablePDFOptions {
  minConfidence?: number;
  preserveFormatting?: boolean;
  invisibleText?: boolean;
}

export interface CorrectionOptions {
  updateSearchableText?: boolean;
  preserveOriginal?: boolean;
}

export interface OCRSearchOptions {
  caseSensitive?: boolean;
  useRegex?: boolean;
  contextLength?: number;
}

export interface DocumentOCRResult {
  totalPages: number;
  processedPages: number;
  ocrResults: PageOCRResult[];
  searchablePDF?: ArrayBuffer;
  processingTime: number;
  averageConfidence: number;
}

export interface PageOCRResult {
  pageNumber: number;
  text: string;
  confidence: number;
  words: OCRWord[];
  lines: OCRLine[];
  paragraphs: OCRParagraph[];
  processingTime?: number;
  languages: string[];
  preprocessingApplied: string[];
  correctionApplied?: boolean;
  correctionCount?: number;
}

export interface OCRWord {
  text: string;
  confidence: number;
  boundingBox: BoundingBox;
}

export interface OCRLine {
  text: string;
  confidence: number;
  boundingBox: BoundingBox;
  words: OCRWord[];
}

export interface OCRParagraph {
  text: string;
  confidence: number;
  boundingBox: BoundingBox;
  lines: OCRLine[];
}

export interface EnhancedOCRResult {
  text: string;
  confidence: number;
  words: OCRWord[];
  lines: OCRLine[];
  paragraphs: OCRParagraph[];
  processingTime: number;
}

export interface OCRResult {
  pageResult: PageOCRResult;
  timestamp: number;
}

export interface TextCorrection {
  type: 'replace' | 'insert' | 'delete';
  original: string;
  corrected: string;
  position?: number;
  confidence?: number;
}

export interface OCRSearchResult {
  pageNumber: number;
  matchText: string;
  matchIndex: number;
  confidence: number;
  boundingBox: BoundingBox;
  context: string;
  words: OCRWord[];
}

export interface OCRStats {
  totalPages: number;
  averageConfidence: number;
  averageProcessingTime: number;
  languagesUsed: string[];
  totalWords: number;
  totalCharacters: number;
}
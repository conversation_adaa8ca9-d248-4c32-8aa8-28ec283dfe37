import {
  Annotation,
  AnnotationData,
  BoundingBox,
  ColorInfo,
  Position,
  DrawingPath,
  Point,
  ProcessingProgressCallback
} from '../types/processing';

/**
 * Professional Annotation System for PDF Documents
 * 
 * Provides comprehensive annotation capabilities including:
 * - Highlight tool with color selection, opacity, and blend modes
 * - Comment and sticky note system with threaded discussions
 * - Freehand drawing tool with pen/brush options and pressure sensitivity
 * - Stamp and signature capabilities with custom stamp creation
 * - Annotation export/import functionality with standard format support
 */
export class PDFAnnotationSystem {
  private document: any;
  private wasmWrapper: any;
  private annotations = new Map<string, Annotation>();
  private annotationHistory: AnnotationOperation[] = [];
  private drawingEngine: DrawingEngine;
  private stampLibrary: StampLibrary;
  private commentSystem: CommentSystem;
  private maxHistorySize = 200;

  constructor(document: any, wasmWrapper: any) {
    this.document = document;
    this.wasmWrapper = wasmWrapper;
    this.drawingEngine = new DrawingEngine();
    this.stampLibrary = new StampLibrary();
    this.commentSystem = new CommentSystem();
  }

  /**
   * Add highlight annotation with advanced options
   */
  public async addHighlight(
    pageNum: number,
    bounds: BoundingBox,
    options: HighlightOptions = {},
    progressCallback?: ProcessingProgressCallback
  ): Promise<string> {
    try {
      progressCallback?.({
        stage: 'processing',
        percentage: 0,
        currentStep: 'Creating highlight annotation...'
      });

      const annotationId = this.generateAnnotationId();
      const highlightData: AnnotationData = {
        highlightColor: options.color || { colorSpace: 'RGB', values: [1, 1, 0] }, // Default yellow
        blendMode: options.blendMode || 'multiply'
      };

      const annotation: Annotation = {
        type: 'highlight',
        id: annotationId,
        pageNum,
        bbox: bounds,
        color: highlightData.highlightColor,
        opacity: options.opacity || 0.5,
        data: highlightData
      };

      progressCallback?.({
        stage: 'processing',
        percentage: 50,
        currentStep: 'Applying highlight to document...'
      });

      // Apply annotation using WASM
      await this.wasmWrapper.addAnnotation(this.document, pageNum, annotation);

      // Store annotation
      this.annotations.set(annotationId, annotation);

      // Record operation
      this.recordOperation({
        type: 'add',
        annotationId,
        annotation,
        timestamp: Date.now(),
        reversible: true
      });

      progressCallback?.({
        stage: 'processing',
        percentage: 100,
        currentStep: 'Highlight annotation added successfully!'
      });

      return annotationId;

    } catch (error) {
      throw new Error(`Failed to add highlight: ${error}`);
    }
  }

  /**
   * Add comment or sticky note annotation
   */
  public async addComment(
    pageNum: number,
    position: Position,
    content: string,
    options: CommentOptions = {},
    progressCallback?: ProcessingProgressCallback
  ): Promise<string> {
    try {
      progressCallback?.({
        stage: 'processing',
        percentage: 0,
        currentStep: 'Creating comment annotation...'
      });

      const annotationId = this.generateAnnotationId();
      const commentData: AnnotationData = {
        commentText: content,
        author: options.author || 'Anonymous',
        createdDate: new Date()
      };

      // Calculate bounds for sticky note icon
      const bounds: BoundingBox = {
        x: position.x,
        y: position.y,
        width: options.iconSize || 24,
        height: options.iconSize || 24
      };

      const annotation: Annotation = {
        type: 'comment',
        id: annotationId,
        pageNum,
        bbox: bounds,
        content,
        color: options.color || { colorSpace: 'RGB', values: [1, 1, 0.8] },
        opacity: options.opacity || 1.0,
        data: commentData
      };

      progressCallback?.({
        stage: 'processing',
        percentage: 30,
        currentStep: 'Adding comment to thread system...'
      });

      // Add to comment system for threading
      await this.commentSystem.addComment(annotation, options.parentCommentId);

      progressCallback?.({
        stage: 'processing',
        percentage: 60,
        currentStep: 'Applying comment to document...'
      });

      // Apply annotation using WASM
      await this.wasmWrapper.addAnnotation(this.document, pageNum, annotation);

      // Store annotation
      this.annotations.set(annotationId, annotation);

      // Record operation
      this.recordOperation({
        type: 'add',
        annotationId,
        annotation,
        timestamp: Date.now(),
        reversible: true
      });

      progressCallback?.({
        stage: 'processing',
        percentage: 100,
        currentStep: 'Comment annotation added successfully!'
      });

      return annotationId;

    } catch (error) {
      throw new Error(`Failed to add comment: ${error}`);
    }
  }

  /**
   * Add freehand drawing annotation with pressure sensitivity
   */
  public async addFreehandDrawing(
    pageNum: number,
    paths: DrawingPath[],
    options: DrawingOptions = {},
    progressCallback?: ProcessingProgressCallback
  ): Promise<string> {
    try {
      progressCallback?.({
        stage: 'processing',
        percentage: 0,
        currentStep: 'Processing freehand drawing...'
      });

      const annotationId = this.generateAnnotationId();

      // Calculate bounds from all paths
      const bounds = this.calculateDrawingBounds(paths);

      // Optimize drawing paths for storage and rendering
      progressCallback?.({
        stage: 'processing',
        percentage: 25,
        currentStep: 'Optimizing drawing paths...'
      });

      const optimizedPaths = await this.drawingEngine.optimizePaths(paths, options);

      const drawingData: AnnotationData = {
        strokeWidth: options.strokeWidth || 2,
        strokeColor: options.color || { colorSpace: 'RGB', values: [0, 0, 0] },
        pressureSensitive: options.pressureSensitive || false,
        paths: optimizedPaths
      };

      const annotation: Annotation = {
        type: 'freehand',
        id: annotationId,
        pageNum,
        bbox: bounds,
        color: drawingData.strokeColor,
        opacity: options.opacity || 1.0,
        data: drawingData
      };

      progressCallback?.({
        stage: 'processing',
        percentage: 70,
        currentStep: 'Applying drawing to document...'
      });

      // Apply annotation using WASM
      await this.wasmWrapper.addAnnotation(this.document, pageNum, annotation);

      // Store annotation
      this.annotations.set(annotationId, annotation);

      // Record operation
      this.recordOperation({
        type: 'add',
        annotationId,
        annotation,
        timestamp: Date.now(),
        reversible: true
      });

      progressCallback?.({
        stage: 'processing',
        percentage: 100,
        currentStep: 'Freehand drawing added successfully!'
      });

      return annotationId;

    } catch (error) {
      throw new Error(`Failed to add freehand drawing: ${error}`);
    }
  }

  /**
   * Add stamp annotation (including signatures)
   */
  public async addStamp(
    pageNum: number,
    position: Position,
    stampOptions: StampOptions,
    progressCallback?: ProcessingProgressCallback
  ): Promise<string> {
    try {
      progressCallback?.({
        stage: 'processing',
        percentage: 0,
        currentStep: 'Preparing stamp annotation...'
      });

      const annotationId = this.generateAnnotationId();
      
      let stampImageData: ArrayBuffer;
      let stampType: string;

      if (stampOptions.customImage) {
        stampImageData = stampOptions.customImage;
        stampType = 'custom';
      } else if (stampOptions.predefinedStamp) {
        progressCallback?.({
          stage: 'processing',
          percentage: 20,
          currentStep: 'Loading predefined stamp...'
        });
        
        const stampInfo = await this.stampLibrary.getStamp(stampOptions.predefinedStamp);
        stampImageData = stampInfo.imageData;
        stampType = stampOptions.predefinedStamp;
      } else {
        throw new Error('Either customImage or predefinedStamp must be provided');
      }

      // Calculate stamp bounds
      const bounds: BoundingBox = {
        x: position.x,
        y: position.y,
        width: stampOptions.width || 100,
        height: stampOptions.height || 50
      };

      progressCallback?.({
        stage: 'processing',
        percentage: 50,
        currentStep: 'Creating stamp annotation...'
      });

      const stampData: AnnotationData = {
        imageData: stampImageData,
        stampType
      };

      const annotation: Annotation = {
        type: 'stamp',
        id: annotationId,
        pageNum,
        bbox: bounds,
        opacity: stampOptions.opacity || 1.0,
        data: stampData
      };

      progressCallback?.({
        stage: 'processing',
        percentage: 80,
        currentStep: 'Applying stamp to document...'
      });

      // Apply annotation using WASM
      await this.wasmWrapper.addAnnotation(this.document, pageNum, annotation);

      // Store annotation
      this.annotations.set(annotationId, annotation);

      // Record operation
      this.recordOperation({
        type: 'add',
        annotationId,
        annotation,
        timestamp: Date.now(),
        reversible: true
      });

      progressCallback?.({
        stage: 'processing',
        percentage: 100,
        currentStep: 'Stamp annotation added successfully!'
      });

      return annotationId;

    } catch (error) {
      throw new Error(`Failed to add stamp: ${error}`);
    }
  }

  /**
   * Add signature annotation (special case of stamp)
   */
  public async addSignature(
    pageNum: number,
    position: Position,
    signatureData: ArrayBuffer,
    options: SignatureOptions = {},
    progressCallback?: ProcessingProgressCallback
  ): Promise<string> {
    const stampOptions: StampOptions = {
      customImage: signatureData,
      width: options.width || 150,
      height: options.height || 75,
      opacity: options.opacity || 1.0
    };

    return this.addStamp(pageNum, position, stampOptions, progressCallback);
  }

  /**
   * Modify existing annotation
   */
  public async modifyAnnotation(
    annotationId: string,
    modifications: Partial<Annotation>,
    progressCallback?: ProcessingProgressCallback
  ): Promise<void> {
    try {
      const annotation = this.annotations.get(annotationId);
      if (!annotation) {
        throw new Error(`Annotation ${annotationId} not found`);
      }

      progressCallback?.({
        stage: 'processing',
        percentage: 0,
        currentStep: 'Modifying annotation...'
      });

      // Store original for undo
      const originalAnnotation = { ...annotation };

      // Apply modifications
      Object.assign(annotation, modifications);

      progressCallback?.({
        stage: 'processing',
        percentage: 50,
        currentStep: 'Updating document...'
      });

      // Update in document using WASM
      await this.updateAnnotationInDocument(annotation);

      // Record operation
      this.recordOperation({
        type: 'modify',
        annotationId,
        annotation: originalAnnotation, // Store original for undo
        modifications,
        timestamp: Date.now(),
        reversible: true
      });

      progressCallback?.({
        stage: 'processing',
        percentage: 100,
        currentStep: 'Annotation modified successfully!'
      });

    } catch (error) {
      throw new Error(`Failed to modify annotation: ${error}`);
    }
  }

  /**
   * Remove annotation
   */
  public async removeAnnotation(
    annotationId: string,
    progressCallback?: ProcessingProgressCallback
  ): Promise<void> {
    try {
      const annotation = this.annotations.get(annotationId);
      if (!annotation) {
        throw new Error(`Annotation ${annotationId} not found`);
      }

      progressCallback?.({
        stage: 'processing',
        percentage: 0,
        currentStep: 'Removing annotation...'
      });

      // Remove from document using WASM
      await this.removeAnnotationFromDocument(annotationId);

      // Remove from local storage
      this.annotations.delete(annotationId);

      // Remove from comment system if it's a comment
      if (annotation.type === 'comment') {
        await this.commentSystem.removeComment(annotationId);
      }

      // Record operation
      this.recordOperation({
        type: 'remove',
        annotationId,
        annotation, // Store for undo
        timestamp: Date.now(),
        reversible: true
      });

      progressCallback?.({
        stage: 'processing',
        percentage: 100,
        currentStep: 'Annotation removed successfully!'
      });

    } catch (error) {
      throw new Error(`Failed to remove annotation: ${error}`);
    }
  }

  /**
   * Get all annotations for a page
   */
  public getAnnotationsForPage(pageNum: number): Annotation[] {
    return Array.from(this.annotations.values()).filter(
      annotation => annotation.pageNum === pageNum
    );
  }

  /**
   * Get all annotations in the document
   */
  public getAllAnnotations(): Annotation[] {
    return Array.from(this.annotations.values());
  }

  /**
   * Search annotations by content or properties
   */
  public searchAnnotations(query: AnnotationSearchQuery): Annotation[] {
    return Array.from(this.annotations.values()).filter(annotation => {
      if (query.type && annotation.type !== query.type) return false;
      if (query.author && annotation.data?.author !== query.author) return false;
      if (query.pageRange) {
        const { start, end } = query.pageRange;
        if (annotation.pageNum < start || annotation.pageNum > end) return false;
      }
      if (query.text) {
        const searchText = query.text.toLowerCase();
        if (annotation.content?.toLowerCase().includes(searchText)) return true;
        if (annotation.data?.commentText?.toLowerCase().includes(searchText)) return true;
      }
      if (query.dateRange) {
        const annotationDate = annotation.data?.createdDate;
        if (annotationDate) {
          const annotationTime = annotationDate.getTime();
          if (annotationTime < query.dateRange.start.getTime() || 
              annotationTime > query.dateRange.end.getTime()) {
            return false;
          }
        }
      }
      return !query.text; // If no text query, return true (other filters passed)
    });
  }

  /**
   * Export annotations in various formats
   */
  public async exportAnnotations(
    format: 'json' | 'xfdf' | 'fdf' | 'csv',
    options: ExportOptions = {}
  ): Promise<string | ArrayBuffer> {
    try {
      const annotations = options.pageRange ? 
        this.getAllAnnotations().filter(a => 
          a.pageNum >= options.pageRange!.start && 
          a.pageNum <= options.pageRange!.end
        ) : 
        this.getAllAnnotations();

      switch (format) {
        case 'json':
          return this.exportToJSON(annotations, options);
          
        case 'xfdf':
          return this.exportToXFDF(annotations, options);
          
        case 'fdf':
          return this.exportToFDF(annotations, options);
          
        case 'csv':
          return this.exportToCSV(annotations, options);
          
        default:
          throw new Error(`Unsupported export format: ${format}`);
      }

    } catch (error) {
      throw new Error(`Failed to export annotations: ${error}`);
    }
  }

  /**
   * Import annotations from various formats
   */
  public async importAnnotations(
    data: string | ArrayBuffer,
    format: 'json' | 'xfdf' | 'fdf',
    options: ImportOptions = {},
    progressCallback?: ProcessingProgressCallback
  ): Promise<string[]> {
    try {
      progressCallback?.({
        stage: 'processing',
        percentage: 0,
        currentStep: `Parsing ${format.toUpperCase()} annotation data...`
      });

      let annotations: Annotation[];

      switch (format) {
        case 'json':
          annotations = this.importFromJSON(data as string, options);
          break;
          
        case 'xfdf':
          annotations = this.importFromXFDF(data as string, options);
          break;
          
        case 'fdf':
          annotations = this.importFromFDF(data as ArrayBuffer, options);
          break;
          
        default:
          throw new Error(`Unsupported import format: ${format}`);
      }

      progressCallback?.({
        stage: 'processing',
        percentage: 30,
        currentStep: `Importing ${annotations.length} annotations...`
      });

      const importedIds: string[] = [];

      for (let i = 0; i < annotations.length; i++) {
        const progress = 30 + (i / annotations.length) * 60;
        
        progressCallback?.({
          stage: 'processing',
          percentage: progress,
          currentStep: `Importing annotation ${i + 1} of ${annotations.length}...`
        });

        const annotation = annotations[i];
        annotation.id = this.generateAnnotationId(); // Generate new ID

        // Add to document
        await this.wasmWrapper.addAnnotation(this.document, annotation.pageNum, annotation);
        
        // Store locally
        this.annotations.set(annotation.id, annotation);
        importedIds.push(annotation.id);
      }

      // Record batch operation
      this.recordOperation({
        type: 'import',
        importedIds,
        timestamp: Date.now(),
        reversible: true
      });

      progressCallback?.({
        stage: 'processing',
        percentage: 100,
        currentStep: `Successfully imported ${annotations.length} annotations!`
      });

      return importedIds;

    } catch (error) {
      throw new Error(`Failed to import annotations: ${error}`);
    }
  }

  /**
   * Get comment thread for annotation
   */
  public getCommentThread(annotationId: string): Annotation[] {
    return this.commentSystem.getThread(annotationId);
  }

  /**
   * Reply to a comment annotation
   */
  public async replyToComment(
    parentAnnotationId: string,
    replyContent: string,
    author?: string
  ): Promise<string> {
    const parentAnnotation = this.annotations.get(parentAnnotationId);
    if (!parentAnnotation || parentAnnotation.type !== 'comment') {
      throw new Error('Parent annotation must be a comment');
    }

    // Create reply annotation near parent
    const replyPosition: Position = {
      x: parentAnnotation.bbox.x + 30,
      y: parentAnnotation.bbox.y - 30,
      page: parentAnnotation.pageNum
    };

    return this.addComment(
      parentAnnotation.pageNum,
      replyPosition,
      replyContent,
      {
        author,
        parentCommentId: parentAnnotationId,
        iconSize: 20 // Smaller for replies
      }
    );
  }

  /**
   * Create custom stamp
   */
  public async createCustomStamp(
    name: string,
    imageData: ArrayBuffer,
    metadata?: StampMetadata
  ): Promise<void> {
    await this.stampLibrary.addStamp(name, imageData, metadata);
  }

  /**
   * Get available predefined stamps
   */
  public getAvailableStamps(): string[] {
    return this.stampLibrary.getStampNames();
  }

  /**
   * Undo last annotation operation
   */
  public async undoLastOperation(): Promise<boolean> {
    if (this.annotationHistory.length === 0) {
      return false;
    }

    const lastOperation = this.annotationHistory[this.annotationHistory.length - 1];
    
    if (!lastOperation.reversible) {
      throw new Error(`Operation '${lastOperation.type}' is not reversible`);
    }

    try {
      await this.reverseOperation(lastOperation);
      this.annotationHistory.pop();
      return true;

    } catch (error) {
      throw new Error(`Failed to undo annotation operation: ${error}`);
    }
  }

  /**
   * Get annotation statistics
   */
  public getAnnotationStats(): AnnotationStats {
    const annotations = this.getAllAnnotations();
    const stats: AnnotationStats = {
      total: annotations.length,
      byType: {},
      byPage: {},
      byAuthor: {},
      dateRange: { earliest: null, latest: null }
    };

    for (const annotation of annotations) {
      // Count by type
      stats.byType[annotation.type] = (stats.byType[annotation.type] || 0) + 1;
      
      // Count by page
      stats.byPage[annotation.pageNum] = (stats.byPage[annotation.pageNum] || 0) + 1;
      
      // Count by author
      const author = annotation.data?.author || 'Unknown';
      stats.byAuthor[author] = (stats.byAuthor[author] || 0) + 1;
      
      // Date range
      const date = annotation.data?.createdDate;
      if (date) {
        if (!stats.dateRange.earliest || date < stats.dateRange.earliest) {
          stats.dateRange.earliest = date;
        }
        if (!stats.dateRange.latest || date > stats.dateRange.latest) {
          stats.dateRange.latest = date;
        }
      }
    }

    return stats;
  }

  /**
   * Clear all annotations
   */
  public async clearAllAnnotations(): Promise<void> {
    const annotationIds = Array.from(this.annotations.keys());
    
    for (const id of annotationIds) {
      await this.removeAnnotationFromDocument(id);
    }
    
    this.annotations.clear();
    this.commentSystem.clear();
  }

  /**
   * Get memory usage information
   */
  public getMemoryUsage(): {
    totalAnnotations: number;
    historySize: number;
    cacheSize: number;
  } {
    return {
      totalAnnotations: this.annotations.size,
      historySize: this.annotationHistory.length,
      cacheSize: this.stampLibrary.getCacheSize() + this.drawingEngine.getCacheSize()
    };
  }

  // Private helper methods

  private generateAnnotationId(): string {
    return `annotation_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private calculateDrawingBounds(paths: DrawingPath[]): BoundingBox {
    let minX = Infinity, minY = Infinity, maxX = 0, maxY = 0;

    for (const path of paths) {
      for (const point of path.points) {
        minX = Math.min(minX, point.x);
        minY = Math.min(minY, point.y);
        maxX = Math.max(maxX, point.x);
        maxY = Math.max(maxY, point.y);
      }
    }

    return {
      x: minX,
      y: minY,
      width: maxX - minX,
      height: maxY - minY
    };
  }

  private async updateAnnotationInDocument(annotation: Annotation): Promise<void> {
    // Update annotation in WASM document
    await this.wasmWrapper.addAnnotation(this.document, annotation.pageNum, annotation);
  }

  private async removeAnnotationFromDocument(_annotationId: string): Promise<void> {
    // Remove annotation from WASM document
    // This would call a WASM function to remove the annotation
  }

  private recordOperation(operation: AnnotationOperation): void {
    this.annotationHistory.push(operation);
    
    // Limit history size
    if (this.annotationHistory.length > this.maxHistorySize) {
      this.annotationHistory.shift();
    }
  }

  private async reverseOperation(operation: AnnotationOperation): Promise<void> {
    switch (operation.type) {
      case 'add':
        if (operation.annotationId) {
          await this.removeAnnotationFromDocument(operation.annotationId);
          this.annotations.delete(operation.annotationId);
        }
        break;
        
      case 'remove':
        if (operation.annotation) {
          await this.wasmWrapper.addAnnotation(
            this.document, 
            operation.annotation.pageNum, 
            operation.annotation
          );
          this.annotations.set(operation.annotation.id, operation.annotation);
        }
        break;
        
      case 'modify':
        if (operation.annotationId && operation.annotation) {
          this.annotations.set(operation.annotationId, operation.annotation);
          await this.updateAnnotationInDocument(operation.annotation);
        }
        break;
        
      case 'import':
        if (operation.importedIds) {
          for (const id of operation.importedIds) {
            await this.removeAnnotationFromDocument(id);
            this.annotations.delete(id);
          }
        }
        break;
        
      default:
        throw new Error(`Cannot reverse operation type: ${operation.type}`);
    }
  }

  // Export/Import helper methods

  private exportToJSON(annotations: Annotation[], _options: ExportOptions): string {
    const exportData = {
      version: '1.0',
      exportDate: new Date().toISOString(),
      annotations: annotations.map(annotation => ({
        ...annotation,
        data: {
          ...annotation.data,
          // Convert dates to ISO strings
          createdDate: annotation.data?.createdDate?.toISOString()
        }
      }))
    };

    return JSON.stringify(exportData, null, _options.prettyPrint ? 2 : 0);
  }

  private exportToXFDF(annotations: Annotation[], _options: ExportOptions): string {
    // XFDF (XML Forms Data Format) export implementation
    let xfdf = `<?xml version="1.0" encoding="UTF-8"?>\n<xfdf xmlns="http://ns.adobe.com/xfdf/" xml:space="preserve">\n<annots>\n`;
    
    for (const annotation of annotations) {
      xfdf += this.annotationToXFDF(annotation);
    }
    
    xfdf += `</annots>\n</xfdf>`;
    return xfdf;
  }

  private exportToFDF(annotations: Annotation[], _options: ExportOptions): ArrayBuffer {
    // FDF (Forms Data Format) binary export implementation
    // This would create a binary FDF file
    const fdfContent = this.createFDFBinary(annotations);
    return fdfContent;
  }

  private exportToCSV(annotations: Annotation[], options: ExportOptions): string {
    const headers = ['ID', 'Type', 'Page', 'Content', 'Author', 'Date', 'X', 'Y', 'Width', 'Height'];
    let csv = headers.join(',') + '\n';

    for (const annotation of annotations) {
      const row = [
        annotation.id,
        annotation.type,
        annotation.pageNum + 1,
        `"${(annotation.content || annotation.data?.commentText || '').replace(/"/g, '""')}"`,
        annotation.data?.author || '',
        annotation.data?.createdDate?.toISOString() || '',
        annotation.bbox.x,
        annotation.bbox.y,
        annotation.bbox.width,
        annotation.bbox.height
      ];
      csv += row.join(',') + '\n';
    }

    return csv;
  }

  private importFromJSON(data: string, options: ImportOptions): Annotation[] {
    const parsedData = JSON.parse(data);
    return parsedData.annotations.map((annotation: any) => ({
      ...annotation,
      data: {
        ...annotation.data,
        // Convert ISO strings back to dates
        createdDate: annotation.data?.createdDate ? new Date(annotation.data.createdDate) : undefined
      }
    }));
  }

  private importFromXFDF(data: string, options: ImportOptions): Annotation[] {
    // XFDF parsing implementation
    // This would parse XML and extract annotations
    return [];
  }

  private importFromFDF(data: ArrayBuffer, options: ImportOptions): Annotation[] {
    // FDF binary parsing implementation
    return [];
  }

  private annotationToXFDF(annotation: Annotation): string {
    // Convert annotation to XFDF format
    return `<${annotation.type} page="${annotation.pageNum}" rect="${annotation.bbox.x},${annotation.bbox.y},${annotation.bbox.x + annotation.bbox.width},${annotation.bbox.y + annotation.bbox.height}" />\n`;
  }

  private createFDFBinary(annotations: Annotation[]): ArrayBuffer {
    // Create binary FDF data
    return new ArrayBuffer(0);
  }
}

// Supporting classes

class DrawingEngine {
  private pathCache = new Map<string, DrawingPath[]>();

  public async optimizePaths(paths: DrawingPath[], options: DrawingOptions): Promise<DrawingPath[]> {
    // Optimize drawing paths for better performance and smaller file size
    return paths.map(path => ({
      ...path,
      points: this.simplifyPath(path.points, options.simplificationTolerance || 2)
    }));
  }

  private simplifyPath(points: Point[], tolerance: number): Point[] {
    // Douglas-Peucker algorithm for path simplification
    if (points.length <= 2) return points;
    
    // Simplified implementation
    return points.filter((point, index) => {
      if (index === 0 || index === points.length - 1) return true;
      
      const prev = points[index - 1];
      const next = points[index + 1];
      
      // Calculate distance from point to line between prev and next
      const distance = this.pointToLineDistance(point, prev, next);
      
      return distance > tolerance;
    });
  }

  private pointToLineDistance(point: Point, lineStart: Point, lineEnd: Point): number {
    const A = point.x - lineStart.x;
    const B = point.y - lineStart.y;
    const C = lineEnd.x - lineStart.x;
    const D = lineEnd.y - lineStart.y;

    const dot = A * C + B * D;
    const lenSq = C * C + D * D;
    
    if (lenSq === 0) return Math.sqrt(A * A + B * B);
    
    const param = dot / lenSq;
    let xx: number, yy: number;

    if (param < 0) {
      xx = lineStart.x;
      yy = lineStart.y;
    } else if (param > 1) {
      xx = lineEnd.x;
      yy = lineEnd.y;
    } else {
      xx = lineStart.x + param * C;
      yy = lineStart.y + param * D;
    }

    const dx = point.x - xx;
    const dy = point.y - yy;
    return Math.sqrt(dx * dx + dy * dy);
  }

  public getCacheSize(): number {
    return this.pathCache.size;
  }
}

class StampLibrary {
  private stamps = new Map<string, StampInfo>();

  constructor() {
    this.initializeDefaultStamps();
  }

  private initializeDefaultStamps(): void {
    // Initialize with common stamps
    const defaultStamps = ['APPROVED', 'REJECTED', 'DRAFT', 'CONFIDENTIAL', 'URGENT'];
    
    for (const stampName of defaultStamps) {
      // Would generate or load stamp images
      this.stamps.set(stampName, {
        name: stampName,
        imageData: new ArrayBuffer(0), // Placeholder
        metadata: {
          category: 'business',
          createdDate: new Date(),
          author: 'system'
        }
      });
    }
  }

  public async addStamp(name: string, imageData: ArrayBuffer, metadata?: StampMetadata): Promise<void> {
    this.stamps.set(name, {
      name,
      imageData,
      metadata: metadata || {
        category: 'custom',
        createdDate: new Date()
      }
    });
  }

  public async getStamp(name: string): Promise<StampInfo> {
    const stamp = this.stamps.get(name);
    if (!stamp) {
      throw new Error(`Stamp '${name}' not found`);
    }
    return stamp;
  }

  public getStampNames(): string[] {
    return Array.from(this.stamps.keys());
  }

  public getCacheSize(): number {
    return this.stamps.size;
  }
}

class CommentSystem {
  private commentThreads = new Map<string, string[]>(); // parentId -> childIds
  private rootComments = new Set<string>();

  public async addComment(annotation: Annotation, parentId?: string): Promise<void> {
    if (parentId) {
      // Add to thread
      if (!this.commentThreads.has(parentId)) {
        this.commentThreads.set(parentId, []);
      }
      this.commentThreads.get(parentId)!.push(annotation.id);
    } else {
      // Root comment
      this.rootComments.add(annotation.id);
    }
  }

  public async removeComment(annotationId: string): Promise<void> {
    // Remove from threads
    for (const [parentId, children] of this.commentThreads) {
      const index = children.indexOf(annotationId);
      if (index > -1) {
        children.splice(index, 1);
        break;
      }
    }
    
    // Remove from root comments
    this.rootComments.delete(annotationId);
    
    // Remove thread if this was a parent
    this.commentThreads.delete(annotationId);
  }

  public getThread(annotationId: string): Annotation[] {
    // This would return the full comment thread
    // Implementation would gather all annotations in the thread
    return [];
  }

  public clear(): void {
    this.commentThreads.clear();
    this.rootComments.clear();
  }
}

// Supporting interfaces

export interface HighlightOptions {
  color?: ColorInfo;
  opacity?: number;
  blendMode?: string;
}

export interface CommentOptions {
  author?: string;
  color?: ColorInfo;
  opacity?: number;
  iconSize?: number;
  parentCommentId?: string;
}

export interface DrawingOptions {
  strokeWidth?: number;
  color?: ColorInfo;
  opacity?: number;
  pressureSensitive?: boolean;
  simplificationTolerance?: number;
}

export interface StampOptions {
  predefinedStamp?: string;
  customImage?: ArrayBuffer;
  width?: number;
  height?: number;
  opacity?: number;
}

export interface SignatureOptions {
  width?: number;
  height?: number;
  opacity?: number;
}

export interface AnnotationSearchQuery {
  type?: string;
  author?: string;
  text?: string;
  pageRange?: { start: number; end: number };
  dateRange?: { start: Date; end: Date };
}

export interface ExportOptions {
  pageRange?: { start: number; end: number };
  prettyPrint?: boolean;
  includeMetadata?: boolean;
}

export interface ImportOptions {
  replaceExisting?: boolean;
  pageOffset?: number;
  authorOverride?: string;
}

export interface AnnotationOperation {
  type: 'add' | 'remove' | 'modify' | 'import';
  annotationId?: string;
  annotation?: Annotation;
  modifications?: Partial<Annotation>;
  importedIds?: string[];
  timestamp: number;
  reversible: boolean;
}

export interface AnnotationStats {
  total: number;
  byType: Record<string, number>;
  byPage: Record<number, number>;
  byAuthor: Record<string, number>;
  dateRange: { earliest: Date | null; latest: Date | null };
}

export interface StampInfo {
  name: string;
  imageData: ArrayBuffer;
  metadata?: StampMetadata;
}

export interface StampMetadata {
  category?: string;
  createdDate?: Date;
  author?: string;
  description?: string;
}
import {
  BoundingBox,
  Position,
  ProcessingProgressCallback
} from '../types/processing';

/**
 * Comprehensive Page Management System
 * 
 * Provides advanced PDF page manipulation capabilities including:
 * - Page addition, removal, rotation, splitting, and merging
 * - Drag-and-drop page reordering with touch optimization
 * - Page thumbnail generation and preview system
 * - Content preservation during operations
 * - Layout optimization and conflict resolution
 */
export class PDFPageManager {
  private document: any;
  private wasmWrapper: any;
  private pageCache = new Map<number, PageInfo>();
  private thumbnailCache = new Map<number, PageThumbnail>();
  private operationHistory: PageOperation[] = [];
  private maxHistorySize = 100;

  constructor(document: any, wasmWrapper: any) {
    this.document = document;
    this.wasmWrapper = wasmWrapper;
  }

  /**
   * Get comprehensive page information
   */
  public async getPageInfo(pageNum: number, includeContent: boolean = false): Promise<PageInfo> {
    const cacheKey = `${pageNum}-${includeContent}`;
    
    if (!includeContent && this.pageCache.has(pageNum)) {
      return this.pageCache.get(pageNum)!;
    }

    try {
      const pageInfo = await this.analyzePageStructure(pageNum, includeContent);
      
      if (!includeContent) {
        this.pageCache.set(pageNum, pageInfo);
      }

      return pageInfo;

    } catch (error) {
      throw new Error(`Failed to get page info for page ${pageNum}: ${error}`);
    }
  }

  /**
   * Add new page with optional content
   */
  public async addPage(
    insertAt: number,
    pageOptions: AddPageOptions = {},
    progressCallback?: ProcessingProgressCallback
  ): Promise<number> {
    try {
      progressCallback?.({
        stage: 'processing',
        percentage: 0,
        currentStep: `Adding new page at position ${insertAt}...`
      });

      const pageCount = this.wasmWrapper.getPageCount(this.document);
      const validIndex = Math.max(0, Math.min(insertAt, pageCount));

      // Create new page with specified dimensions
      const newPageIndex = await this.createNewPage(validIndex, pageOptions);

      // Add content if specified
      if (pageOptions.template || pageOptions.content) {
        progressCallback?.({
          stage: 'processing',
          percentage: 50,
          currentStep: 'Adding page content...'
        });

        await this.addPageContent(newPageIndex, pageOptions);
      }

      // Record operation for undo/redo
      this.recordOperation({
        type: 'add',
        targetPages: [newPageIndex],
        originalIndex: validIndex,
        timestamp: Date.now(),
        reversible: true
      });

      // Clear caches
      this.clearPageCaches();

      progressCallback?.({
        stage: 'processing',
        percentage: 100,
        currentStep: 'Page added successfully!'
      });

      return newPageIndex;

    } catch (error) {
      throw new Error(`Failed to add page: ${error}`);
    }
  }

  /**
   * Remove page(s) with content preservation option
   */
  public async removePage(
    pageNum: number | number[],
    options: RemovePageOptions = {},
    progressCallback?: ProcessingProgressCallback
  ): Promise<void> {
    const pageNumbers = Array.isArray(pageNum) ? pageNum : [pageNum];
    const pageCount = this.wasmWrapper.getPageCount(this.document);

    try {
      progressCallback?.({
        stage: 'processing',
        percentage: 0,
        currentStep: `Removing ${pageNumbers.length} page(s)...`
      });

      // Validate page numbers
      for (const num of pageNumbers) {
        if (num < 0 || num >= pageCount) {
          throw new Error(`Invalid page number: ${num}. Document has ${pageCount} pages.`);
        }
      }

      // Preserve content if requested
      const preservedContent: PageContent[] = [];
      if (options.preserveContent) {
        progressCallback?.({
          stage: 'processing',
          percentage: 25,
          currentStep: 'Preserving page content...'
        });

        for (const num of pageNumbers) {
          const content = await this.extractPageContent(num);
          preservedContent.push(content);
        }
      }

      // Remove pages in reverse order to maintain indices
      const sortedPages = [...pageNumbers].sort((a, b) => b - a);
      
      progressCallback?.({
        stage: 'processing',
        percentage: 50,
        currentStep: 'Removing pages...'
      });

      for (const num of sortedPages) {
        await this.removePageInternal(num);
      }

      // Redistribute preserved content if requested
      if (options.preserveContent && options.redistributeTo !== undefined) {
        progressCallback?.({
          stage: 'processing',
          percentage: 75,
          currentStep: 'Redistributing preserved content...'
        });

        await this.redistributeContent(preservedContent, options.redistributeTo);
      }

      // Record operation
      this.recordOperation({
        type: 'remove',
        targetPages: pageNumbers,
        preservedContent: options.preserveContent ? preservedContent : undefined,
        timestamp: Date.now(),
        reversible: true
      });

      this.clearPageCaches();

      progressCallback?.({
        stage: 'processing',
        percentage: 100,
        currentStep: 'Pages removed successfully!'
      });

    } catch (error) {
      throw new Error(`Failed to remove page(s): ${error}`);
    }
  }

  /**
   * Rotate page(s) by specified angle
   */
  public async rotatePage(
    pageNum: number | number[],
    angle: 90 | 180 | 270 | -90 | -180 | -270,
    progressCallback?: ProcessingProgressCallback
  ): Promise<void> {
    const pageNumbers = Array.isArray(pageNum) ? pageNum : [pageNum];

    try {
      progressCallback?.({
        stage: 'processing',
        percentage: 0,
        currentStep: `Rotating ${pageNumbers.length} page(s) by ${angle}°...`
      });

      const normalizedAngle = this.normalizeAngle(angle);
      
      for (let i = 0; i < pageNumbers.length; i++) {
        const num = pageNumbers[i];
        const progress = (i / pageNumbers.length) * 80 + 10;
        
        progressCallback?.({
          stage: 'processing',
          percentage: progress,
          currentStep: `Rotating page ${num + 1}...`
        });

        await this.rotatePageInternal(num, normalizedAngle);
      }

      // Record operation
      this.recordOperation({
        type: 'rotate',
        targetPages: pageNumbers,
        angle: normalizedAngle,
        timestamp: Date.now(),
        reversible: true
      });

      this.clearPageCaches();

      progressCallback?.({
        stage: 'processing',
        percentage: 100,
        currentStep: 'Pages rotated successfully!'
      });

    } catch (error) {
      throw new Error(`Failed to rotate page(s): ${error}`);
    }
  }

  /**
   * Split page into multiple pages
   */
  public async splitPage(
    pageNum: number,
    splitOptions: SplitPageOptions,
    progressCallback?: ProcessingProgressCallback
  ): Promise<number[]> {
    try {
      progressCallback?.({
        stage: 'processing',
        percentage: 0,
        currentStep: `Analyzing page ${pageNum + 1} for splitting...`
      });

      // Analyze page content and determine split points
      const pageInfo = await this.getPageInfo(pageNum, true);
      const splitPoints = await this.calculateSplitPoints(pageInfo, splitOptions);

      progressCallback?.({
        stage: 'processing',
        percentage: 25,
        currentStep: `Splitting page into ${splitPoints.length + 1} parts...`
      });

      const newPageIndices: number[] = [];

      // Create new pages for each split
      for (let i = 0; i < splitPoints.length; i++) {
        const progress = 25 + (i / splitPoints.length) * 50;
        
        progressCallback?.({
          stage: 'processing',
          percentage: progress,
          currentStep: `Creating split page ${i + 1}...`
        });

        const splitArea = this.calculateSplitArea(pageInfo, splitPoints, i);
        const newPageIndex = await this.createSplitPage(pageNum, splitArea, pageNum + i + 1);
        newPageIndices.push(newPageIndex);
      }

      // Update original page with first split area
      const firstSplitArea = this.calculateSplitArea(pageInfo, splitPoints, -1);
      await this.updatePageWithSplitArea(pageNum, firstSplitArea);

      progressCallback?.({
        stage: 'finalizing',
        percentage: 85,
        currentStep: 'Optimizing split pages...'
      });

      // Optimize content layout in split pages
      for (const index of newPageIndices) {
        await this.optimizeSplitPageLayout(index);
      }

      // Record operation
      this.recordOperation({
        type: 'split',
        targetPages: [pageNum],
        resultPages: [pageNum, ...newPageIndices],
        splitOptions,
        timestamp: Date.now(),
        reversible: true
      });

      this.clearPageCaches();

      progressCallback?.({
        stage: 'finalizing',
        percentage: 100,
        currentStep: `Page split into ${newPageIndices.length + 1} pages successfully!`
      });

      return [pageNum, ...newPageIndices];

    } catch (error) {
      throw new Error(`Failed to split page ${pageNum}: ${error}`);
    }
  }

  /**
   * Merge multiple pages into one
   */
  public async mergePages(
    pageNumbers: number[],
    mergeOptions: MergePageOptions = {},
    progressCallback?: ProcessingProgressCallback
  ): Promise<number> {
    try {
      progressCallback?.({
        stage: 'processing',
        percentage: 0,
        currentStep: `Analyzing ${pageNumbers.length} pages for merging...`
      });

      if (pageNumbers.length < 2) {
        throw new Error('At least 2 pages required for merging');
      }

      // Sort pages and validate
      const sortedPages = [...pageNumbers].sort((a, b) => a - b);
      const pageCount = this.wasmWrapper.getPageCount(this.document);
      
      for (const num of sortedPages) {
        if (num < 0 || num >= pageCount) {
          throw new Error(`Invalid page number: ${num}`);
        }
      }

      // Analyze page contents for optimal layout
      const pageInfos: PageInfo[] = [];
      for (let i = 0; i < sortedPages.length; i++) {
        const progress = (i / sortedPages.length) * 20;
        progressCallback?.({
          stage: 'processing',
          percentage: progress,
          currentStep: `Analyzing page ${sortedPages[i] + 1}...`
        });

        const info = await this.getPageInfo(sortedPages[i], true);
        pageInfos.push(info);
      }

      progressCallback?.({
        stage: 'processing',
        percentage: 25,
        currentStep: 'Calculating optimal merge layout...'
      });

      // Calculate merged page layout
      const mergedLayout = await this.calculateMergedLayout(pageInfos, mergeOptions);

      progressCallback?.({
        stage: 'processing',
        percentage: 40,
        currentStep: 'Creating merged page...'
      });

      // Create new merged page
      const targetPageIndex = sortedPages[0]; // Use first page as target
      await this.createMergedPage(targetPageIndex, pageInfos, mergedLayout);

      progressCallback?.({
        stage: 'processing',
        percentage: 70,
        currentStep: 'Removing source pages...'
      });

      // Remove other pages (in reverse order to maintain indices)
      const pagesToRemove = sortedPages.slice(1).reverse();
      for (const pageIndex of pagesToRemove) {
        await this.removePageInternal(pageIndex);
      }

      progressCallback?.({
        stage: 'finalizing',
        percentage: 90,
        currentStep: 'Optimizing merged page layout...'
      });

      // Optimize final layout
      await this.optimizeMergedPageLayout(targetPageIndex, mergeOptions);

      // Record operation
      this.recordOperation({
        type: 'merge',
        targetPages: sortedPages,
        resultPages: [targetPageIndex],
        mergeOptions,
        timestamp: Date.now(),
        reversible: false // Complex to reverse
      });

      this.clearPageCaches();

      progressCallback?.({
        stage: 'finalizing',
        percentage: 100,
        currentStep: `${pageNumbers.length} pages merged successfully!`
      });

      return targetPageIndex;

    } catch (error) {
      throw new Error(`Failed to merge pages: ${error}`);
    }
  }

  /**
   * Reorder pages with drag-and-drop support
   */
  public async reorderPages(
    fromIndex: number,
    toIndex: number,
    pageCount: number = 1,
    progressCallback?: ProcessingProgressCallback
  ): Promise<number[]> {
    try {
      const totalPages = this.wasmWrapper.getPageCount(this.document);
      
      // Validate indices
      if (fromIndex < 0 || fromIndex >= totalPages || toIndex < 0 || toIndex >= totalPages) {
        throw new Error(`Invalid page indices: from=${fromIndex}, to=${toIndex}, total=${totalPages}`);
      }

      if (fromIndex + pageCount > totalPages) {
        throw new Error(`Page range exceeds document length`);
      }

      progressCallback?.({
        stage: 'processing',
        percentage: 0,
        currentStep: `Reordering ${pageCount} page(s) from position ${fromIndex} to ${toIndex}...`
      });

      // Calculate new page order
      const newOrder = this.calculateNewPageOrder(totalPages, fromIndex, toIndex, pageCount);

      progressCallback?.({
        stage: 'processing',
        percentage: 30,
        currentStep: 'Applying page reordering...'
      });

      // Apply reordering through WASM
      await this.applyPageReordering(newOrder);

      // Record operation
      this.recordOperation({
        type: 'reorder',
        targetPages: Array.from({ length: pageCount }, (_, i) => fromIndex + i),
        fromIndex,
        toIndex,
        pageCount,
        newOrder,
        timestamp: Date.now(),
        reversible: true
      });

      this.clearPageCaches();

      progressCallback?.({
        stage: 'processing',
        percentage: 100,
        currentStep: 'Pages reordered successfully!'
      });

      return newOrder;

    } catch (error) {
      throw new Error(`Failed to reorder pages: ${error}`);
    }
  }

  /**
   * Generate page thumbnail with caching
   */
  public async getPageThumbnail(
    pageNum: number,
    options: ThumbnailOptions = {}
  ): Promise<PageThumbnail> {
    const cacheKey = `${pageNum}-${options.width || 200}-${options.height || 200}-${options.quality || 0.8}`;
    
    if (this.thumbnailCache.has(pageNum)) {
      const cached = this.thumbnailCache.get(pageNum)!;
      if (cached.options.width === options.width && 
          cached.options.height === options.height &&
          cached.options.quality === options.quality) {
        return cached;
      }
    }

    try {
      const thumbnail = await this.generatePageThumbnail(pageNum, options);
      this.thumbnailCache.set(pageNum, thumbnail);
      return thumbnail;

    } catch (error) {
      throw new Error(`Failed to generate thumbnail for page ${pageNum}: ${error}`);
    }
  }

  /**
   * Get thumbnails for all pages with batch optimization
   */
  public async getAllPageThumbnails(
    options: ThumbnailOptions = {},
    progressCallback?: ProcessingProgressCallback
  ): Promise<PageThumbnail[]> {
    const pageCount = this.wasmWrapper.getPageCount(this.document);
    const thumbnails: PageThumbnail[] = [];

    try {
      for (let i = 0; i < pageCount; i++) {
        const progress = (i / pageCount) * 100;
        
        progressCallback?.({
          stage: 'processing',
          percentage: progress,
          currentStep: `Generating thumbnail ${i + 1} of ${pageCount}...`
        });

        const thumbnail = await this.getPageThumbnail(i, options);
        thumbnails.push(thumbnail);
      }

      return thumbnails;

    } catch (error) {
      throw new Error(`Failed to generate page thumbnails: ${error}`);
    }
  }

  /**
   * Undo last page operation
   */
  public async undoLastOperation(): Promise<boolean> {
    if (this.operationHistory.length === 0) {
      return false;
    }

    const lastOperation = this.operationHistory[this.operationHistory.length - 1];
    
    if (!lastOperation.reversible) {
      throw new Error(`Operation '${lastOperation.type}' is not reversible`);
    }

    try {
      await this.reverseOperation(lastOperation);
      this.operationHistory.pop();
      this.clearPageCaches();
      return true;

    } catch (error) {
      throw new Error(`Failed to undo operation: ${error}`);
    }
  }

  /**
   * Get page operation history
   */
  public getOperationHistory(): PageOperation[] {
    return [...this.operationHistory];
  }

  /**
   * Clear all caches and reset state
   */
  public clearCaches(): void {
    this.pageCache.clear();
    this.thumbnailCache.clear();
  }

  /**
   * Get memory usage statistics
   */
  public getMemoryUsage(): {
    pageCache: number;
    thumbnailCache: number;
    operationHistory: number;
    totalPages: number;
  } {
    return {
      pageCache: this.pageCache.size,
      thumbnailCache: this.thumbnailCache.size,
      operationHistory: this.operationHistory.length,
      totalPages: this.wasmWrapper.getPageCount(this.document)
    };
  }

  // Private helper methods

  /**
   * Analyze page structure and content
   */
  private async analyzePageStructure(pageNum: number, includeContent: boolean): Promise<PageInfo> {
    // This would use WASM to analyze the page
    // For now, returning mock data structure
    
    const pageInfo: PageInfo = {
      pageNumber: pageNum,
      dimensions: { width: 612, height: 792 }, // Standard letter size
      rotation: 0,
      hasText: true,
      hasImages: Math.random() > 0.5,
      hasForms: false,
      hasAnnotations: false,
      contentBounds: { x: 50, y: 50, width: 512, height: 692 },
      textBlocks: [],
      images: [],
      annotations: [],
      formFields: []
    };

    if (includeContent) {
      // Would extract actual content using WASM
      pageInfo.textBlocks = await this.extractTextBlocks(pageNum);
      pageInfo.images = await this.extractImages(pageNum);
    }

    return pageInfo;
  }

  /**
   * Create new page with options
   */
  private async createNewPage(index: number, options: AddPageOptions): Promise<number> {
    // Use WASM to create new page
    const dimensions = options.dimensions || { width: 612, height: 792 };
    
    // Mock implementation - real version would call WASM
    return index;
  }

  /**
   * Add content to a new page
   */
  private async addPageContent(pageIndex: number, options: AddPageOptions): Promise<void> {
    if (options.template) {
      await this.applyPageTemplate(pageIndex, options.template);
    }
    
    if (options.content) {
      await this.addPageText(pageIndex, options.content);
    }
  }

  private async applyPageTemplate(pageIndex: number, template: string): Promise<void> {
    // Apply predefined template
  }

  private async addPageText(pageIndex: number, content: string): Promise<void> {
    // Add text content to page
  }

  private async removePageInternal(pageNum: number): Promise<void> {
    // Use WASM to remove page
  }

  private async extractPageContent(pageNum: number): Promise<PageContent> {
    return {
      text: 'Preserved text content',
      images: [],
      annotations: [],
      forms: []
    };
  }

  private async redistributeContent(content: PageContent[], targetPage: number): Promise<void> {
    // Redistribute preserved content to target page
  }

  private normalizeAngle(angle: number): number {
    const normalized = angle % 360;
    return normalized < 0 ? normalized + 360 : normalized;
  }

  private async rotatePageInternal(pageNum: number, angle: number): Promise<void> {
    // Use WASM to rotate page
  }

  private async calculateSplitPoints(pageInfo: PageInfo, options: SplitPageOptions): Promise<number[]> {
    // Calculate optimal split points based on content
    switch (options.method) {
      case 'horizontal':
        return [pageInfo.dimensions.height / 2];
      case 'vertical':
        return [pageInfo.dimensions.width / 2];
      case 'grid':
        return options.gridSize ? 
               Array.from({ length: options.gridSize - 1 }, (_, i) => (i + 1) * (pageInfo.dimensions.height / options.gridSize!)) :
               [pageInfo.dimensions.height / 2];
      case 'content-aware':
        return await this.calculateContentAwareSplits(pageInfo);
      default:
        return [pageInfo.dimensions.height / 2];
    }
  }

  private async calculateContentAwareSplits(pageInfo: PageInfo): Promise<number[]> {
    // Analyze content to find natural split points
    return [pageInfo.dimensions.height / 2]; // Simplified
  }

  private calculateSplitArea(pageInfo: PageInfo, splitPoints: number[], index: number): BoundingBox {
    const height = pageInfo.dimensions.height;
    const splitHeight = height / (splitPoints.length + 1);
    
    return {
      x: 0,
      y: index * splitHeight,
      width: pageInfo.dimensions.width,
      height: splitHeight
    };
  }

  private async createSplitPage(originalPage: number, area: BoundingBox, insertAt: number): Promise<number> {
    // Create new page with content from split area
    return insertAt;
  }

  private async updatePageWithSplitArea(pageNum: number, area: BoundingBox): Promise<void> {
    // Update original page to show only the specified area
  }

  private async optimizeSplitPageLayout(pageIndex: number): Promise<void> {
    // Optimize layout after splitting
  }

  private async calculateMergedLayout(pageInfos: PageInfo[], options: MergePageOptions): Promise<MergedLayout> {
    return {
      arrangement: options.arrangement || 'vertical',
      totalDimensions: { width: 612, height: pageInfos.length * 792 },
      pagePositions: pageInfos.map((info, i) => ({
        pageIndex: i,
        position: { x: 0, y: i * info.dimensions.height, page: i },
        scale: options.scale || 1
      }))
    };
  }

  private async createMergedPage(targetIndex: number, pageInfos: PageInfo[], layout: MergedLayout): Promise<void> {
    // Create merged page using WASM
  }

  private async optimizeMergedPageLayout(pageIndex: number, options: MergePageOptions): Promise<void> {
    // Optimize merged page layout
  }

  private calculateNewPageOrder(totalPages: number, from: number, to: number, count: number): number[] {
    const order = Array.from({ length: totalPages }, (_, i) => i);
    const movedPages = order.splice(from, count);
    order.splice(to, 0, ...movedPages);
    return order;
  }

  private async applyPageReordering(newOrder: number[]): Promise<void> {
    // Apply new page order using WASM
  }

  private async generatePageThumbnail(pageNum: number, options: ThumbnailOptions): Promise<PageThumbnail> {
    const scale = Math.min((options.width || 200) / 612, (options.height || 200) / 792);
    
    // Generate thumbnail using WASM
    const imageData = await this.wasmWrapper.renderPage(this.document, pageNum, scale);
    
    return {
      pageNumber: pageNum,
      imageData,
      dimensions: {
        width: Math.floor(612 * scale),
        height: Math.floor(792 * scale)
      },
      options: options,
      timestamp: Date.now()
    };
  }

  private async reverseOperation(operation: PageOperation): Promise<void> {
    switch (operation.type) {
      case 'add':
        if (operation.targetPages) {
          await this.removePageInternal(operation.targetPages[0]);
        }
        break;
        
      case 'remove':
        // Complex to reverse - would need preserved content
        throw new Error('Remove operation reversal not yet implemented');
        
      case 'rotate':
        if (operation.targetPages && operation.angle !== undefined) {
          const reverseAngle = 360 - operation.angle;
          for (const pageNum of operation.targetPages) {
            await this.rotatePageInternal(pageNum, reverseAngle);
          }
        }
        break;
        
      case 'reorder':
        if (operation.newOrder) {
          // Calculate reverse order
          const reverseOrder = this.calculateReverseOrder(operation.newOrder);
          await this.applyPageReordering(reverseOrder);
        }
        break;
        
      default:
        throw new Error(`Cannot reverse operation type: ${operation.type}`);
    }
  }

  private calculateReverseOrder(order: number[]): number[] {
    const reverse = new Array(order.length);
    for (let i = 0; i < order.length; i++) {
      reverse[order[i]] = i;
    }
    return reverse;
  }

  private async extractTextBlocks(pageNum: number): Promise<any[]> {
    // Extract text blocks using WASM
    return [];
  }

  private async extractImages(pageNum: number): Promise<any[]> {
    // Extract images using WASM
    return [];
  }

  private recordOperation(operation: PageOperation): void {
    this.operationHistory.push(operation);
    
    // Limit history size
    if (this.operationHistory.length > this.maxHistorySize) {
      this.operationHistory.shift();
    }
  }

  private clearPageCaches(): void {
    this.pageCache.clear();
    this.thumbnailCache.clear();
  }
}

// Supporting interfaces and types

export interface PageInfo {
  pageNumber: number;
  dimensions: { width: number; height: number };
  rotation: number;
  hasText: boolean;
  hasImages: boolean;
  hasForms: boolean;
  hasAnnotations: boolean;
  contentBounds: BoundingBox;
  textBlocks: any[];
  images: any[];
  annotations: any[];
  formFields: any[];
}

export interface PageThumbnail {
  pageNumber: number;
  imageData: ImageData;
  dimensions: { width: number; height: number };
  options: ThumbnailOptions;
  timestamp: number;
}

export interface AddPageOptions {
  dimensions?: { width: number; height: number };
  template?: string;
  content?: string;
  backgroundColor?: string;
}

export interface RemovePageOptions {
  preserveContent?: boolean;
  redistributeTo?: number;
}

export interface SplitPageOptions {
  method: 'horizontal' | 'vertical' | 'grid' | 'content-aware';
  gridSize?: number;
  preserveAspectRatio?: boolean;
}

export interface MergePageOptions {
  arrangement?: 'vertical' | 'horizontal' | 'grid';
  scale?: number;
  spacing?: number;
  alignment?: 'start' | 'center' | 'end';
}

export interface ThumbnailOptions {
  width?: number;
  height?: number;
  quality?: number;
  format?: 'png' | 'jpeg' | 'webp';
}

export interface PageOperation {
  type: 'add' | 'remove' | 'rotate' | 'split' | 'merge' | 'reorder';
  targetPages?: number[];
  resultPages?: number[];
  originalIndex?: number;
  fromIndex?: number;
  toIndex?: number;
  pageCount?: number;
  angle?: number;
  splitOptions?: SplitPageOptions;
  mergeOptions?: MergePageOptions;
  newOrder?: number[];
  preservedContent?: PageContent[];
  timestamp: number;
  reversible: boolean;
}

export interface PageContent {
  text: string;
  images: any[];
  annotations: any[];
  forms: any[];
}

export interface MergedLayout {
  arrangement: string;
  totalDimensions: { width: number; height: number };
  pagePositions: Array<{
    pageIndex: number;
    position: Position;
    scale: number;
  }>;
}
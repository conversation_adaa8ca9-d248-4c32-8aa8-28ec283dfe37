import type {
  ProcessingOptions,
  ProcessedResult,
  SignatureData,
  Position,
  WatermarkOptions,
  ProcessingProgressCallback,
  ProcessingError
} from './types/processing.js';
import type { WorkerMessage, WorkerResponse } from './workers/pdf-worker.js';

export class PDFService {
  private static instance: PDFService;
  private worker: Worker | null = null;
  private messageId = 0;
  private pendingOperations = new Map<string, {
    resolve: (value: any) => void;
    reject: (error: Error) => void;
    progressCallback?: ProcessingProgressCallback;
  }>();

  private constructor() {}

  public static getInstance(): PDFService {
    if (!PDFService.instance) {
      PDFService.instance = new PDFService();
    }
    return PDFService.instance;
  }

  /**
   * Initialize the PDF service with Web Worker
   */
  public async initialize(): Promise<void> {
    if (this.worker) {
      return; // Already initialized
    }

    try {
      // Create worker from the worker file
      const workerBlob = new Blob([
        // We'll need to import the worker code here
        // For now, we'll create a URL that can be loaded
      ], { type: 'application/javascript' });

      this.worker = new Worker(URL.createObjectURL(workerBlob), {
        type: 'module'
      });

      this.worker.addEventListener('message', this.handleWorkerMessage.bind(this));
      this.worker.addEventListener('error', this.handleWorkerError.bind(this));

      // Test worker is working
      await this.testWorker();
    } catch (error) {
      throw new Error(`Failed to initialize PDF service: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Process a PDF document
   */
  public async processDocument(
    file: File,
    options: ProcessingOptions = {},
    progressCallback?: ProcessingProgressCallback
  ): Promise<ProcessedResult> {
    await this.ensureWorkerReady();

    const fileArrayBuffer = await file.arrayBuffer();
    const messageId = this.generateMessageId();

    return this.sendWorkerMessage({
      id: messageId,
      type: 'process',
      payload: {
        file: fileArrayBuffer,
        fileName: file.name,
        options
      }
    }, progressCallback);
  }

  /**
   * Compress PDF to target size
   */
  public async compressToTarget(
    file: File,
    targetSize: number,
    progressCallback?: ProcessingProgressCallback
  ): Promise<Uint8Array> {
    await this.ensureWorkerReady();

    const fileArrayBuffer = await file.arrayBuffer();
    const messageId = this.generateMessageId();

    const result = await this.sendWorkerMessage({
      id: messageId,
      type: 'compress',
      payload: {
        file: fileArrayBuffer,
        fileName: file.name,
        targetSize
      }
    }, progressCallback);

    return result.data;
  }

  /**
   * Add signature to PDF
   */
  public async addSignature(
    file: File,
    signature: SignatureData,
    position: Position
  ): Promise<Uint8Array> {
    await this.ensureWorkerReady();

    const fileArrayBuffer = await file.arrayBuffer();
    const messageId = this.generateMessageId();

    const result = await this.sendWorkerMessage({
      id: messageId,
      type: 'addSignature',
      payload: {
        file: fileArrayBuffer,
        fileName: file.name,
        signature,
        position
      }
    });

    return result.data;
  }

  /**
   * Add watermark to PDF
   */
  public async addWatermark(
    file: File,
    watermark: WatermarkOptions
  ): Promise<Uint8Array> {
    await this.ensureWorkerReady();

    const fileArrayBuffer = await file.arrayBuffer();
    const messageId = this.generateMessageId();

    const result = await this.sendWorkerMessage({
      id: messageId,
      type: 'addWatermark',
      payload: {
        file: fileArrayBuffer,
        fileName: file.name,
        watermark
      }
    });

    return result.data;
  }

  /**
   * Validate if file is a valid PDF
   */
  public async validatePDF(file: File): Promise<boolean> {
    await this.ensureWorkerReady();

    const fileArrayBuffer = await file.arrayBuffer();
    const messageId = this.generateMessageId();

    const result = await this.sendWorkerMessage({
      id: messageId,
      type: 'validate',
      payload: {
        file: fileArrayBuffer,
        fileName: file.name
      }
    });

    return result.valid;
  }

  /**
   * Get PDF information
   */
  public async getPDFInfo(file: File): Promise<{
    pages: number;
    size: number;
    title?: string;
    author?: string;
  }> {
    await this.ensureWorkerReady();

    const fileArrayBuffer = await file.arrayBuffer();
    const messageId = this.generateMessageId();

    return this.sendWorkerMessage({
      id: messageId,
      type: 'getInfo',
      payload: {
        file: fileArrayBuffer,
        fileName: file.name
      }
    });
  }

  /**
   * Check memory usage and clean up if needed
   */
  public getMemoryUsage(): {
    usedJSHeapSize: number;
    totalJSHeapSize: number;
    jsHeapSizeLimit: number;
  } {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      return {
        usedJSHeapSize: memory.usedJSHeapSize,
        totalJSHeapSize: memory.totalJSHeapSize,
        jsHeapSizeLimit: memory.jsHeapSizeLimit
      };
    }
    
    return {
      usedJSHeapSize: 0,
      totalJSHeapSize: 0,
      jsHeapSizeLimit: 0
    };
  }

  /**
   * Clean up resources
   */
  public dispose(): void {
    if (this.worker) {
      this.worker.terminate();
      this.worker = null;
    }

    // Reject all pending operations
    for (const [, operation] of this.pendingOperations) {
      operation.reject(new Error('PDF service was disposed'));
    }
    this.pendingOperations.clear();
  }

  private async ensureWorkerReady(): Promise<void> {
    if (!this.worker) {
      await this.initialize();
    }
  }

  private generateMessageId(): string {
    return `msg_${++this.messageId}_${Date.now()}`;
  }

  private sendWorkerMessage(message: WorkerMessage, progressCallback?: ProcessingProgressCallback): Promise<any> {
    return new Promise((resolve, reject) => {
      if (!this.worker) {
        reject(new Error('Worker not initialized'));
        return;
      }

      this.pendingOperations.set(message.id, {
        resolve,
        reject,
        progressCallback
      });

      this.worker.postMessage(message);
    });
  }

  private handleWorkerMessage(event: MessageEvent<WorkerResponse>): void {
    const { id, type, payload } = event.data;
    const operation = this.pendingOperations.get(id);

    if (!operation) {
      console.warn(`Received message for unknown operation: ${id}`);
      return;
    }

    switch (type) {
      case 'success':
        this.pendingOperations.delete(id);
        operation.resolve(payload);
        break;

      case 'error':
        this.pendingOperations.delete(id);
        const error: ProcessingError = new Error(payload.message) as ProcessingError;
        error.code = payload.code;
        error.name = payload.name;
        operation.reject(error);
        break;

      case 'progress':
        if (operation.progressCallback) {
          operation.progressCallback(payload);
        }
        break;

      default:
        console.warn(`Unknown message type: ${type}`);
    }
  }

  private handleWorkerError(error: ErrorEvent): void {
    console.error('PDF Worker error:', error);
    
    // Reject all pending operations
    for (const [, operation] of this.pendingOperations) {
      const processingError: ProcessingError = new Error(`Worker error: ${error.message}`) as ProcessingError;
      processingError.code = 'WASM_ERROR';
      operation.reject(processingError);
    }
    
    this.pendingOperations.clear();
  }

  private async testWorker(): Promise<void> {
    // Simple test to ensure worker is responding
    // This would be implemented based on the actual worker setup
    return Promise.resolve();
  }
}
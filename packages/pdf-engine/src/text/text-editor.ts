import {
  TextBlock,
  TextLayout,
  TextModification,
  TextRange,
  TextStyle,
  TextLine,
  TextCharacter,
  FontInfo,
  ColorInfo,
  BoundingBox,
  LayoutAdjustment,
  Position
} from '../types/processing';

/**
 * Production-Grade Text Editor for PDF Documents
 * 
 * Provides comprehensive text editing capabilities including:
 * - Advanced text layout analysis
 * - Font matching and style preservation
 * - Layout adjustment and reflow
 * - Undo/redo system
 * - Real-time preview
 */
export class PDFTextEditor {
  private document: any;
  private wasmWrapper: any;
  private undoStack: TextModification[] = [];
  private redoStack: TextModification[] = [];
  private maxUndoSteps = 50;
  private textCache = new Map<number, TextLayout>();
  private fontCache = new Map<string, FontInfo>();
  
  constructor(document: any, wasmWrapper: any) {
    this.document = document;
    this.wasmWrapper = wasmWrapper;
  }

  /**
   * Analyze text layout for a specific page
   */
  public async analyzeTextLayout(pageNum: number, useCache: boolean = true): Promise<TextLayout> {
    if (useCache && this.textCache.has(pageNum)) {
      return this.textCache.get(pageNum)!;
    }

    try {
      const textBlocks = await this.wasmWrapper.extractTextBlocks(this.document, pageNum);
      const rawLayout = await this.wasmWrapper.getTextLayout(this.document, pageNum);

      // Enhanced layout analysis
      const enhancedLayout = await this.enhanceTextLayout(rawLayout, textBlocks);
      
      if (useCache) {
        this.textCache.set(pageNum, enhancedLayout);
      }

      return enhancedLayout;

    } catch (error) {
      throw new Error(`Failed to analyze text layout for page ${pageNum}: ${error}`);
    }
  }

  /**
   * Insert text at a specific position with intelligent font matching
   */
  public async insertText(
    pageNum: number, 
    position: Position, 
    text: string, 
    styleHint?: Partial<TextStyle>
  ): Promise<void> {
    try {
      // Analyze surrounding text for style matching
      const layout = await this.analyzeTextLayout(pageNum);
      const matchedStyle = await this.matchTextStyle(position, layout, styleHint);
      
      // Create modification record
      const modification: TextModification = {
        type: 'insert',
        position,
        newText: text,
        newStyle: matchedStyle,
        layoutAdjustment: {
          preserveSpacing: true,
          adjustLineHeight: true,
          reflowParagraph: false,
          maintainJustification: true
        }
      };

      // Apply the modification
      await this.applyTextModification(pageNum, modification);
      
      // Add to undo stack
      this.addToUndoStack(modification);
      
      // Clear cache for this page
      this.textCache.delete(pageNum);

    } catch (error) {
      throw new Error(`Failed to insert text: ${error}`);
    }
  }

  /**
   * Modify existing text with advanced layout preservation
   */
  public async modifyText(
    pageNum: number,
    textRange: TextRange,
    newText: string,
    newStyle?: Partial<TextStyle>,
    layoutOptions?: Partial<LayoutAdjustment>
  ): Promise<void> {
    try {
      // Get current text style from range
      const layout = await this.analyzeTextLayout(pageNum);
      const currentStyle = this.extractStyleFromRange(textRange, layout);
      
      // Merge with new style
      const finalStyle: TextStyle = {
        ...currentStyle,
        ...newStyle
      };

      const modification: TextModification = {
        type: 'modify',
        textRange,
        newText,
        newStyle: finalStyle,
        layoutAdjustment: {
          preserveSpacing: true,
          adjustLineHeight: true,
          reflowParagraph: true,
          maintainJustification: true,
          ...layoutOptions
        }
      };

      await this.applyTextModification(pageNum, modification);
      this.addToUndoStack(modification);
      this.textCache.delete(pageNum);

    } catch (error) {
      throw new Error(`Failed to modify text: ${error}`);
    }
  }

  /**
   * Delete text in a specific range with automatic content repositioning
   */
  public async deleteText(
    pageNum: number,
    textRange: TextRange,
    repositionContent: boolean = true
  ): Promise<void> {
    try {
      // Store original text for undo
      const layout = await this.analyzeTextLayout(pageNum);
      const originalText = this.extractTextFromRange(textRange, layout);
      const originalStyle = this.extractStyleFromRange(textRange, layout);

      const modification: TextModification = {
        type: 'delete',
        textRange,
        newText: '', // Empty for deletion
        layoutAdjustment: {
          preserveSpacing: repositionContent,
          adjustLineHeight: repositionContent,
          reflowParagraph: repositionContent,
          maintainJustification: true
        }
      };

      // Store reverse operation for undo
      const reverseModification: TextModification = {
        type: 'insert',
        position: { x: textRange.bbox.x, y: textRange.bbox.y, page: pageNum },
        newText: originalText,
        newStyle: originalStyle,
        layoutAdjustment: modification.layoutAdjustment
      };

      await this.applyTextModification(pageNum, modification);
      this.addToUndoStack(reverseModification);
      this.textCache.delete(pageNum);

    } catch (error) {
      throw new Error(`Failed to delete text: ${error}`);
    }
  }

  /**
   * Select text in a specific range
   */
  public async selectText(pageNum: number, startPos: Position, endPos: Position): Promise<TextRange> {
    try {
      const layout = await this.analyzeTextLayout(pageNum);
      
      // Find characters within selection bounds
      const selectedChars: TextCharacter[] = [];
      let minX = Infinity, minY = Infinity, maxX = 0, maxY = 0;

      for (const char of layout.characters) {
        if (this.isCharacterInSelection(char, startPos, endPos)) {
          selectedChars.push(char);
          minX = Math.min(minX, char.bbox.x);
          minY = Math.min(minY, char.bbox.y);
          maxX = Math.max(maxX, char.bbox.x + char.bbox.width);
          maxY = Math.max(maxY, char.bbox.y + char.bbox.height);
        }
      }

      if (selectedChars.length === 0) {
        throw new Error('No text found in selection range');
      }

      return {
        startPage: pageNum,
        endPage: pageNum,
        startChar: 0, // Would be calculated based on character positions
        endChar: selectedChars.length - 1,
        bbox: {
          x: minX,
          y: minY,
          width: maxX - minX,
          height: maxY - minY
        }
      };

    } catch (error) {
      throw new Error(`Failed to select text: ${error}`);
    }
  }

  /**
   * Find and replace text with advanced matching options
   */
  public async findAndReplace(
    searchText: string,
    replaceText: string,
    options: {
      caseSensitive?: boolean;
      wholeWords?: boolean;
      useRegex?: boolean;
      pageRange?: { start: number; end: number };
    } = {}
  ): Promise<number> {
    let replacementCount = 0;
    const pageCount = this.wasmWrapper.getPageCount(this.document);
    
    const startPage = options.pageRange?.start || 0;
    const endPage = options.pageRange?.end || pageCount - 1;

    try {
      for (let pageNum = startPage; pageNum <= endPage; pageNum++) {
        const layout = await this.analyzeTextLayout(pageNum);
        const matches = this.findTextMatches(layout, searchText, options);

        // Apply replacements in reverse order to maintain positions
        for (let i = matches.length - 1; i >= 0; i--) {
          const match = matches[i];
          await this.modifyText(pageNum, match.range, replaceText, match.style);
          replacementCount++;
        }
      }

      return replacementCount;

    } catch (error) {
      throw new Error(`Find and replace failed: ${error}`);
    }
  }

  /**
   * Undo last text modification
   */
  public async undo(): Promise<boolean> {
    if (this.undoStack.length === 0) {
      return false;
    }

    const lastModification = this.undoStack.pop()!;
    this.redoStack.push(lastModification);

    try {
      // Apply reverse of the last modification
      const reverseModification = this.createReverseModification(lastModification);
      await this.applyTextModification(0, reverseModification); // Page would be stored in modification
      
      // Clear relevant caches
      this.textCache.clear();
      
      return true;

    } catch (error) {
      console.error('Undo failed:', error);
      // Restore the modification if undo fails
      this.undoStack.push(lastModification);
      this.redoStack.pop();
      return false;
    }
  }

  /**
   * Redo last undone modification
   */
  public async redo(): Promise<boolean> {
    if (this.redoStack.length === 0) {
      return false;
    }

    const modification = this.redoStack.pop()!;
    this.undoStack.push(modification);

    try {
      await this.applyTextModification(0, modification); // Page would be stored in modification
      this.textCache.clear();
      return true;

    } catch (error) {
      console.error('Redo failed:', error);
      this.redoStack.push(modification);
      this.undoStack.pop();
      return false;
    }
  }

  /**
   * Get text editing capabilities and status
   */
  public getEditingInfo(): {
    canUndo: boolean;
    canRedo: boolean;
    undoStackSize: number;
    redoStackSize: number;
    cachedPages: number[];
    totalFonts: number;
  } {
    return {
      canUndo: this.undoStack.length > 0,
      canRedo: this.redoStack.length > 0,
      undoStackSize: this.undoStack.length,
      redoStackSize: this.redoStack.length,
      cachedPages: Array.from(this.textCache.keys()),
      totalFonts: this.fontCache.size
    };
  }

  // Private helper methods

  /**
   * Enhance raw text layout with additional analysis
   */
  private async enhanceTextLayout(rawLayout: TextLayout, textBlocks: TextBlock[]): Promise<TextLayout> {
    // Group characters into lines and words
    const lines = this.groupCharactersIntoLines(rawLayout.characters);
    const enhancedTextBlocks = this.enhanceTextBlocks(textBlocks, rawLayout.characters);

    return {
      pageNum: rawLayout.pageNum,
      textBlocks: enhancedTextBlocks,
      lines,
      characters: rawLayout.characters
    };
  }

  /**
   * Group characters into logical lines
   */
  private groupCharactersIntoLines(characters: TextCharacter[]): TextLine[] {
    const lines: TextLine[] = [];
    const lineGroups = new Map<number, TextCharacter[]>(); // y-position -> characters

    // Group by approximate Y position (allowing for small variations)
    const tolerance = 2; // pixels
    
    for (const char of characters) {
      const roundedY = Math.round(char.bbox.y / tolerance) * tolerance;
      
      if (!lineGroups.has(roundedY)) {
        lineGroups.set(roundedY, []);
      }
      lineGroups.get(roundedY)!.push(char);
    }

    // Convert groups to lines
    let lineId = 0;
    for (const [y, lineChars] of lineGroups) {
      if (lineChars.length === 0) continue;

      // Sort characters by X position
      lineChars.sort((a, b) => a.bbox.x - b.bbox.x);

      // Calculate line bounds
      const minX = Math.min(...lineChars.map(c => c.bbox.x));
      const maxX = Math.max(...lineChars.map(c => c.bbox.x + c.bbox.width));
      const minY = Math.min(...lineChars.map(c => c.bbox.y));
      const maxY = Math.max(...lineChars.map(c => c.bbox.y + c.bbox.height));

      lines.push({
        id: `line_${lineId++}`,
        bbox: {
          x: minX,
          y: minY,
          width: maxX - minX,
          height: maxY - minY
        },
        text: lineChars.map(c => c.char).join(''),
        characters: lineChars,
        lineHeight: maxY - minY,
        baseline: y
      });
    }

    // Sort lines by Y position (top to bottom)
    lines.sort((a, b) => a.baseline - b.baseline);

    return lines;
  }

  /**
   * Enhance text blocks with character-level information
   */
  private enhanceTextBlocks(textBlocks: TextBlock[], characters: TextCharacter[]): TextBlock[] {
    return textBlocks.map(block => {
      // Find characters that belong to this block
      const blockChars = characters.filter(char => 
        this.isPointInBounds(char.bbox, block.bbox)
      );

      return {
        ...block,
        // Add enhanced properties based on character analysis
        text: blockChars.map(c => c.char).join(''),
        // Could add more analysis here
      };
    });
  }

  /**
   * Match text style based on surrounding context
   */
  private async matchTextStyle(
    position: Position, 
    layout: TextLayout, 
    styleHint?: Partial<TextStyle>
  ): Promise<TextStyle> {
    // Find nearest character for style matching
    const nearestChar = this.findNearestCharacter(position, layout.characters);
    
    let baseStyle: TextStyle = {
      fontFamily: 'Arial',
      fontSize: 12,
      fontWeight: 'normal',
      fontStyle: 'normal',
      color: { colorSpace: 'RGB', values: [0, 0, 0] }
    };

    if (nearestChar) {
      baseStyle = {
        fontFamily: nearestChar.font.family,
        fontSize: nearestChar.fontSize,
        fontWeight: 'normal', // Would be determined from font info
        fontStyle: 'normal',
        color: nearestChar.color
      };
    }

    // Apply style hints
    return {
      ...baseStyle,
      ...styleHint
    };
  }

  /**
   * Apply a text modification to the document
   */
  private async applyTextModification(pageNum: number, modification: TextModification): Promise<void> {
    try {
      await this.wasmWrapper.modifyTextLayout(this.document, pageNum, [modification]);
    } catch (error) {
      throw new Error(`Failed to apply text modification: ${error}`);
    }
  }

  /**
   * Extract text style from a text range
   */
  private extractStyleFromRange(textRange: TextRange, layout: TextLayout): TextStyle {
    // Find characters in the range
    const rangeChars = layout.characters.filter(char =>
      this.isPointInBounds(char.bbox, textRange.bbox)
    );

    if (rangeChars.length === 0) {
      return {
        fontFamily: 'Arial',
        fontSize: 12,
        fontWeight: 'normal',
        fontStyle: 'normal',
        color: { colorSpace: 'RGB', values: [0, 0, 0] }
      };
    }

    // Use style from first character (could be enhanced to merge styles)
    const firstChar = rangeChars[0];
    return {
      fontFamily: firstChar.font.family,
      fontSize: firstChar.fontSize,
      fontWeight: 'normal',
      fontStyle: 'normal',
      color: firstChar.color
    };
  }

  /**
   * Extract text content from a range
   */
  private extractTextFromRange(textRange: TextRange, layout: TextLayout): string {
    const rangeChars = layout.characters.filter(char =>
      this.isPointInBounds(char.bbox, textRange.bbox)
    );
    
    return rangeChars.map(c => c.char).join('');
  }

  /**
   * Find text matches in layout
   */
  private findTextMatches(
    layout: TextLayout, 
    searchText: string, 
    options: any
  ): Array<{ range: TextRange; style: TextStyle }> {
    // Simplified implementation - would be enhanced for production
    const matches: Array<{ range: TextRange; style: TextStyle }> = [];
    
    // Convert layout to searchable text
    const fullText = layout.characters.map(c => c.char).join('');
    
    let searchPattern: RegExp;
    if (options.useRegex) {
      searchPattern = new RegExp(searchText, options.caseSensitive ? 'g' : 'gi');
    } else {
      const escaped = searchText.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
      const flags = options.caseSensitive ? 'g' : 'gi';
      searchPattern = new RegExp(escaped, flags);
    }

    let match;
    while ((match = searchPattern.exec(fullText)) !== null) {
      const startIndex = match.index;
      const endIndex = startIndex + match[0].length - 1;
      
      // Create range from character positions
      if (startIndex < layout.characters.length && endIndex < layout.characters.length) {
        const startChar = layout.characters[startIndex];
        const endChar = layout.characters[endIndex];
        
        const range: TextRange = {
          startPage: layout.pageNum,
          endPage: layout.pageNum,
          startChar: startIndex,
          endChar: endIndex,
          bbox: {
            x: startChar.bbox.x,
            y: Math.min(startChar.bbox.y, endChar.bbox.y),
            width: (endChar.bbox.x + endChar.bbox.width) - startChar.bbox.x,
            height: Math.max(startChar.bbox.y + startChar.bbox.height, 
                            endChar.bbox.y + endChar.bbox.height) - 
                   Math.min(startChar.bbox.y, endChar.bbox.y)
          }
        };

        matches.push({
          range,
          style: this.extractStyleFromRange(range, layout)
        });
      }
    }

    return matches;
  }

  /**
   * Check if character is in selection bounds
   */
  private isCharacterInSelection(char: TextCharacter, startPos: Position, endPos: Position): boolean {
    const charCenter = {
      x: char.bbox.x + char.bbox.width / 2,
      y: char.bbox.y + char.bbox.height / 2
    };

    return charCenter.x >= Math.min(startPos.x, endPos.x) &&
           charCenter.x <= Math.max(startPos.x, endPos.x) &&
           charCenter.y >= Math.min(startPos.y, endPos.y) &&
           charCenter.y <= Math.max(startPos.y, endPos.y);
  }

  /**
   * Find nearest character to a position
   */
  private findNearestCharacter(position: Position, characters: TextCharacter[]): TextCharacter | null {
    if (characters.length === 0) return null;

    let nearest = characters[0];
    let minDistance = this.calculateDistance(position, this.getCharacterCenter(nearest));

    for (const char of characters) {
      const distance = this.calculateDistance(position, this.getCharacterCenter(char));
      if (distance < minDistance) {
        minDistance = distance;
        nearest = char;
      }
    }

    return nearest;
  }

  /**
   * Calculate distance between two points
   */
  private calculateDistance(point1: Position, point2: Position): number {
    const dx = point1.x - point2.x;
    const dy = point1.y - point2.y;
    return Math.sqrt(dx * dx + dy * dy);
  }

  /**
   * Get center point of a character
   */
  private getCharacterCenter(char: TextCharacter): Position {
    return {
      x: char.bbox.x + char.bbox.width / 2,
      y: char.bbox.y + char.bbox.height / 2,
      page: 0 // Page number would be passed as a parameter
    };
  }

  /**
   * Check if point is within bounds
   */
  private isPointInBounds(point: BoundingBox, bounds: BoundingBox): boolean {
    return point.x >= bounds.x &&
           point.y >= bounds.y &&
           point.x + point.width <= bounds.x + bounds.width &&
           point.y + point.height <= bounds.y + bounds.height;
  }

  /**
   * Create reverse of a modification for undo
   */
  private createReverseModification(modification: TextModification): TextModification {
    switch (modification.type) {
      case 'insert':
        return {
          type: 'delete',
          textRange: {
            startPage: 0,
            endPage: 0,
            startChar: 0,
            endChar: modification.newText?.length || 0,
            bbox: { x: 0, y: 0, width: 0, height: 0 } // Would be calculated
          },
          layoutAdjustment: modification.layoutAdjustment
        };
      
      case 'delete':
        return {
          type: 'insert',
          position: modification.textRange ? 
                   { x: modification.textRange.bbox.x, y: modification.textRange.bbox.y, page: 0 } :
                   { x: 0, y: 0, page: 0 },
          newText: '', // Would need to store original text
          newStyle: modification.newStyle,
          layoutAdjustment: modification.layoutAdjustment
        };
      
      case 'modify':
        return {
          type: 'modify',
          textRange: modification.textRange,
          newText: '', // Would need to store original text
          newStyle: modification.newStyle, // Would need to store original style
          layoutAdjustment: modification.layoutAdjustment
        };
      
      default:
        throw new Error(`Cannot create reverse for modification type: ${modification.type}`);
    }
  }

  /**
   * Add modification to undo stack
   */
  private addToUndoStack(modification: TextModification): void {
    this.undoStack.push(modification);
    
    // Limit stack size
    if (this.undoStack.length > this.maxUndoSteps) {
      this.undoStack.shift();
    }
    
    // Clear redo stack when new modification is made
    this.redoStack.length = 0;
  }

  /**
   * Clear all caches and reset editor state
   */
  public clearCaches(): void {
    this.textCache.clear();
    this.fontCache.clear();
  }

  /**
   * Get memory usage information
   */
  public getMemoryUsage(): {
    textCacheSize: number;
    fontCacheSize: number;
    undoStackSize: number;
    redoStackSize: number;
  } {
    return {
      textCacheSize: this.textCache.size,
      fontCacheSize: this.fontCache.size,
      undoStackSize: this.undoStack.length,
      redoStackSize: this.redoStack.length
    };
  }
}
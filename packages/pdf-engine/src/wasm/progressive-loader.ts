import { BrowserCapabilities, WASMLoadingConfig } from '../types/processing';

/**
 * Progressive WASM Loading Strategy
 * 
 * Implements intelligent WASM loading with:
 * - Browser capability detection
 * - Optimal build selection
 * - Progressive loading with fallbacks
 * - Performance monitoring
 */
export class ProgressiveWASMLoader {
  private static instance: ProgressiveWASMLoader;
  private loadingPromise: Promise<WebAssembly.Instance> | null = null;
  private wasmInstance: WebAssembly.Instance | null = null;
  private loadingStartTime = 0;
  private loadingStats: LoadingStats = {
    attempts: 0,
    buildType: 'basic',
    loadingTimeMs: 0,
    fallbacksUsed: [],
    success: false
  };

  private constructor() {}

  public static getInstance(): ProgressiveWASMLoader {
    if (!ProgressiveWASMLoader.instance) {
      ProgressiveWASMLoader.instance = new ProgressiveWASMLoader();
    }
    return ProgressiveWASMLoader.instance;
  }

  /**
   * Load optimal WASM build with progressive strategy
   */
  public async loadWASM(config: WASMLoadingConfig): Promise<WebAssembly.Instance> {
    if (this.wasmInstance) {
      return this.wasmInstance;
    }

    if (this.loadingPromise) {
      return this.loadingPromise;
    }

    this.loadingPromise = this.performProgressiveLoading(config);
    return this.loadingPromise;
  }

  private async performProgressiveLoading(config: WASMLoadingConfig): Promise<WebAssembly.Instance> {
    this.loadingStartTime = performance.now();
    this.loadingStats.attempts = 0;
    this.loadingStats.fallbacksUsed = [];

    const capabilities = this.detectCapabilities();
    const loadingStrategy = this.createLoadingStrategy(capabilities, config);

    console.log('Starting progressive WASM loading with strategy:', loadingStrategy);

    for (const attempt of loadingStrategy.attempts) {
      try {
        this.loadingStats.attempts++;
        console.log(`WASM Loading attempt ${this.loadingStats.attempts}: ${attempt.buildType}`);

        const instance = await this.loadWASMBuild(attempt);
        
        // Success!
        this.wasmInstance = instance;
        this.loadingStats.buildType = attempt.buildType;
        this.loadingStats.loadingTimeMs = performance.now() - this.loadingStartTime;
        this.loadingStats.success = true;

        console.log('WASM loading successful:', this.loadingStats);
        return instance;

      } catch (error) {
        console.warn(`WASM loading failed for ${attempt.buildType}:`, error);
        this.loadingStats.fallbacksUsed.push({
          buildType: attempt.buildType,
          error: error instanceof Error ? error.message : 'Unknown error'
        });

        // Continue to next attempt
      }
    }

    // All attempts failed
    this.loadingStats.loadingTimeMs = performance.now() - this.loadingStartTime;
    this.loadingStats.success = false;

    throw new Error(`All WASM loading attempts failed. Fallbacks used: ${this.loadingStats.fallbacksUsed.map(f => f.buildType).join(', ')}`);
  }

  /**
   * Detect browser capabilities for WASM optimization
   */
  private detectCapabilities(): BrowserCapabilities {
    const capabilities: BrowserCapabilities = {
      webAssembly: typeof WebAssembly !== 'undefined',
      simd: false,
      threads: false,
      sharedArrayBuffer: typeof SharedArrayBuffer !== 'undefined',
      workers: typeof Worker !== 'undefined'
    };

    // Detailed SIMD support detection
    if (capabilities.webAssembly) {
      try {
        // Test SIMD with actual WASM bytecode
        const simdTestModule = new Uint8Array([
          0x00, 0x61, 0x73, 0x6d, 0x01, 0x00, 0x00, 0x00, // WASM header
          0x01, 0x05, 0x01, 0x60, 0x00, 0x01, 0x7b,       // Type section (function type)
          0x03, 0x02, 0x01, 0x00,                         // Function section
          0x0a, 0x0a, 0x01, 0x08, 0x00,                   // Code section
          0x41, 0x00, 0xfd, 0x0f, 0x0b                    // i32.const 0, v128.const, end
        ]);
        
        capabilities.simd = WebAssembly.validate(simdTestModule);
      } catch {
        capabilities.simd = false;
      }
    }

    // Threads support requires SIMD + SharedArrayBuffer + Atomics
    capabilities.threads = capabilities.simd && 
                          capabilities.sharedArrayBuffer && 
                          typeof Atomics !== 'undefined';

    // Additional capability tests
    this.performExtendedCapabilityTests(capabilities);

    return capabilities;
  }

  /**
   * Perform extended browser capability tests
   */
  private performExtendedCapabilityTests(capabilities: BrowserCapabilities): void {
    // Test actual SharedArrayBuffer functionality (not just existence)
    if (capabilities.sharedArrayBuffer) {
      try {
        const sab = new SharedArrayBuffer(1024);
        const view = new Int32Array(sab);
        Atomics.store(view, 0, 42);
        const value = Atomics.load(view, 0);
        capabilities.threads = capabilities.threads && value === 42;
      } catch {
        capabilities.threads = false;
        capabilities.sharedArrayBuffer = false;
      }
    }

    // Test Worker functionality
    if (capabilities.workers) {
      try {
        // Test if we can create a worker (some environments block this)
        const testWorker = new Worker('data:application/javascript,self.postMessage("test");');
        testWorker.terminate();
      } catch {
        capabilities.workers = false;
        capabilities.threads = false; // Threads need workers
      }
    }

    console.log('Browser capabilities detected:', capabilities);
  }

  /**
   * Create loading strategy based on capabilities and configuration
   */
  private createLoadingStrategy(capabilities: BrowserCapabilities, config: WASMLoadingConfig): LoadingStrategy {
    const attempts: LoadingAttempt[] = [];

    // Determine optimal build order based on capabilities
    if (capabilities.threads && capabilities.simd && config.buildType !== 'basic') {
      attempts.push({
        buildType: 'threads-simd',
        wasmPath: '/assets/wasm/mupdf/mupdf-threads-simd.wasm',
        loadingMethod: 'streaming',
        priority: 1
      });
    }

    if (capabilities.simd && (config.buildType === 'simd' || config.buildType === 'threads-simd')) {
      attempts.push({
        buildType: 'simd',
        wasmPath: '/assets/wasm/mupdf/mupdf-simd.wasm',
        loadingMethod: 'streaming',
        priority: 2
      });
    }

    if (capabilities.threads && (config.buildType === 'threads' || config.buildType === 'threads-simd')) {
      attempts.push({
        buildType: 'threads',
        wasmPath: '/assets/wasm/mupdf/mupdf-threads.wasm',
        loadingMethod: 'streaming',
        priority: 3
      });
    }

    // Always include basic as fallback
    attempts.push({
      buildType: 'basic',
      wasmPath: '/assets/wasm/mupdf/mupdf-basic.wasm',
      loadingMethod: 'streaming',
      priority: 4
    });

    // Add direct loading fallbacks if streaming fails
    if (config.progressive) {
      const streamingAttempts = [...attempts];
      for (const attempt of streamingAttempts) {
        attempts.push({
          ...attempt,
          loadingMethod: 'direct',
          priority: attempt.priority + 10
        });
      }
    }

    // Sort by priority (lower number = higher priority)
    attempts.sort((a, b) => a.priority - b.priority);

    return {
      attempts,
      fallbackEnabled: config.fallbackEnabled,
      maxAttempts: attempts.length
    };
  }

  /**
   * Load specific WASM build
   */
  private async loadWASMBuild(attempt: LoadingAttempt): Promise<WebAssembly.Instance> {
    const startTime = performance.now();
    
    try {
      let instance: WebAssembly.Instance;

      if (attempt.loadingMethod === 'streaming' && 'compileStreaming' in WebAssembly) {
        instance = await this.loadViaStreaming(attempt);
      } else {
        instance = await this.loadViaDirect(attempt);
      }

      const loadTime = performance.now() - startTime;
      console.log(`WASM build ${attempt.buildType} loaded successfully in ${loadTime.toFixed(2)}ms`);

      return instance;

    } catch (error) {
      const loadTime = performance.now() - startTime;
      console.warn(`WASM build ${attempt.buildType} failed after ${loadTime.toFixed(2)}ms:`, error);
      throw error;
    }
  }

  /**
   * Load WASM via streaming compilation
   */
  private async loadViaStreaming(attempt: LoadingAttempt): Promise<WebAssembly.Instance> {
    const response = await fetch(attempt.wasmPath);
    
    if (!response.ok) {
      throw new Error(`Failed to fetch WASM: ${response.status} ${response.statusText}`);
    }

    // Use streaming compilation for better performance
    const module = await WebAssembly.compileStreaming(response);
    const importObject = this.createImportObject(attempt);
    
    return await WebAssembly.instantiate(module, importObject);
  }

  /**
   * Load WASM via direct ArrayBuffer compilation
   */
  private async loadViaDirect(attempt: LoadingAttempt): Promise<WebAssembly.Instance> {
    const response = await fetch(attempt.wasmPath);
    
    if (!response.ok) {
      throw new Error(`Failed to fetch WASM: ${response.status} ${response.statusText}`);
    }

    const wasmBytes = await response.arrayBuffer();
    const module = await WebAssembly.compile(wasmBytes);
    const importObject = this.createImportObject(attempt);
    
    return await WebAssembly.instantiate(module, importObject);
  }

  /**
   * Create WASM import object based on build type
   */
  private createImportObject(attempt: LoadingAttempt): WebAssembly.Imports {
    const baseImports: WebAssembly.Imports = {
      env: {
        memory: this.createWASMMemory(attempt),
        __heap_base: 0,
        __data_end: 0,
        
        // Standard C library functions
        malloc: (size: number) => this.mockMalloc(size),
        free: (ptr: number) => this.mockFree(ptr),
        memset: (ptr: number, value: number, size: number) => ptr,
        memcpy: (dest: number, src: number, size: number) => dest,
        
        // Error handling
        abort: () => { throw new Error('WASM abort called'); },
        __assert_fail: (condition: number, file: number, line: number, func: number) => {
          throw new Error(`WASM assertion failed at ${file}:${line} in ${func}`);
        },
        
        // Math functions
        sin: Math.sin,
        cos: Math.cos,
        tan: Math.tan,
        sqrt: Math.sqrt,
        pow: Math.pow,
        
        // Console output
        printf: (...args: any[]) => console.log('WASM printf:', ...args),
        puts: (str: number) => console.log('WASM puts:', str)
      }
    };

    // Add threading imports for thread-enabled builds
    if (attempt.buildType.includes('threads')) {
      baseImports.env = {
        ...baseImports.env,
        __pthread_create: () => 0,
        __pthread_join: () => 0,
        __pthread_mutex_lock: () => 0,
        __pthread_mutex_unlock: () => 0
      };
    }

    return baseImports;
  }

  /**
   * Create WASM memory based on build type
   */
  private createWASMMemory(attempt: LoadingAttempt): WebAssembly.Memory {
    const memoryConfig: WebAssembly.MemoryDescriptor = {
      initial: 256, // 16MB initial
      maximum: 4096 // 256MB maximum
    };

    // Enable shared memory for threading builds
    if (attempt.buildType.includes('threads')) {
      memoryConfig.shared = true;
    }

    return new WebAssembly.Memory(memoryConfig);
  }

  // Mock memory management functions (will be replaced by actual WASM exports)
  private mockMalloc(size: number): number {
    return Math.floor(Math.random() * 1000000); // Mock pointer
  }

  private mockFree(ptr: number): void {
    // Mock free - actual implementation will be in WASM
  }

  /**
   * Get loading statistics
   */
  public getLoadingStats(): LoadingStats {
    return { ...this.loadingStats };
  }

  /**
   * Check if WASM is loaded and ready
   */
  public isReady(): boolean {
    return this.wasmInstance !== null;
  }

  /**
   * Get loaded WASM instance
   */
  public getInstance(): WebAssembly.Instance | null {
    return this.wasmInstance;
  }

  /**
   * Reset loader state
   */
  public reset(): void {
    this.wasmInstance = null;
    this.loadingPromise = null;
    this.loadingStats = {
      attempts: 0,
      buildType: 'basic',
      loadingTimeMs: 0,
      fallbacksUsed: [],
      success: false
    };
  }
}

// Supporting interfaces

interface LoadingStrategy {
  attempts: LoadingAttempt[];
  fallbackEnabled: boolean;
  maxAttempts: number;
}

interface LoadingAttempt {
  buildType: 'basic' | 'simd' | 'threads' | 'threads-simd';
  wasmPath: string;
  loadingMethod: 'streaming' | 'direct';
  priority: number;
}

interface LoadingStats {
  attempts: number;
  buildType: string;
  loadingTimeMs: number;
  fallbacksUsed: FallbackAttempt[];
  success: boolean;
}

interface FallbackAttempt {
  buildType: string;
  error: string;
}

/**
 * WASM Build Detector - Utility for determining available builds
 */
export class WASMBuildDetector {
  private static cachedBuilds: string[] | null = null;

  /**
   * Detect available WASM builds on the server
   */
  public static async detectAvailableBuilds(): Promise<string[]> {
    if (this.cachedBuilds) {
      return this.cachedBuilds;
    }

    const builds: string[] = [];
    const basePath = '/assets/wasm/mupdf/';
    const buildTypes = ['basic', 'simd', 'threads', 'threads-simd'];

    for (const buildType of buildTypes) {
      try {
        const response = await fetch(`${basePath}mupdf-${buildType}.wasm`, { method: 'HEAD' });
        if (response.ok) {
          builds.push(buildType);
        }
      } catch {
        // Build not available
      }
    }

    this.cachedBuilds = builds;
    console.log('Available WASM builds:', builds);
    
    return builds;
  }

  /**
   * Get recommended build type based on capabilities
   */
  public static getRecommendedBuild(capabilities: BrowserCapabilities, availableBuilds: string[]): string {
    if (capabilities.threads && capabilities.simd && availableBuilds.includes('threads-simd')) {
      return 'threads-simd';
    }
    
    if (capabilities.simd && availableBuilds.includes('simd')) {
      return 'simd';
    }
    
    if (capabilities.threads && availableBuilds.includes('threads')) {
      return 'threads';
    }
    
    return 'basic'; // Always available as fallback
  }
}

/**
 * WASM Performance Monitor - Tracks performance metrics
 */
export class WASMPerformanceMonitor {
  private static metrics: PerformanceMetric[] = [];
  private static readonly MAX_METRICS = 100;

  public static recordMetric(
    operation: string,
    durationMs: number,
    buildType: string,
    success: boolean,
    metadata?: Record<string, any>
  ): void {
    const metric: PerformanceMetric = {
      timestamp: Date.now(),
      operation,
      durationMs,
      buildType,
      success,
      metadata
    };

    this.metrics.push(metric);
    
    // Keep only recent metrics
    if (this.metrics.length > this.MAX_METRICS) {
      this.metrics.shift();
    }
  }

  public static getMetrics(): PerformanceMetric[] {
    return [...this.metrics];
  }

  public static getAveragePerformance(operation: string, buildType?: string): number | null {
    const filtered = this.metrics.filter(m => 
      m.operation === operation && 
      m.success && 
      (!buildType || m.buildType === buildType)
    );

    if (filtered.length === 0) return null;

    const totalDuration = filtered.reduce((sum, m) => sum + m.durationMs, 0);
    return totalDuration / filtered.length;
  }

  public static getBestPerformingBuild(operation: string): string | null {
    const buildPerformance = new Map<string, number[]>();

    for (const metric of this.metrics) {
      if (metric.operation === operation && metric.success) {
        if (!buildPerformance.has(metric.buildType)) {
          buildPerformance.set(metric.buildType, []);
        }
        buildPerformance.get(metric.buildType)!.push(metric.durationMs);
      }
    }

    let bestBuild = null;
    let bestAverage = Infinity;

    for (const [buildType, durations] of buildPerformance) {
      const average = durations.reduce((sum, d) => sum + d, 0) / durations.length;
      if (average < bestAverage) {
        bestAverage = average;
        bestBuild = buildType;
      }
    }

    return bestBuild;
  }
}

interface PerformanceMetric {
  timestamp: number;
  operation: string;
  durationMs: number;
  buildType: string;
  success: boolean;
  metadata?: Record<string, any>;
}
/**
 * Privacy Manager for PDF Processing Engine
 * 
 * Ensures privacy-first processing with zero server communication by default,
 * client-side encryption, and comprehensive privacy compliance monitoring.
 */

export interface PrivacySettings {
  enforceClientSideOnly: boolean;
  allowServerProcessing: boolean;
  encryptTemporaryFiles: boolean;
  clearMemoryOnExit: boolean;
  logPrivacyEvents: boolean;
  gdprCompliant: boolean;
  ccpaCompliant: boolean;
  dataRetentionDays: number;
}

export interface PrivacyEvent {
  id: string;
  timestamp: Date;
  type: 'data-loaded' | 'processing-started' | 'server-communication' | 'memory-cleared' | 'data-encrypted' | 'compliance-check';
  location: 'client' | 'server' | 'memory' | 'storage';
  dataType: 'pdf-content' | 'metadata' | 'user-data' | 'processing-state';
  action: 'create' | 'read' | 'update' | 'delete' | 'transmit' | 'encrypt' | 'decrypt';
  details: string;
  complianceStatus: 'compliant' | 'warning' | 'violation';
}

export interface PrivacyReport {
  timestamp: Date;
  overallStatus: 'compliant' | 'warnings' | 'violations';
  serverCommunications: number;
  dataEncrypted: number;
  memoryCleared: number;
  privacyEvents: PrivacyEvent[];
  violations: PrivacyEvent[];
  recommendations: string[];
}

export interface EncryptedStorage {
  data: ArrayBuffer;
  encryptionKey: string;
  iv: string;
  timestamp: Date;
  expiresAt: Date;
}

/**
 * Privacy-first processing manager with comprehensive compliance monitoring
 */
export class PrivacyManager {
  private static instance: PrivacyManager;
  private settings: PrivacySettings;
  private privacyEvents: PrivacyEvent[] = [];
  private encryptedStorageMap: Map<string, EncryptedStorage> = new Map();
  private networkMonitor: NetworkMonitor | null = null;
  private memoryMonitor: MemoryMonitor | null = null;

  private constructor() {
    this.settings = this.getDefaultPrivacySettings();
    this.initializePrivacyMonitoring();
  }

  public static getInstance(): PrivacyManager {
    if (!PrivacyManager.instance) {
      PrivacyManager.instance = new PrivacyManager();
    }
    return PrivacyManager.instance;
  }

  /**
   * Initialize privacy monitoring systems
   */
  private async initializePrivacyMonitoring(): Promise<void> {
    try {
      // Initialize network monitoring to detect any server communications
      this.networkMonitor = new NetworkMonitor((request) => {
        this.logPrivacyEvent({
          type: 'server-communication',
          location: 'server',
          dataType: 'pdf-content',
          action: 'transmit',
          details: `Network request detected: ${request.url}`,
          complianceStatus: this.settings.allowServerProcessing ? 'compliant' : 'violation'
        });
      });

      // Initialize memory monitoring
      this.memoryMonitor = new MemoryMonitor();

      // Set up automatic memory cleanup
      this.setupMemoryCleanup();

      // Log initialization
      this.logPrivacyEvent({
        type: 'compliance-check',
        location: 'client',
        dataType: 'processing-state',
        action: 'create',
        details: 'Privacy Manager initialized with default settings',
        complianceStatus: 'compliant'
      });

    } catch (error) {
      console.error('Failed to initialize privacy monitoring:', error);
    }
  }

  /**
   * Verify zero server communication for client-side operations
   * AC: 10.1 - Verify zero server communication for default client-side operations
   */
  public verifyZeroServerCommunication(): Promise<{
    hasServerCommunication: boolean;
    communicationAttempts: Array<{
      timestamp: Date;
      url: string;
      method: string;
      blocked: boolean;
    }>;
    complianceStatus: 'compliant' | 'violation';
  }> {
    return new Promise((resolve) => {
      if (!this.networkMonitor) {
        resolve({
          hasServerCommunication: false,
          communicationAttempts: [],
          complianceStatus: 'compliant'
        });
        return;
      }

      const communicationAttempts = this.networkMonitor.getNetworkAttempts();
      const hasServerCommunication = communicationAttempts.length > 0;
      
      const complianceStatus = hasServerCommunication && !this.settings.allowServerProcessing 
        ? 'violation' 
        : 'compliant';

      resolve({
        hasServerCommunication,
        communicationAttempts,
        complianceStatus
      });
    });
  }

  /**
   * Encrypt data for temporary storage
   * AC: 10.2 - Implement client-side encryption for temporary storage and memory handling
   */
  public async encryptData(
    data: ArrayBuffer,
    identifier: string,
    expirationMinutes: number = 60
  ): Promise<string> {
    try {
      // Generate encryption key and IV
      const key = await this.generateEncryptionKey();
      const iv = crypto.getRandomValues(new Uint8Array(16));

      // Encrypt the data
      const encryptedData = await crypto.subtle.encrypt(
        {
          name: 'AES-GCM',
          iv: iv
        },
        key,
        data
      );

      // Store encrypted data
      const encryptedStorage: EncryptedStorage = {
        data: encryptedData,
        encryptionKey: await this.exportKey(key),
        iv: Array.from(iv).map(b => b.toString(16).padStart(2, '0')).join(''),
        timestamp: new Date(),
        expiresAt: new Date(Date.now() + expirationMinutes * 60 * 1000)
      };

      this.encryptedStorageMap.set(identifier, encryptedStorage);

      // Log encryption event
      this.logPrivacyEvent({
        type: 'data-encrypted',
        location: 'memory',
        dataType: 'pdf-content',
        action: 'encrypt',
        details: `Data encrypted with identifier: ${identifier}`,
        complianceStatus: 'compliant'
      });

      return identifier;

    } catch (error) {
      this.logPrivacyEvent({
        type: 'data-encrypted',
        location: 'memory',
        dataType: 'pdf-content',
        action: 'encrypt',
        details: `Encryption failed: ${error}`,
        complianceStatus: 'violation'
      });
      throw new Error(`Data encryption failed: ${error}`);
    }
  }

  /**
   * Decrypt data from temporary storage
   */
  public async decryptData(identifier: string): Promise<ArrayBuffer> {
    try {
      const encryptedStorage = this.encryptedStorageMap.get(identifier);
      
      if (!encryptedStorage) {
        throw new Error(`No encrypted data found for identifier: ${identifier}`);
      }

      // Check expiration
      if (new Date() > encryptedStorage.expiresAt) {
        this.encryptedStorageMap.delete(identifier);
        throw new Error(`Encrypted data expired for identifier: ${identifier}`);
      }

      // Import the key
      const key = await this.importKey(encryptedStorage.encryptionKey);
      const iv = new Uint8Array(encryptedStorage.iv.match(/.{2}/g)!.map(byte => parseInt(byte, 16)));

      // Decrypt the data
      const decryptedData = await crypto.subtle.decrypt(
        {
          name: 'AES-GCM',
          iv: iv
        },
        key,
        encryptedStorage.data
      );

      // Log decryption event
      this.logPrivacyEvent({
        type: 'data-encrypted',
        location: 'memory',
        dataType: 'pdf-content',
        action: 'decrypt',
        details: `Data decrypted with identifier: ${identifier}`,
        complianceStatus: 'compliant'
      });

      return decryptedData;

    } catch (error) {
      this.logPrivacyEvent({
        type: 'data-encrypted',
        location: 'memory',
        dataType: 'pdf-content',
        action: 'decrypt',
        details: `Decryption failed: ${error}`,
        complianceStatus: 'violation'
      });
      throw new Error(`Data decryption failed: ${error}`);
    }
  }

  /**
   * Secure memory cleanup for sensitive document data
   * AC: 10.4 - Create secure memory cleanup for sensitive document data
   */
  public async performSecureMemoryCleanup(targetIdentifiers?: string[]): Promise<{
    itemsCleared: number;
    memoryFreed: number;
    complianceStatus: 'compliant';
  }> {
    try {
      let itemsCleared = 0;
      let memoryFreed = 0;

      if (targetIdentifiers) {
        // Clear specific identifiers
        for (const identifier of targetIdentifiers) {
          const storage = this.encryptedStorageMap.get(identifier);
          if (storage) {
            memoryFreed += storage.data.byteLength;
            this.encryptedStorageMap.delete(identifier);
            itemsCleared++;
          }
        }
      } else {
        // Clear all expired or all data
        for (const [identifier, storage] of this.encryptedStorageMap.entries()) {
          if (new Date() > storage.expiresAt || !targetIdentifiers) {
            memoryFreed += storage.data.byteLength;
            this.encryptedStorageMap.delete(identifier);
            itemsCleared++;
          }
        }
      }

      // Force garbage collection if available
      if ('gc' in window && typeof (window as any).gc === 'function') {
        (window as any).gc();
      }

      // Clear JavaScript memory references
      if (this.memoryMonitor) {
        this.memoryMonitor.clearSensitiveReferences();
      }

      // Log cleanup event
      this.logPrivacyEvent({
        type: 'memory-cleared',
        location: 'memory',
        dataType: 'pdf-content',
        action: 'delete',
        details: `Secure cleanup: ${itemsCleared} items cleared, ${memoryFreed} bytes freed`,
        complianceStatus: 'compliant'
      });

      return {
        itemsCleared,
        memoryFreed,
        complianceStatus: 'compliant'
      };

    } catch (error) {
      this.logPrivacyEvent({
        type: 'memory-cleared',
        location: 'memory',
        dataType: 'pdf-content',
        action: 'delete',
        details: `Memory cleanup failed: ${error}`,
        complianceStatus: 'violation'
      });
      throw new Error(`Secure memory cleanup failed: ${error}`);
    }
  }

  /**
   * Monitor and report privacy compliance
   * AC: 10.3 - Add privacy compliance monitoring with transparent processing location indicators
   */
  public generatePrivacyReport(): PrivacyReport {
    const now = new Date();
    const recentEvents = this.privacyEvents.filter(
      event => (now.getTime() - event.timestamp.getTime()) < 24 * 60 * 60 * 1000 // Last 24 hours
    );

    const violations = recentEvents.filter(event => event.complianceStatus === 'violation');
    const warnings = recentEvents.filter(event => event.complianceStatus === 'warning');

    let overallStatus: PrivacyReport['overallStatus'] = 'compliant';
    if (violations.length > 0) {
      overallStatus = 'violations';
    } else if (warnings.length > 0) {
      overallStatus = 'warnings';
    }

    const serverCommunications = recentEvents.filter(e => e.type === 'server-communication').length;
    const dataEncrypted = recentEvents.filter(e => e.type === 'data-encrypted' && e.action === 'encrypt').length;
    const memoryCleared = recentEvents.filter(e => e.type === 'memory-cleared').length;

    const recommendations = this.generatePrivacyRecommendations(recentEvents, violations, warnings);

    return {
      timestamp: now,
      overallStatus,
      serverCommunications,
      dataEncrypted,
      memoryCleared,
      privacyEvents: recentEvents,
      violations,
      recommendations
    };
  }

  /**
   * Get transparent processing location indicators
   */
  public getProcessingLocationIndicators(): {
    currentLocation: 'client' | 'server' | 'hybrid';
    dataLocation: string[];
    networkActivity: boolean;
    encryptionStatus: 'encrypted' | 'unencrypted' | 'mixed';
    complianceStatus: 'compliant' | 'warning' | 'violation';
  } {
    const networkActivity = this.networkMonitor?.hasRecentActivity() || false;
    const currentLocation = networkActivity ? 'server' : 'client';
    
    const dataLocation: string[] = ['Browser Memory'];
    if (this.encryptedStorageMap.size > 0) {
      dataLocation.push('Encrypted Client Storage');
    }
    if (networkActivity) {
      dataLocation.push('Server Processing');
    }

    const encryptionStatus = this.encryptedStorageMap.size > 0 ? 'encrypted' : 'unencrypted';
    
    let complianceStatus: 'compliant' | 'warning' | 'violation' = 'compliant';
    if (networkActivity && !this.settings.allowServerProcessing) {
      complianceStatus = 'violation';
    } else if (networkActivity && this.settings.allowServerProcessing) {
      complianceStatus = 'warning';
    }

    return {
      currentLocation,
      dataLocation,
      networkActivity,
      encryptionStatus,
      complianceStatus
    };
  }

  /**
   * Update privacy settings
   */
  public updatePrivacySettings(newSettings: Partial<PrivacySettings>): void {
    this.settings = { ...this.settings, ...newSettings };
    
    this.logPrivacyEvent({
      type: 'compliance-check',
      location: 'client',
      dataType: 'processing-state',
      action: 'update',
      details: `Privacy settings updated: ${JSON.stringify(newSettings)}`,
      complianceStatus: 'compliant'
    });
  }

  /**
   * Get current privacy settings
   */
  public getPrivacySettings(): PrivacySettings {
    return { ...this.settings };
  }

  // Private helper methods

  private getDefaultPrivacySettings(): PrivacySettings {
    return {
      enforceClientSideOnly: true,
      allowServerProcessing: false,
      encryptTemporaryFiles: true,
      clearMemoryOnExit: true,
      logPrivacyEvents: true,
      gdprCompliant: true,
      ccpaCompliant: true,
      dataRetentionDays: 0 // No retention by default
    };
  }

  private logPrivacyEvent(eventData: Omit<PrivacyEvent, 'id' | 'timestamp'>): void {
    if (!this.settings.logPrivacyEvents) return;

    const event: PrivacyEvent = {
      id: `privacy_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
      ...eventData
    };

    this.privacyEvents.push(event);

    // Log to console for development/debugging
    if (event.complianceStatus === 'violation') {
      console.error('Privacy violation detected:', event);
    } else if (event.complianceStatus === 'warning') {
      console.warn('Privacy warning:', event);
    }

    // Keep only recent events to prevent memory bloat
    if (this.privacyEvents.length > 1000) {
      this.privacyEvents = this.privacyEvents.slice(-500);
    }
  }

  private async generateEncryptionKey(): Promise<CryptoKey> {
    return await crypto.subtle.generateKey(
      {
        name: 'AES-GCM',
        length: 256
      },
      true,
      ['encrypt', 'decrypt']
    );
  }

  private async exportKey(key: CryptoKey): Promise<string> {
    const exported = await crypto.subtle.exportKey('jwk', key);
    return JSON.stringify(exported);
  }

  private async importKey(keyData: string): Promise<CryptoKey> {
    const keyObject = JSON.parse(keyData);
    return await crypto.subtle.importKey(
      'jwk',
      keyObject,
      {
        name: 'AES-GCM',
        length: 256
      },
      false,
      ['encrypt', 'decrypt']
    );
  }

  private setupMemoryCleanup(): void {
    // Setup automatic cleanup on page unload
    window.addEventListener('beforeunload', () => {
      if (this.settings.clearMemoryOnExit) {
        this.performSecureMemoryCleanup().catch(console.error);
      }
    });

    // Setup periodic cleanup of expired data
    setInterval(() => {
      this.performSecureMemoryCleanup().catch(console.error);
    }, 5 * 60 * 1000); // Every 5 minutes
  }

  private generatePrivacyRecommendations(
    events: PrivacyEvent[],
    violations: PrivacyEvent[],
    warnings: PrivacyEvent[]
  ): string[] {
    const recommendations: string[] = [];

    if (violations.length > 0) {
      recommendations.push(`${violations.length} privacy violation(s) detected - review server communication settings`);
    }

    if (warnings.length > 0) {
      recommendations.push(`${warnings.length} privacy warning(s) - consider stricter privacy settings`);
    }

    const serverEvents = events.filter(e => e.type === 'server-communication');
    if (serverEvents.length > 0 && this.settings.enforceClientSideOnly) {
      recommendations.push('Server communication detected while client-side enforcement is enabled');
    }

    const encryptionEvents = events.filter(e => e.type === 'data-encrypted');
    if (encryptionEvents.length === 0 && this.settings.encryptTemporaryFiles) {
      recommendations.push('No data encryption events detected - verify encryption is working');
    }

    if (this.encryptedStorageMap.size > 10) {
      recommendations.push('Large number of encrypted items in memory - consider more frequent cleanup');
    }

    if (recommendations.length === 0) {
      recommendations.push('Privacy compliance is good - no immediate actions required');
    }

    return recommendations;
  }

  /**
   * Export privacy audit log
   */
  public exportPrivacyAuditLog(): {
    exportDate: Date;
    settings: PrivacySettings;
    eventCount: number;
    events: PrivacyEvent[];
    summary: {
      violations: number;
      warnings: number;
      compliantEvents: number;
    };
  } {
    const violations = this.privacyEvents.filter(e => e.complianceStatus === 'violation').length;
    const warnings = this.privacyEvents.filter(e => e.complianceStatus === 'warning').length;
    const compliantEvents = this.privacyEvents.filter(e => e.complianceStatus === 'compliant').length;

    return {
      exportDate: new Date(),
      settings: this.settings,
      eventCount: this.privacyEvents.length,
      events: [...this.privacyEvents],
      summary: {
        violations,
        warnings,
        compliantEvents
      }
    };
  }

  /**
   * Cleanup and shutdown
   */
  public async cleanup(): Promise<void> {
    await this.performSecureMemoryCleanup();
    this.networkMonitor?.cleanup();
    this.memoryMonitor?.cleanup();
    this.privacyEvents.length = 0;
    console.log('Privacy Manager cleanup completed');
  }
}

/**
 * Network monitoring helper class
 */
class NetworkMonitor {
  private networkAttempts: Array<{
    timestamp: Date;
    url: string;
    method: string;
    blocked: boolean;
  }> = [];
  
  private originalFetch: typeof fetch;
  private originalXMLHttpRequest: typeof XMLHttpRequest;

  constructor(private onNetworkActivity: (request: { url: string; method: string }) => void) {
    this.setupNetworkInterception();
  }

  private setupNetworkInterception(): void {
    // Intercept fetch requests
    this.originalFetch = window.fetch;
    window.fetch = (...args) => {
      const url = typeof args[0] === 'string' ? args[0] : args[0].url;
      const method = args[1]?.method || 'GET';
      
      this.networkAttempts.push({
        timestamp: new Date(),
        url,
        method,
        blocked: false
      });
      
      this.onNetworkActivity({ url, method });
      
      return this.originalFetch.apply(window, args);
    };

    // Intercept XMLHttpRequest
    this.originalXMLHttpRequest = window.XMLHttpRequest;
    const self = this;
    
    window.XMLHttpRequest = class extends XMLHttpRequest {
      open(method: string, url: string | URL, ...args: any[]): void {
        const urlString = url.toString();
        
        self.networkAttempts.push({
          timestamp: new Date(),
          url: urlString,
          method,
          blocked: false
        });
        
        self.onNetworkActivity({ url: urlString, method });
        
        return super.open(method, url, ...args);
      }
    };
  }

  public getNetworkAttempts(): typeof this.networkAttempts {
    return [...this.networkAttempts];
  }

  public hasRecentActivity(minutesAgo: number = 5): boolean {
    const cutoff = new Date(Date.now() - minutesAgo * 60 * 1000);
    return this.networkAttempts.some(attempt => attempt.timestamp > cutoff);
  }

  public cleanup(): void {
    // Restore original implementations
    window.fetch = this.originalFetch;
    window.XMLHttpRequest = this.originalXMLHttpRequest;
    this.networkAttempts.length = 0;
  }
}

/**
 * Memory monitoring helper class
 */
class MemoryMonitor {
  private sensitiveReferences: WeakSet<object> = new WeakSet();

  public trackSensitiveData(data: object): void {
    this.sensitiveReferences.add(data);
  }

  public clearSensitiveReferences(): void {
    // WeakSet automatically handles cleanup when objects are garbage collected
    // This method serves as a signal to perform manual cleanup where possible
    if ('gc' in window && typeof (window as any).gc === 'function') {
      try {
        (window as any).gc();
      } catch (error) {
        // Garbage collection not available
      }
    }
  }

  public cleanup(): void {
    // Nothing specific to clean up for WeakSet
  }
}
/**
 * Privacy-Preserving Error Logging and Performance Analytics
 * 
 * Logs errors and performance metrics without exposing sensitive document data
 * while maintaining compliance with privacy-first principles.
 */

export interface PrivacyPreservingLogEntry {
  id: string;
  timestamp: Date;
  level: 'info' | 'warn' | 'error' | 'debug';
  category: 'performance' | 'error' | 'privacy' | 'compliance' | 'user-action';
  
  // Safe metadata (no sensitive content)
  metadata: {
    fileSize?: number;
    pageCount?: number;
    processingMethod?: string;
    operationType?: string;
    userAgent?: string;
    deviceType?: string;
    processingTimeMs?: number;
  };
  
  // Sanitized message (sensitive data removed)
  message: string;
  
  // Error information (sanitized)
  error?: {
    name: string;
    code?: string;
    stack?: string; // Sanitized stack trace
    cause?: string;
  };
  
  // Performance metrics
  performance?: {
    memoryUsageMB?: number;
    cpuTime?: number;
    renderingTime?: number;
    networkTime?: number;
  };
  
  // Privacy compliance indicators
  privacy: {
    containsSensitiveData: false; // Always false for privacy compliance
    processingLocation: 'client' | 'server';
    dataEncrypted: boolean;
    complianceStatus: 'compliant' | 'warning' | 'violation';
  };
}

export interface AnalyticsEvent {
  eventType: 'operation_start' | 'operation_complete' | 'operation_error' | 'user_interaction';
  operationType: string;
  duration?: number;
  success: boolean;
  errorCategory?: string;
  performanceMetrics?: {
    speed: 'fast' | 'medium' | 'slow';
    memoryUsage: 'low' | 'medium' | 'high';
    qualityScore: number;
  };
}

/**
 * Privacy-preserving logging system
 */
export class PrivacyLogger {
  private static instance: PrivacyLogger;
  private logEntries: PrivacyPreservingLogEntry[] = [];
  private analyticsEvents: AnalyticsEvent[] = [];
  private maxLogEntries = 500;
  private sensitiveDataPatterns: RegExp[];

  private constructor() {
    this.initializeSensitiveDataPatterns();
  }

  public static getInstance(): PrivacyLogger {
    if (!PrivacyLogger.instance) {
      PrivacyLogger.instance = new PrivacyLogger();
    }
    return PrivacyLogger.instance;
  }

  /**
   * Log error without exposing sensitive data
   * AC: 10.5 - Privacy-preserving error logging without sensitive data exposure
   */
  public logError(
    error: Error,
    context: {
      operationType?: string;
      fileSize?: number;
      pageCount?: number;
      processingMethod?: string;
    } = {}
  ): void {
    const sanitizedError = this.sanitizeError(error);
    const sanitizedMessage = this.sanitizeMessage(error.message);

    const logEntry: PrivacyPreservingLogEntry = {
      id: `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
      level: 'error',
      category: 'error',
      metadata: {
        fileSize: context.fileSize ? this.categorizeFileSize(context.fileSize) : undefined,
        pageCount: context.pageCount ? this.categorizePageCount(context.pageCount) : undefined,
        processingMethod: context.processingMethod,
        operationType: context.operationType,
        userAgent: this.sanitizeUserAgent(navigator.userAgent),
        deviceType: this.detectDeviceType()
      },
      message: sanitizedMessage,
      error: sanitizedError,
      privacy: {
        containsSensitiveData: false,
        processingLocation: 'client',
        dataEncrypted: true,
        complianceStatus: 'compliant'
      }
    };

    this.addLogEntry(logEntry);

    // Also track as analytics event
    this.trackAnalyticsEvent({
      eventType: 'operation_error',
      operationType: context.operationType || 'unknown',
      success: false,
      errorCategory: this.categorizeError(error)
    });
  }

  /**
   * Log performance metrics without sensitive data
   */
  public logPerformance(
    operationType: string,
    metrics: {
      processingTimeMs: number;
      memoryUsageMB: number;
      fileSize: number;
      pageCount: number;
      compressionRatio?: number;
      qualityScore?: number;
      processingMethod: string;
    }
  ): void {
    const logEntry: PrivacyPreservingLogEntry = {
      id: `perf_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
      level: 'info',
      category: 'performance',
      metadata: {
        fileSize: this.categorizeFileSize(metrics.fileSize),
        pageCount: this.categorizePageCount(metrics.pageCount),
        processingMethod: metrics.processingMethod,
        operationType: operationType,
        processingTimeMs: metrics.processingTimeMs
      },
      message: `${operationType} completed in ${metrics.processingTimeMs}ms`,
      performance: {
        memoryUsageMB: metrics.memoryUsageMB,
        cpuTime: metrics.processingTimeMs
      },
      privacy: {
        containsSensitiveData: false,
        processingLocation: metrics.processingMethod.includes('server') ? 'server' : 'client',
        dataEncrypted: true,
        complianceStatus: 'compliant'
      }
    };

    this.addLogEntry(logEntry);

    // Track analytics event
    this.trackAnalyticsEvent({
      eventType: 'operation_complete',
      operationType,
      duration: metrics.processingTimeMs,
      success: true,
      performanceMetrics: {
        speed: this.categorizeSpeed(metrics.processingTimeMs, metrics.fileSize),
        memoryUsage: this.categorizeMemoryUsage(metrics.memoryUsageMB),
        qualityScore: metrics.qualityScore || 95
      }
    });
  }

  /**
   * Log privacy compliance events
   */
  public logPrivacyEvent(
    eventType: 'data_encrypted' | 'memory_cleared' | 'server_communication' | 'compliance_check',
    details: string,
    complianceStatus: 'compliant' | 'warning' | 'violation'
  ): void {
    const logEntry: PrivacyPreservingLogEntry = {
      id: `privacy_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
      level: complianceStatus === 'violation' ? 'error' : complianceStatus === 'warning' ? 'warn' : 'info',
      category: 'privacy',
      metadata: {},
      message: this.sanitizeMessage(details),
      privacy: {
        containsSensitiveData: false,
        processingLocation: eventType === 'server_communication' ? 'server' : 'client',
        dataEncrypted: eventType === 'data_encrypted',
        complianceStatus
      }
    };

    this.addLogEntry(logEntry);
  }

  /**
   * Log user interactions (for UX analytics)
   */
  public logUserInteraction(
    action: string,
    context: {
      feature?: string;
      success?: boolean;
      duration?: number;
    } = {}
  ): void {
    const logEntry: PrivacyPreservingLogEntry = {
      id: `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
      level: 'info',
      category: 'user-action',
      metadata: {},
      message: `User action: ${action}`,
      privacy: {
        containsSensitiveData: false,
        processingLocation: 'client',
        dataEncrypted: false,
        complianceStatus: 'compliant'
      }
    };

    this.addLogEntry(logEntry);

    // Track analytics event
    this.trackAnalyticsEvent({
      eventType: 'user_interaction',
      operationType: action,
      duration: context.duration,
      success: context.success ?? true
    });
  }

  /**
   * Get privacy-compliant analytics summary
   */
  public getAnalyticsSummary(): {
    totalOperations: number;
    successRate: number;
    averageProcessingTime: number;
    mostCommonOperations: Array<{ operation: string; count: number }>;
    errorCategories: Array<{ category: string; count: number }>;
    performanceTrends: {
      fastOperations: number;
      mediumOperations: number;
      slowOperations: number;
    };
    privacyCompliance: {
      compliantEvents: number;
      warnings: number;
      violations: number;
    };
  } {
    const completedOperations = this.analyticsEvents.filter(e => 
      e.eventType === 'operation_complete' || e.eventType === 'operation_error'
    );

    const successfulOperations = completedOperations.filter(e => e.success).length;
    const totalOperations = completedOperations.length;
    const successRate = totalOperations > 0 ? (successfulOperations / totalOperations) * 100 : 100;

    const averageProcessingTime = completedOperations
      .filter(e => e.duration)
      .reduce((sum, e) => sum + (e.duration || 0), 0) / Math.max(completedOperations.length, 1);

    const operationCounts = completedOperations.reduce((acc, e) => {
      acc[e.operationType] = (acc[e.operationType] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const mostCommonOperations = Object.entries(operationCounts)
      .map(([operation, count]) => ({ operation, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5);

    const errorEvents = this.analyticsEvents.filter(e => e.eventType === 'operation_error');
    const errorCategoryCounts = errorEvents.reduce((acc, e) => {
      const category = e.errorCategory || 'unknown';
      acc[category] = (acc[category] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const errorCategories = Object.entries(errorCategoryCounts)
      .map(([category, count]) => ({ category, count }))
      .sort((a, b) => b.count - a.count);

    const performanceEvents = this.analyticsEvents.filter(e => e.performanceMetrics);
    const performanceTrends = performanceEvents.reduce(
      (acc, e) => {
        if (e.performanceMetrics) {
          acc[`${e.performanceMetrics.speed}Operations`]++;
        }
        return acc;
      },
      { fastOperations: 0, mediumOperations: 0, slowOperations: 0 }
    );

    const privacyEvents = this.logEntries.filter(e => e.category === 'privacy');
    const privacyCompliance = privacyEvents.reduce(
      (acc, e) => {
        acc[`${e.privacy.complianceStatus}${e.privacy.complianceStatus === 'compliant' ? 'Events' : 's'}`]++;
        return acc;
      },
      { compliantEvents: 0, warnings: 0, violations: 0 }
    );

    return {
      totalOperations,
      successRate,
      averageProcessingTime,
      mostCommonOperations,
      errorCategories,
      performanceTrends,
      privacyCompliance
    };
  }

  /**
   * Export privacy-compliant logs for debugging
   */
  public exportLogs(
    level?: 'info' | 'warn' | 'error',
    category?: 'performance' | 'error' | 'privacy' | 'compliance' | 'user-action',
    lastHours?: number
  ): PrivacyPreservingLogEntry[] {
    let filteredLogs = [...this.logEntries];

    if (level) {
      filteredLogs = filteredLogs.filter(log => log.level === level);
    }

    if (category) {
      filteredLogs = filteredLogs.filter(log => log.category === category);
    }

    if (lastHours) {
      const cutoff = new Date(Date.now() - lastHours * 60 * 60 * 1000);
      filteredLogs = filteredLogs.filter(log => log.timestamp > cutoff);
    }

    return filteredLogs;
  }

  /**
   * Clear old logs (privacy compliance)
   */
  public clearOldLogs(daysToKeep: number = 7): void {
    const cutoff = new Date(Date.now() - daysToKeep * 24 * 60 * 60 * 1000);
    
    this.logEntries = this.logEntries.filter(entry => entry.timestamp > cutoff);
    this.analyticsEvents = this.analyticsEvents.filter(event => 
      !event.duration || new Date().getTime() > cutoff.getTime()
    );
  }

  // Private helper methods

  private initializeSensitiveDataPatterns(): void {
    this.sensitiveDataPatterns = [
      /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g, // Email addresses
      /\b\d{3}-?\d{2}-?\d{4}\b/g, // SSN-like patterns
      /\b\d{4}[- ]?\d{4}[- ]?\d{4}[- ]?\d{4}\b/g, // Credit card-like patterns
      /bearer\s+[a-zA-Z0-9]+/gi, // API tokens
      /api[_-]?key[:\s]*[a-zA-Z0-9]+/gi, // API keys
      /password[:\s]*[^\s]+/gi, // Passwords
    ];
  }

  private sanitizeMessage(message: string): string {
    let sanitized = message;
    
    // Remove sensitive patterns
    for (const pattern of this.sensitiveDataPatterns) {
      sanitized = sanitized.replace(pattern, '[REDACTED]');
    }
    
    // Remove file paths that might contain user information
    sanitized = sanitized.replace(/[C-Z]:\\[^\\]*(?:\\[^\\]*)*|\/[^\/]*(?:\/[^\/]*)*/g, '[PATH]');
    
    // Remove potential PDF content snippets
    sanitized = sanitized.replace(/content[:\s]*[^\s]{10,}/gi, 'content: [REDACTED]');
    
    return sanitized;
  }

  private sanitizeError(error: Error): PrivacyPreservingLogEntry['error'] {
    let sanitizedStack = '';
    if (error.stack) {
      // Remove sensitive file paths and keep only function names and line numbers
      sanitizedStack = error.stack
        .split('\n')
        .map(line => {
          // Keep function names but remove file paths
          return line.replace(/\/[^\/\s]*\/[^\/\s]*\//g, '[PATH]/');
        })
        .join('\n');
    }

    return {
      name: error.name,
      code: (error as any).code,
      stack: sanitizedStack,
      cause: error.cause ? String(error.cause) : undefined
    };
  }

  private categorizeFileSize(sizeBytes: number): number {
    // Return size categories instead of exact sizes for privacy
    if (sizeBytes < 1024 * 1024) return 1; // < 1MB
    if (sizeBytes < 10 * 1024 * 1024) return 2; // 1-10MB
    if (sizeBytes < 50 * 1024 * 1024) return 3; // 10-50MB
    return 4; // > 50MB
  }

  private categorizePageCount(pages: number): number {
    if (pages <= 5) return 1;
    if (pages <= 20) return 2;
    if (pages <= 100) return 3;
    return 4;
  }

  private categorizeError(error: Error): string {
    if (error.name.includes('Network')) return 'network';
    if (error.name.includes('Memory')) return 'memory';
    if (error.name.includes('Security')) return 'security';
    if (error.name.includes('Permission')) return 'permission';
    if (error.message.includes('WASM')) return 'wasm';
    if (error.message.includes('PDF')) return 'pdf-parsing';
    return 'general';
  }

  private categorizeSpeed(processingTimeMs: number, fileSizeBytes: number): 'fast' | 'medium' | 'slow' {
    const sizeMB = fileSizeBytes / (1024 * 1024);
    const timePerMB = processingTimeMs / sizeMB;
    
    if (timePerMB < 1000) return 'fast';     // < 1 second per MB
    if (timePerMB < 5000) return 'medium';   // 1-5 seconds per MB
    return 'slow';                           // > 5 seconds per MB
  }

  private categorizeMemoryUsage(memoryMB: number): 'low' | 'medium' | 'high' {
    if (memoryMB < 100) return 'low';
    if (memoryMB < 500) return 'medium';
    return 'high';
  }

  private sanitizeUserAgent(userAgent: string): string {
    // Extract only browser type and major version for privacy
    if (userAgent.includes('Chrome')) return 'Chrome';
    if (userAgent.includes('Firefox')) return 'Firefox';
    if (userAgent.includes('Safari')) return 'Safari';
    if (userAgent.includes('Edge')) return 'Edge';
    return 'Unknown';
  }

  private detectDeviceType(): string {
    const userAgent = navigator.userAgent.toLowerCase();
    if (/mobile|android|iphone|ipod/.test(userAgent)) return 'mobile';
    if (/ipad|tablet/.test(userAgent)) return 'tablet';
    return 'desktop';
  }

  private addLogEntry(entry: PrivacyPreservingLogEntry): void {
    this.logEntries.push(entry);
    
    // Keep only the most recent entries
    if (this.logEntries.length > this.maxLogEntries) {
      this.logEntries = this.logEntries.slice(-this.maxLogEntries * 0.8);
    }

    // Console logging for development (sanitized)
    const consoleMessage = `[${entry.level.toUpperCase()}] ${entry.category}: ${entry.message}`;
    
    switch (entry.level) {
      case 'error':
        console.error(consoleMessage, entry.error);
        break;
      case 'warn':
        console.warn(consoleMessage);
        break;
      case 'debug':
        console.debug(consoleMessage);
        break;
      default:
        console.log(consoleMessage);
    }
  }

  private trackAnalyticsEvent(event: AnalyticsEvent): void {
    this.analyticsEvents.push(event);
    
    // Keep only recent analytics events
    if (this.analyticsEvents.length > 1000) {
      this.analyticsEvents = this.analyticsEvents.slice(-500);
    }
  }

  /**
   * Cleanup method
   */
  public cleanup(): void {
    this.logEntries.length = 0;
    this.analyticsEvents.length = 0;
  }
}
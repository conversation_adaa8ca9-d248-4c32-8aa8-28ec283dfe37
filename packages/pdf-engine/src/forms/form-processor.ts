/**
 * Form Field Processing System for MuPDF WASM PDF Engine
 * 
 * Provides comprehensive form field detection, creation, editing, and data extraction capabilities
 * with validation and formatting options for interactive PDF forms.
 */

import { FormField, FormValidation, BoundingBox, Position, ProcessingProgressCallback } from '../types/processing';

export interface FormData {
  fields: Record<string, string | boolean | string[]>;
  metadata: {
    formTitle?: string;
    version?: string;
    createdDate?: Date;
    modifiedDate?: Date;
  };
}

export interface FormExportOptions {
  format: 'json' | 'csv' | 'xml' | 'pdf-fdf';
  includeMetadata?: boolean;
  flattenStructure?: boolean;
}

export interface FormCreationOptions {
  fieldType: FormField['type'];
  name: string;
  position: Position;
  size: { width: number; height: number };
  value?: string | boolean;
  options?: string[];
  validation?: FormValidation;
  required?: boolean;
  readonly?: boolean;
  label?: string;
  tooltip?: string;
}

export interface FormProcessingResult {
  success: boolean;
  fieldsProcessed: number;
  validationErrors: Array<{
    fieldId: string;
    fieldName: string;
    error: string;
    value: any;
  }>;
  extractedData?: FormData;
  processingTimeMs: number;
}

/**
 * Advanced Form Field Processing System
 * 
 * Handles detection, creation, editing, and data extraction for PDF forms
 * with comprehensive validation and export capabilities.
 */
export class FormProcessor {
  private static instance: FormProcessor;
  private wasmModule: any = null;
  private isInitialized = false;

  private constructor() {}

  public static getInstance(): FormProcessor {
    if (!FormProcessor.instance) {
      FormProcessor.instance = new FormProcessor();
    }
    return FormProcessor.instance;
  }

  /**
   * Initialize the form processor with MuPDF WASM integration
   */
  public async initialize(wasmModule?: any): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      // Initialize with provided WASM module or load dynamically
      if (wasmModule) {
        this.wasmModule = wasmModule;
      } else {
        // Dynamically import MuPDF wrapper if available
        try {
          const { MuPDFWrapper } = await import('../wasm/mupdf-wrapper');
          this.wasmModule = MuPDFWrapper.getInstance();
          await this.wasmModule.initialize();
        } catch (error) {
          console.warn('MuPDF WASM not available for form processing, using fallback methods');
          this.wasmModule = null;
        }
      }

      this.isInitialized = true;
      console.log('FormProcessor initialized successfully');
    } catch (error) {
      console.error('FormProcessor initialization failed:', error);
      throw new Error(`Form processor initialization failed: ${error}`);
    }
  }

  /**
   * Detect and extract all form fields from a PDF document
   * AC: 8.1 - Form field detection and extraction with field type recognition
   */
  public async detectFormFields(
    document: ArrayBuffer,
    progressCallback?: ProcessingProgressCallback
  ): Promise<FormField[]> {
    await this.initialize();
    const startTime = performance.now();

    try {
      progressCallback?.({
        stage: 'loading',
        percentage: 0,
        currentStep: 'Loading document for form field detection...'
      });

      let formFields: FormField[] = [];

      if (this.wasmModule) {
        // Use MuPDF WASM for advanced form field detection
        formFields = await this.detectFormFieldsWithWASM(document, progressCallback);
      } else {
        // Fallback to JavaScript-based detection using pdf-lib
        formFields = await this.detectFormFieldsWithJS(document, progressCallback);
      }

      progressCallback?.({
        stage: 'finalizing',
        percentage: 100,
        currentStep: `Detected ${formFields.length} form fields`
      });

      const processingTime = performance.now() - startTime;
      console.log(`Form field detection completed in ${processingTime.toFixed(2)}ms`);

      return formFields;
    } catch (error) {
      throw new Error(`Form field detection failed: ${error}`);
    }
  }

  /**
   * Create new form fields in a PDF document
   * AC: 8.2 - Form field creation tools with validation and formatting options
   */
  public async createFormField(
    document: ArrayBuffer,
    fieldOptions: FormCreationOptions,
    progressCallback?: ProcessingProgressCallback
  ): Promise<ArrayBuffer> {
    await this.initialize();
    const startTime = performance.now();

    try {
      progressCallback?.({
        stage: 'processing',
        percentage: 0,
        currentStep: `Creating ${fieldOptions.fieldType} field: ${fieldOptions.name}...`
      });

      // Validate field creation options
      this.validateFieldCreationOptions(fieldOptions);

      let processedDocument: ArrayBuffer;

      if (this.wasmModule) {
        // Use MuPDF WASM for advanced form field creation
        processedDocument = await this.createFormFieldWithWASM(document, fieldOptions, progressCallback);
      } else {
        // Fallback to JavaScript-based field creation
        processedDocument = await this.createFormFieldWithJS(document, fieldOptions, progressCallback);
      }

      progressCallback?.({
        stage: 'finalizing',
        percentage: 100,
        currentStep: 'Form field created successfully'
      });

      const processingTime = performance.now() - startTime;
      console.log(`Form field creation completed in ${processingTime.toFixed(2)}ms`);

      return processedDocument;
    } catch (error) {
      throw new Error(`Form field creation failed: ${error}`);
    }
  }

  /**
   * Interactive form filling interface with data validation
   * AC: 8.3 - Interactive form filling interface with data validation
   */
  public async fillFormFields(
    document: ArrayBuffer,
    formData: FormData,
    validateData: boolean = true,
    progressCallback?: ProcessingProgressCallback
  ): Promise<FormProcessingResult> {
    await this.initialize();
    const startTime = performance.now();

    try {
      progressCallback?.({
        stage: 'loading',
        percentage: 0,
        currentStep: 'Analyzing form structure...'
      });

      // First detect existing form fields
      const existingFields = await this.detectFormFields(document);
      const fieldMap = new Map(existingFields.map(field => [field.name, field]));

      progressCallback?.({
        stage: 'processing',
        percentage: 25,
        currentStep: 'Validating form data...'
      });

      const validationErrors: FormProcessingResult['validationErrors'] = [];
      let fieldsProcessed = 0;

      // Validate form data if requested
      if (validateData) {
        for (const [fieldName, value] of Object.entries(formData.fields)) {
          const field = fieldMap.get(fieldName);
          if (field) {
            const fieldValidationErrors = this.validateFieldValue(field, value);
            validationErrors.push(...fieldValidationErrors);
          }
        }
      }

      progressCallback?.({
        stage: 'processing',
        percentage: 50,
        currentStep: 'Filling form fields...'
      });

      // Fill the form fields
      let processedDocument: ArrayBuffer;
      
      if (this.wasmModule && validationErrors.length === 0) {
        processedDocument = await this.fillFormFieldsWithWASM(
          document, 
          formData, 
          existingFields, 
          progressCallback
        );
        fieldsProcessed = Object.keys(formData.fields).length;
      } else if (validationErrors.length === 0) {
        processedDocument = await this.fillFormFieldsWithJS(
          document, 
          formData, 
          existingFields, 
          progressCallback
        );
        fieldsProcessed = Object.keys(formData.fields).length;
      } else {
        processedDocument = document; // No processing if validation errors
      }

      progressCallback?.({
        stage: 'finalizing',
        percentage: 100,
        currentStep: 'Form filling completed'
      });

      const processingTime = performance.now() - startTime;

      return {
        success: validationErrors.length === 0,
        fieldsProcessed,
        validationErrors,
        extractedData: formData,
        processingTimeMs: processingTime
      };
    } catch (error) {
      throw new Error(`Form filling failed: ${error}`);
    }
  }

  /**
   * Export form data in various formats
   * AC: 8.4 - Form data export capabilities (JSON, CSV, XML formats)
   */
  public async exportFormData(
    document: ArrayBuffer,
    exportOptions: FormExportOptions,
    progressCallback?: ProcessingProgressCallback
  ): Promise<string | ArrayBuffer> {
    await this.initialize();
    const startTime = performance.now();

    try {
      progressCallback?.({
        stage: 'loading',
        percentage: 0,
        currentStep: 'Extracting form data...'
      });

      // Extract form data from document
      const formFields = await this.detectFormFields(document);
      const formData: FormData = {
        fields: {},
        metadata: {
          createdDate: new Date(),
          version: '1.0'
        }
      };

      // Populate form data
      for (const field of formFields) {
        if (field.value !== undefined) {
          formData.fields[field.name] = field.value;
        }
      }

      progressCallback?.({
        stage: 'processing',
        percentage: 50,
        currentStep: `Exporting to ${exportOptions.format.toUpperCase()}...`
      });

      let exportResult: string | ArrayBuffer;

      switch (exportOptions.format) {
        case 'json':
          exportResult = this.exportToJSON(formData, exportOptions);
          break;
        case 'csv':
          exportResult = this.exportToCSV(formData, exportOptions);
          break;
        case 'xml':
          exportResult = this.exportToXML(formData, exportOptions);
          break;
        case 'pdf-fdf':
          exportResult = await this.exportToFDF(formData, exportOptions);
          break;
        default:
          throw new Error(`Unsupported export format: ${exportOptions.format}`);
      }

      progressCallback?.({
        stage: 'finalizing',
        percentage: 100,
        currentStep: 'Export completed successfully'
      });

      const processingTime = performance.now() - startTime;
      console.log(`Form data export completed in ${processingTime.toFixed(2)}ms`);

      return exportResult;
    } catch (error) {
      throw new Error(`Form data export failed: ${error}`);
    }
  }

  /**
   * Process form submission workflows
   * AC: 8.5 - Form submission handling and data processing workflows
   */
  public async processFormSubmission(
    document: ArrayBuffer,
    submissionData: FormData,
    workflowOptions: {
      validateRequired?: boolean;
      generateReceipt?: boolean;
      archiveSubmission?: boolean;
      notificationEmail?: string;
    } = {},
    progressCallback?: ProcessingProgressCallback
  ): Promise<{
    success: boolean;
    submissionId: string;
    receipt?: ArrayBuffer;
    validationErrors: FormProcessingResult['validationErrors'];
    processingTimeMs: number;
  }> {
    await this.initialize();
    const startTime = performance.now();

    try {
      progressCallback?.({
        stage: 'loading',
        percentage: 0,
        currentStep: 'Processing form submission...'
      });

      // Generate unique submission ID
      const submissionId = `form_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      // Validate submission if required
      const validationErrors: FormProcessingResult['validationErrors'] = [];
      
      if (workflowOptions.validateRequired) {
        const formFields = await this.detectFormFields(document);
        const requiredFields = formFields.filter(field => field.required);
        
        for (const field of requiredFields) {
          if (!(field.name in submissionData.fields) || !submissionData.fields[field.name]) {
            validationErrors.push({
              fieldId: field.id,
              fieldName: field.name,
              error: 'Required field is empty',
              value: submissionData.fields[field.name] || null
            });
          }
        }
      }

      progressCallback?.({
        stage: 'processing',
        percentage: 40,
        currentStep: 'Validating submission data...'
      });

      let receipt: ArrayBuffer | undefined;
      
      if (workflowOptions.generateReceipt && validationErrors.length === 0) {
        progressCallback?.({
          stage: 'processing',
          percentage: 70,
          currentStep: 'Generating submission receipt...'
        });
        
        receipt = await this.generateSubmissionReceipt(
          document, 
          submissionData, 
          submissionId
        );
      }

      progressCallback?.({
        stage: 'finalizing',
        percentage: 100,
        currentStep: 'Submission processing completed'
      });

      const processingTime = performance.now() - startTime;

      return {
        success: validationErrors.length === 0,
        submissionId,
        receipt,
        validationErrors,
        processingTimeMs: processingTime
      };
    } catch (error) {
      throw new Error(`Form submission processing failed: ${error}`);
    }
  }

  // Private helper methods

  private async detectFormFieldsWithWASM(
    document: ArrayBuffer,
    progressCallback?: ProcessingProgressCallback
  ): Promise<FormField[]> {
    try {
      // Load document in MuPDF WASM
      const pdfDoc = await this.wasmModule.loadDocument(document);
      const formFields: FormField[] = [];

      progressCallback?.({
        stage: 'processing',
        percentage: 25,
        currentStep: 'Analyzing document with MuPDF WASM...'
      });

      // Get form fields using MuPDF WASM APIs
      const wasmFormFields = await this.wasmModule.getFormFields(pdfDoc);
      
      progressCallback?.({
        stage: 'processing',
        percentage: 75,
        currentStep: 'Processing detected form fields...'
      });

      for (const wasmField of wasmFormFields) {
        const formField: FormField = {
          id: wasmField.id || `field_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          name: wasmField.name,
          type: this.mapWASMFieldType(wasmField.type),
          pageNum: wasmField.pageNum,
          bbox: wasmField.bbox,
          value: wasmField.value,
          options: wasmField.options,
          required: wasmField.required || false,
          readonly: wasmField.readonly || false,
          validation: wasmField.validation
        };
        
        formFields.push(formField);
      }

      return formFields;
    } catch (error) {
      console.warn('WASM form field detection failed, falling back to JavaScript:', error);
      return this.detectFormFieldsWithJS(document, progressCallback);
    }
  }

  private async detectFormFieldsWithJS(
    document: ArrayBuffer,
    progressCallback?: ProcessingProgressCallback
  ): Promise<FormField[]> {
    try {
      const { PDFDocument } = await import('pdf-lib');
      const pdfDoc = await PDFDocument.load(document);

      progressCallback?.({
        stage: 'processing',
        percentage: 25,
        currentStep: 'Analyzing document with pdf-lib...'
      });

      const formFields: FormField[] = [];
      const form = pdfDoc.getForm();
      const fields = form.getFields();

      progressCallback?.({
        stage: 'processing',
        percentage: 75,
        currentStep: 'Processing detected form fields...'
      });

      for (let i = 0; i < fields.length; i++) {
        const field = fields[i];
        const fieldName = field.getName();
        
        let fieldType: FormField['type'] = 'text';
        let value: string | boolean | undefined;
        let options: string[] | undefined;

        // Determine field type and extract value
        if (field.constructor.name.includes('TextField')) {
          fieldType = 'text';
          value = (field as any).getText?.();
        } else if (field.constructor.name.includes('CheckBox')) {
          fieldType = 'checkbox';
          value = (field as any).isChecked?.();
        } else if (field.constructor.name.includes('RadioGroup')) {
          fieldType = 'radio';
          value = (field as any).getSelected?.();
          options = (field as any).getOptions?.();
        } else if (field.constructor.name.includes('Dropdown')) {
          fieldType = 'dropdown';
          value = (field as any).getSelected?.();
          options = (field as any).getOptions?.();
        } else if (field.constructor.name.includes('Button')) {
          fieldType = 'button';
        }

        const formField: FormField = {
          id: `field_${i}_${fieldName}`,
          name: fieldName,
          type: fieldType,
          pageNum: 0, // pdf-lib doesn't easily provide page number
          bbox: { x: 0, y: 0, width: 100, height: 20 }, // Default bbox
          value,
          options,
          required: false, // pdf-lib doesn't provide required field info easily
          readonly: (field as any).isReadOnly?.() || false
        };

        formFields.push(formField);
      }

      return formFields;
    } catch (error) {
      console.error('JavaScript form field detection failed:', error);
      return []; // Return empty array as fallback
    }
  }

  private validateFieldCreationOptions(options: FormCreationOptions): void {
    if (!options.name) {
      throw new Error('Field name is required');
    }
    
    if (!options.position || typeof options.position.x !== 'number' || typeof options.position.y !== 'number') {
      throw new Error('Valid position coordinates are required');
    }
    
    if (!options.size || options.size.width <= 0 || options.size.height <= 0) {
      throw new Error('Valid field size is required');
    }

    // Type-specific validations
    if (options.fieldType === 'dropdown' || options.fieldType === 'radio') {
      if (!options.options || options.options.length === 0) {
        throw new Error(`Options array is required for ${options.fieldType} fields`);
      }
    }
  }

  private validateFieldValue(field: FormField, value: any): FormProcessingResult['validationErrors'] {
    const errors: FormProcessingResult['validationErrors'] = [];
    
    if (!field.validation) {
      return errors;
    }

    const validation = field.validation;
    const stringValue = String(value);

    // Required field validation
    if (field.required && (!value || stringValue.trim() === '')) {
      errors.push({
        fieldId: field.id,
        fieldName: field.name,
        error: 'This field is required',
        value
      });
      return errors;
    }

    // Pattern validation
    if (validation.pattern && stringValue) {
      const regex = new RegExp(validation.pattern);
      if (!regex.test(stringValue)) {
        errors.push({
          fieldId: field.id,
          fieldName: field.name,
          error: 'Value does not match required pattern',
          value
        });
      }
    }

    // Length validations
    if (validation.minLength && stringValue.length < validation.minLength) {
      errors.push({
        fieldId: field.id,
        fieldName: field.name,
        error: `Minimum length is ${validation.minLength} characters`,
        value
      });
    }

    if (validation.maxLength && stringValue.length > validation.maxLength) {
      errors.push({
        fieldId: field.id,
        fieldName: field.name,
        error: `Maximum length is ${validation.maxLength} characters`,
        value
      });
    }

    // Numeric validation
    if (validation.numeric && isNaN(Number(stringValue))) {
      errors.push({
        fieldId: field.id,
        fieldName: field.name,
        error: 'Value must be numeric',
        value
      });
    }

    // Email validation
    if (validation.email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(stringValue)) {
        errors.push({
          fieldId: field.id,
          fieldName: field.name,
          error: 'Invalid email format',
          value
        });
      }
    }

    return errors;
  }

  private async createFormFieldWithWASM(
    document: ArrayBuffer,
    options: FormCreationOptions,
    progressCallback?: ProcessingProgressCallback
  ): Promise<ArrayBuffer> {
    // Implementation for WASM-based form field creation
    // This would use MuPDF WASM APIs for advanced field creation
    throw new Error('WASM form field creation not yet implemented');
  }

  private async createFormFieldWithJS(
    document: ArrayBuffer,
    options: FormCreationOptions,
    progressCallback?: ProcessingProgressCallback
  ): Promise<ArrayBuffer> {
    const { PDFDocument } = await import('pdf-lib');
    const pdfDoc = await PDFDocument.load(document);
    const form = pdfDoc.getForm();

    progressCallback?.({
      stage: 'processing',
      percentage: 50,
      currentStep: `Creating ${options.fieldType} field...`
    });

    // Create field based on type
    switch (options.fieldType) {
      case 'text':
        const textField = form.createTextField(options.name);
        if (options.value && typeof options.value === 'string') {
          textField.setText(options.value);
        }
        if (options.readonly) {
          textField.enableReadOnly();
        }
        break;

      case 'checkbox':
        const checkBox = form.createCheckBox(options.name);
        if (options.value === true) {
          checkBox.check();
        } else if (options.value === false) {
          checkBox.uncheck();
        }
        if (options.readonly) {
          checkBox.enableReadOnly();
        }
        break;

      case 'dropdown':
        if (!options.options) {
          throw new Error('Options required for dropdown field');
        }
        const dropdown = form.createDropdown(options.name);
        dropdown.setOptions(options.options);
        if (options.value && typeof options.value === 'string') {
          dropdown.select(options.value);
        }
        if (options.readonly) {
          dropdown.enableReadOnly();
        }
        break;

      case 'radio':
        if (!options.options) {
          throw new Error('Options required for radio field');
        }
        const radioGroup = form.createRadioGroup(options.name);
        for (const option of options.options) {
          radioGroup.addOptionToPage(option, pdfDoc.getPage(0)); // Add to first page for simplicity
        }
        if (options.value && typeof options.value === 'string') {
          radioGroup.select(options.value);
        }
        if (options.readonly) {
          radioGroup.enableReadOnly();
        }
        break;

      case 'button':
        const button = form.createButton(options.name);
        // Buttons are typically not filled with values
        if (options.readonly) {
          button.enableReadOnly();
        }
        break;
    }

    return new Uint8Array(await pdfDoc.save()).buffer;
  }

  private async fillFormFieldsWithWASM(
    document: ArrayBuffer,
    formData: FormData,
    existingFields: FormField[],
    progressCallback?: ProcessingProgressCallback
  ): Promise<ArrayBuffer> {
    // Implementation for WASM-based form filling
    // This would use MuPDF WASM APIs for advanced form filling
    throw new Error('WASM form filling not yet implemented');
  }

  private async fillFormFieldsWithJS(
    document: ArrayBuffer,
    formData: FormData,
    existingFields: FormField[],
    progressCallback?: ProcessingProgressCallback
  ): Promise<ArrayBuffer> {
    const { PDFDocument } = await import('pdf-lib');
    const pdfDoc = await PDFDocument.load(document);
    const form = pdfDoc.getForm();

    let fieldsProcessed = 0;
    const totalFields = Object.keys(formData.fields).length;

    for (const [fieldName, value] of Object.entries(formData.fields)) {
      try {
        const field = form.getField(fieldName);
        const fieldInfo = existingFields.find(f => f.name === fieldName);
        
        if (fieldInfo) {
          switch (fieldInfo.type) {
            case 'text':
              if (typeof value === 'string') {
                (field as any).setText(value);
              }
              break;
            case 'checkbox':
              if (typeof value === 'boolean') {
                if (value) {
                  (field as any).check();
                } else {
                  (field as any).uncheck();
                }
              }
              break;
            case 'dropdown':
            case 'radio':
              if (typeof value === 'string') {
                (field as any).select(value);
              }
              break;
          }
        }
        
        fieldsProcessed++;
        
        progressCallback?.({
          stage: 'processing',
          percentage: 50 + (fieldsProcessed / totalFields) * 40,
          currentStep: `Filled field: ${fieldName}`
        });
      } catch (error) {
        console.warn(`Failed to fill field ${fieldName}:`, error);
      }
    }

    return new Uint8Array(await pdfDoc.save()).buffer;
  }

  private exportToJSON(formData: FormData, options: FormExportOptions): string {
    const exportData = options.flattenStructure ? 
      formData.fields : 
      formData;

    return JSON.stringify(exportData, null, options.includeMetadata ? 2 : 0);
  }

  private exportToCSV(formData: FormData, options: FormExportOptions): string {
    const headers = ['Field Name', 'Value'];
    const rows = [headers];

    for (const [fieldName, value] of Object.entries(formData.fields)) {
      const csvValue = Array.isArray(value) ? value.join(';') : String(value);
      rows.push([fieldName, csvValue]);
    }

    return rows.map(row => row.map(cell => `"${cell}"`).join(',')).join('\n');
  }

  private exportToXML(formData: FormData, options: FormExportOptions): string {
    let xml = '<?xml version="1.0" encoding="UTF-8"?>\n<form>\n';
    
    if (options.includeMetadata && formData.metadata) {
      xml += '  <metadata>\n';
      for (const [key, value] of Object.entries(formData.metadata)) {
        xml += `    <${key}>${value}</${key}>\n`;
      }
      xml += '  </metadata>\n';
    }
    
    xml += '  <fields>\n';
    for (const [fieldName, value] of Object.entries(formData.fields)) {
      const xmlValue = Array.isArray(value) ? value.join(',') : String(value);
      xml += `    <field name="${fieldName}">${xmlValue}</field>\n`;
    }
    xml += '  </fields>\n</form>';
    
    return xml;
  }

  private async exportToFDF(formData: FormData, options: FormExportOptions): Promise<ArrayBuffer> {
    // FDF (Forms Data Format) export - simplified implementation
    const fdfContent = `%FDF-1.2
1 0 obj
<<
/FDF
<<
/Fields [
${Object.entries(formData.fields).map(([name, value]) => 
  `<<
/T (${name})
/V (${value})
>>`
).join('\n')}
]
>>
>>
endobj
trailer

<<
/Root 1 0 R
>>
%%EOF`;

    return new TextEncoder().encode(fdfContent).buffer;
  }

  private async generateSubmissionReceipt(
    document: ArrayBuffer,
    submissionData: FormData,
    submissionId: string
  ): Promise<ArrayBuffer> {
    const { PDFDocument, rgb } = await import('pdf-lib');
    const receiptDoc = await PDFDocument.create();
    const page = receiptDoc.addPage([600, 800]);
    
    const { width, height } = page.getSize();
    
    // Add receipt header
    page.drawText('Form Submission Receipt', {
      x: 50,
      y: height - 100,
      size: 24,
      color: rgb(0, 0, 0)
    });
    
    page.drawText(`Submission ID: ${submissionId}`, {
      x: 50,
      y: height - 140,
      size: 12,
      color: rgb(0.5, 0.5, 0.5)
    });
    
    page.drawText(`Date: ${new Date().toLocaleDateString()}`, {
      x: 50,
      y: height - 160,
      size: 12,
      color: rgb(0.5, 0.5, 0.5)
    });
    
    // Add submitted data
    let yPosition = height - 220;
    page.drawText('Submitted Data:', {
      x: 50,
      y: yPosition,
      size: 16,
      color: rgb(0, 0, 0)
    });
    
    yPosition -= 30;
    for (const [fieldName, value] of Object.entries(submissionData.fields)) {
      if (yPosition < 50) break; // Prevent overflow
      
      page.drawText(`${fieldName}: ${value}`, {
        x: 70,
        y: yPosition,
        size: 10,
        color: rgb(0.2, 0.2, 0.2)
      });
      yPosition -= 20;
    }
    
    return new Uint8Array(await receiptDoc.save()).buffer;
  }

  private mapWASMFieldType(wasmType: string): FormField['type'] {
    // Map MuPDF WASM field types to our standard types
    switch (wasmType?.toLowerCase()) {
      case 'text':
      case 'textfield':
        return 'text';
      case 'checkbox':
      case 'check':
        return 'checkbox';
      case 'radiobutton':
      case 'radio':
        return 'radio';
      case 'combobox':
      case 'dropdown':
      case 'choice':
        return 'dropdown';
      case 'button':
      case 'pushbutton':
        return 'button';
      default:
        return 'text'; // Default fallback
    }
  }

  /**
   * Get form processing statistics
   */
  public getProcessingStats(): {
    totalFormsProcessed: number;
    avgFieldsPerForm: number;
    mostCommonFieldType: string;
    successRate: number;
  } {
    // In a real implementation, this would track actual statistics
    return {
      totalFormsProcessed: 0,
      avgFieldsPerForm: 0,
      mostCommonFieldType: 'text',
      successRate: 100
    };
  }

  /**
   * Cleanup resources
   */
  public async cleanup(): Promise<void> {
    this.wasmModule = null;
    this.isInitialized = false;
    console.log('FormProcessor cleanup completed');
  }
}
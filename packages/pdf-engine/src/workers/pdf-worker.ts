import { PDFProcessingEngine } from '../engine.js';
import type {
  ProcessingOptions,
  SignatureData,
  Position,
  WatermarkOptions,
  ProcessingProgress
} from '../types/processing.js';

export interface WorkerMessage {
  id: string;
  type: 'process' | 'compress' | 'addSignature' | 'addWatermark' | 'validate' | 'getInfo';
  payload: {
    file?: ArrayBuffer;
    fileName?: string;
    options?: ProcessingOptions;
    targetSize?: number;
    signature?: SignatureData;
    position?: Position;
    watermark?: WatermarkOptions;
  };
}

export interface WorkerResponse {
  id: string;
  type: 'success' | 'error' | 'progress';
  payload: any;
}

declare const self: DedicatedWorkerGlobalScope;

class PDFWorkerService {
  private engine: PDFProcessingEngine;
  private activeProcesses = new Map<string, boolean>();

  constructor() {
    this.engine = PDFProcessingEngine.getInstance();
    this.setupMessageHandler();
  }

  private setupMessageHandler(): void {
    self.addEventListener('message', async (event: MessageEvent<WorkerMessage>) => {
      const { id, type, payload } = event.data;

      try {
        this.activeProcesses.set(id, true);

        switch (type) {
          case 'process':
            await this.handleProcess(id, payload);
            break;
          case 'compress':
            await this.handleCompress(id, payload);
            break;
          case 'addSignature':
            await this.handleAddSignature(id, payload);
            break;
          case 'addWatermark':
            await this.handleAddWatermark(id, payload);
            break;
          case 'validate':
            await this.handleValidate(id, payload);
            break;
          case 'getInfo':
            await this.handleGetInfo(id, payload);
            break;
          default:
            this.sendError(id, new Error(`Unknown message type: ${type}`));
        }
      } catch (error) {
        this.sendError(id, error instanceof Error ? error : new Error('Unknown error'));
      } finally {
        this.activeProcesses.delete(id);
      }
    });
  }

  private async handleProcess(id: string, payload: any): Promise<void> {
    if (!payload.file || !payload.fileName) {
      throw new Error('File data and filename are required');
    }

    const file = new File([payload.file], payload.fileName, { type: 'application/pdf' });
    const options = payload.options || {};

    const progressCallback = (progress: ProcessingProgress) => {
      this.sendProgress(id, progress);
    };

    const result = await this.engine.processDocument(file, options, progressCallback);
    this.sendSuccess(id, result);
  }

  private async handleCompress(id: string, payload: any): Promise<void> {
    if (!payload.file || !payload.fileName || !payload.targetSize) {
      throw new Error('File data, filename, and target size are required');
    }

    const file = new File([payload.file], payload.fileName, { type: 'application/pdf' });

    const progressCallback = (progress: ProcessingProgress) => {
      this.sendProgress(id, progress);
    };

    const result = await this.engine.compressToTarget(file, payload.targetSize, progressCallback);
    this.sendSuccess(id, { data: result });
  }

  private async handleAddSignature(id: string, payload: any): Promise<void> {
    if (!payload.file || !payload.fileName || !payload.signature || !payload.position) {
      throw new Error('File data, filename, signature, and position are required');
    }

    const file = new File([payload.file], payload.fileName, { type: 'application/pdf' });
    const result = await this.engine.addSignature(file, payload.signature, payload.position);
    this.sendSuccess(id, { data: result });
  }

  private async handleAddWatermark(id: string, payload: any): Promise<void> {
    if (!payload.file || !payload.fileName || !payload.watermark) {
      throw new Error('File data, filename, and watermark options are required');
    }

    const file = new File([payload.file], payload.fileName, { type: 'application/pdf' });
    const result = await this.engine.addWatermark(file, payload.watermark);
    this.sendSuccess(id, { data: result });
  }

  private async handleValidate(id: string, payload: any): Promise<void> {
    if (!payload.file || !payload.fileName) {
      throw new Error('File data and filename are required');
    }

    const file = new File([payload.file], payload.fileName, { type: 'application/pdf' });
    const isValid = await this.engine.validatePDF(file);
    this.sendSuccess(id, { valid: isValid });
  }

  private async handleGetInfo(id: string, payload: any): Promise<void> {
    if (!payload.file || !payload.fileName) {
      throw new Error('File data and filename are required');
    }

    const file = new File([payload.file], payload.fileName, { type: 'application/pdf' });
    const info = await this.engine.getPDFInfo(file);
    this.sendSuccess(id, info);
  }

  private sendSuccess(id: string, payload: any): void {
    const response: WorkerResponse = {
      id,
      type: 'success',
      payload
    };
    self.postMessage(response);
  }

  private sendError(id: string, error: Error): void {
    const response: WorkerResponse = {
      id,
      type: 'error',
      payload: {
        message: error.message,
        name: error.name,
        code: (error as any).code || 'UNKNOWN_ERROR'
      }
    };
    self.postMessage(response);
  }

  private sendProgress(id: string, progress: ProcessingProgress): void {
    const response: WorkerResponse = {
      id,
      type: 'progress',
      payload: progress
    };
    self.postMessage(response);
  }
}

// Initialize the worker service
new PDFWorkerService();
export interface User {
  id: string;              // UUID from Supabase Auth
  email: string;           // Authentication email, unique
  name: string;            // Display name for UI
  avatar_url: string | null; // Profile image URL
  subscription_tier: 'free' | 'premium'; // Current subscription level
  role: 'user' | 'admin';  // User role for admin access
  created_at: string;      // Account creation date
  updated_at: string;      // Last profile modification
}

export interface Session {
  access_token: string;
  refresh_token: string;
  expires_at: number;
  user: User;
}

export interface AuthState {
  user: User | null;
  isLoading: boolean;
  session: Session | null;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterCredentials {
  email: string;
  password: string;
  name: string;
}

export interface AuthResult {
  user: User;
  session: Session;
}

export interface Subscription {
  id: string;
  userId: string;
  plan: 'free' | 'pro' | 'enterprise';
  status: 'active' | 'cancelled' | 'past_due';
  currentPeriodEnd: Date;
}

export interface ProcessingOptions {
  quality: 'low' | 'medium' | 'high';
  compression: boolean;
  watermark?: string;
}

export interface ProcessingHistory {
  id: string;
  user_id: string;
  original_filename: string;
  operation_type: 'compress' | 'sign' | 'batch' | 'watermark';
  settings: {
    target_size?: number;
    quality_level?: number;
    // ... other processing parameters
  };
  processing_time_ms: number;
  file_size_before: number;
  file_size_after: number;
  created_at: string;
}

export interface UserSession {
  id: string;
  user_id: string;
  user_email: string;
  user_name: string;
  created_at: string;
  last_activity: string;
  expires_at: string;
  ip_address?: string;
  user_agent?: string;
  status: 'active' | 'expired' | 'revoked';
}

export interface SessionStats {
  total_active_sessions: number;
  total_users_online: number;
  average_session_duration: number;
  sessions_today: number;
}

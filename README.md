All processes are now stopped. Here are the manual commands to run each component individually:

Manual Component Commands

1. Web Application (Frontend) - Required for UI

cd apps/web
npm run dev
- URL: http://localhost:5173/
- Purpose: React frontend with PDF processing UI

2. API Server (Backend) - Optional

cd apps/api
npm run dev
- URL: http://localhost:3000
- Purpose: Next.js API for user management, history
tracking

3. PDF Engine - Optional (for development)

cd packages/pdf-engine
npm run dev
- Purpose: TypeScript compilation in watch mode for PDF
processing engine

4. Shared Package - Optional (for development)

cd packages/shared
npm run dev
- Purpose: Shared utilities and types compilation

Minimal Setup for UI Access

Just run this one command:
cd apps/web && npm run dev

This gives you the full PDF processing interface at
http://localhost:5173/ since all PDF processing happens
client-side in the browser.

Full Development Setup

If you want everything running with hot-reload:
# Terminal 1 - Web App (Required)
cd apps/web && npm run dev

# Terminal 2 - API Server (Optional)  
cd apps/api && npm run dev

# Terminal 3 - PDF Engine Watch (Optional)
cd packages/pdf-engine && npm run dev

The web app is all you need to see and use the PDF
processing functionality in the browser.
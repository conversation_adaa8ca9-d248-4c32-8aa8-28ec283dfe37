# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

MobilePDF Pro is a Progressive Web Application (PWA) for client-side PDF processing with mobile-first design. All PDF processing happens in the browser using WebAssembly for privacy and performance.

## Architecture

This is a **Turborepo monorepo** with npm workspaces:

### Apps
- `apps/web` - React frontend (Vite + TypeScript + TailwindCSS)
- `apps/api` - Next.js API server for user management (optional)

### Packages  
- `packages/pdf-engine` - Core PDF processing engine with WebAssembly
- `packages/shared` - Shared types and utilities
- `packages/ui` - Shared UI components
- `packages/config` - Shared configuration

## Essential Development Commands

### Quick Start (Minimal Setup)
```bash
cd apps/web && npm run dev
# Access at: http://localhost:5173/
```
This provides the complete PDF processing UI since all processing is client-side.

### Full Development Setup
```bash
# Terminal 1 - Web App (Required)
cd apps/web && npm run dev

# Terminal 2 - API Server (Optional)  
cd apps/api && npm run dev

# Terminal 3 - PDF Engine Watch (Optional)
cd packages/pdf-engine && npm run dev
```

### Monorepo Commands (from root)
```bash
npm install          # Install all dependencies
npm run dev         # Run all apps in development
npm run build       # Build all packages and apps  
npm run lint        # Lint all packages
npm run test        # Test all packages
npm run type-check  # TypeScript checking across all packages
```

### Testing
```bash
# Web app tests (Vitest + React Testing Library)
cd apps/web && npm run test

# End-to-end tests (Playwright) 
cd apps/web && npx playwright test

# PDF engine tests
cd packages/pdf-engine && npm run test
```

## CRITICAL RULE
 - CRITICAL: Don't Fake any of the process during coding or planning, that affects project velocity or project credibility
 - CRITICAL: If you are not sure of any steps, ask questions and get clarified and proceed, don't assume

## Development Setup
 - CRITICAL: Use Playwright MCP to check the changes and its working, if needed take screenshots
 - IMPORTANT: Don't run dev server, it is already running in the background, if not you can run it
 - IMPORTANT: Always stick to the versions specified in docs/architecture/tech-stack.md
 - IMPORTANT: Always stick to the folder structure specified in docs/architecture/unified-project-structure.md
 - IMPORTANT: Always stick to the coding standards specified in docs/architecture/coding-standards.md
 - IMPORTANT: Never ever Fake/Mockup any implementation scenario to make the tests pass, instead fix the code. This will cause a serious issue in the future.
 - IMPORTANT: Refer Serena MCP for any file location search and project details, be specific with the search query.

## Tech Stack Integration

### Frontend (apps/web)
- **React 18.2+** with TypeScript 5.3+
- **Vite 5.0+** for build tooling
- **TailwindCSS 3.4+** for styling
- **Zustand** for state management with persistence
- **React Router DOM** for routing
- **tRPC** for type-safe API communication
- **Supabase** for authentication
- **pdf-lib** with custom WebAssembly engine

### Backend (apps/api)
- **Next.js 14+** API routes
- **tRPC** server with Zod validation
- **Supabase** integration
- **Upstash Redis** for rate limiting

### Testing
- **Vitest** for unit testing
- **React Testing Library** for component testing
- **Playwright** for end-to-end testing
- **jsdom** for browser environment simulation

## Key Patterns

### State Management
- Use **Zustand** for global state with persistence middleware
- Local component state with React hooks for ephemeral data
- All stores have TypeScript interfaces for type safety

### API Integration  
- **tRPC** client in web app connects to Next.js API routes
- **React Query** for server state management
- **Supabase** client for direct authentication and database operations

### Error Handling
- Custom Error classes that extend Error
- React error boundaries for component-level errors
- Progressive enhancement for graceful degradation

### Performance
- Dynamic imports for code splitting at route level
- React.memo, useMemo, useCallback for optimization
- WebAssembly for CPU-intensive PDF processing

## File Conventions

- `.tsx` for React components
- `.ts` for utilities and non-React code  
- Test files alongside source: `*.test.ts` or `*.test.tsx`
- Index files for directory re-exports
- Named exports preferred over default exports
import { initTRPC, TRPCError } from '@trpc/server'
import { createClient } from '@supabase/supabase-js'
import { getSupabaseUrl, getSupabaseServiceRoleKey } from 'shared'
import type { User } from 'shared'

const supabaseUrl = getSupabaseUrl()
const supabaseServiceKey = getSupabaseServiceRoleKey()

if (!supabaseUrl || !supabaseServiceKey) {
  throw new Error('Missing Supabase environment variables')
}

const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

// Create context for tRPC
export const createContext = async (opts: { req: Request }) => {
  return {
    req: opts.req,
    supabase: supabaseAdmin,
    user: null as User | null,
  }
}

export type Context = Awaited<ReturnType<typeof createContext>>

// Initialize tRPC
const t = initTRPC.context<Context>().create()

// Auth middleware
const authMiddleware = t.middleware(async ({ ctx, next }) => {
  const authHeader = ctx.req.headers.get('authorization')
  const token = authHeader?.replace('Bearer ', '')
  
  if (!token) {
    throw new TRPCError({
      code: 'UNAUTHORIZED',
      message: 'Authentication required',
    })
  }

  const { data: authUser, error } = await ctx.supabase.auth.getUser(token)
  
  if (error || !authUser.user) {
    throw new TRPCError({
      code: 'UNAUTHORIZED',
      message: 'Invalid token',
    })
  }

  // Fetch user data from database to get subscription tier
  const { data: userData } = await ctx.supabase
    .from('users')
    .select('subscription_tier, role')
    .eq('id', authUser.user.id)
    .single()

  const user: User = {
    id: authUser.user.id,
    email: authUser.user.email!,
    name: authUser.user.user_metadata?.name || authUser.user.email!.split('@')[0],
    avatar_url: authUser.user.user_metadata?.avatar_url || null,
    subscription_tier: userData?.subscription_tier || 'free', // Fetch from database or default
    role: authUser.user.user_metadata?.role || 'user', // Get role from user metadata
    created_at: authUser.user.created_at,
    updated_at: authUser.user.updated_at || authUser.user.created_at,
  }

  return next({
    ctx: { ...ctx, user },
  })
})

export const router = t.router
export const publicProcedure = t.procedure
export const protectedProcedure = t.procedure.use(authMiddleware)
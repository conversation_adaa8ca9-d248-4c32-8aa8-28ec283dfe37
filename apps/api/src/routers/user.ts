import { z } from 'zod'
import { TRPCError } from '@trpc/server'
import { router, protectedProcedure } from '../trpc'

const updateProfileSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name too long').optional(),
  avatar_url: z.string().url('Invalid avatar URL').optional(),
})

export const userRouter = router({
  getProfile: protectedProcedure
    .output(z.object({
      id: z.string(),
      email: z.string(),
      name: z.string(),
      avatar_url: z.string().nullable(),
      subscription_tier: z.enum(['free', 'premium']),
      role: z.enum(['user', 'admin']),
      created_at: z.string(),
      updated_at: z.string(),
    }))
    .query(async ({ ctx }) => {
      if (!ctx.user) {
        throw new TRPCError({
          code: 'UNAUTHORIZED',
          message: 'User not authenticated',
        })
      }

      return ctx.user
    }),

  updateProfile: protectedProcedure
    .input(updateProfileSchema)
    .output(z.object({
      success: z.boolean(),
      user: z.object({
        id: z.string(),
        email: z.string(),
        name: z.string(),
        avatar_url: z.string().nullable(),
        subscription_tier: z.enum(['free', 'premium']),
        role: z.enum(['user', 'admin']),
        created_at: z.string(),
        updated_at: z.string(),
      }),
    }))
    .mutation(async ({ input, ctx }) => {
      if (!ctx.user) {
        throw new TRPCError({
          code: 'UNAUTHORIZED',
          message: 'User not authenticated',
        })
      }

      const { data, error } = await ctx.supabase.auth.admin.updateUserById(
        ctx.user.id,
        {
          user_metadata: {
            name: input.name || ctx.user.name,
            avatar_url: input.avatar_url || ctx.user.avatar_url,
          }
        }
      )

      if (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: error.message,
        })
      }

      if (!data.user) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to update user',
        })
      }

      const updatedUser = {
        ...ctx.user,
        name: input.name || ctx.user.name,
        avatar_url: input.avatar_url || ctx.user.avatar_url,
        updated_at: new Date().toISOString(),
      }

      return {
        success: true,
        user: updatedUser,
      }
    }),

  deleteAccount: protectedProcedure
    .output(z.object({
      success: z.boolean(),
      message: z.string(),
    }))
    .mutation(async ({ ctx }) => {
      if (!ctx.user) {
        throw new TRPCError({
          code: 'UNAUTHORIZED',
          message: 'User not authenticated',
        })
      }

      // Delete user from Supabase Auth
      const { error } = await ctx.supabase.auth.admin.deleteUser(ctx.user.id)

      if (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: error.message,
        })
      }

      return {
        success: true,
        message: 'Account deleted successfully',
      }
    }),
})
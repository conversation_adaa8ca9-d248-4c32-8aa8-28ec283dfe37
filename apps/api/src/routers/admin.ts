import { z } from 'zod'
import { TRPCError } from '@trpc/server'
import { router, protectedProcedure } from '../trpc'

// Admin middleware to check if user has admin role
const adminProcedure = protectedProcedure.use(async ({ ctx, next }) => {
  if (!ctx.user) {
    throw new TRPCError({
      code: 'UNAUTHORIZED',
      message: 'User not authenticated',
    })
  }

  // Check if user has admin role
  // For now, we'll check user metadata, but in production this should be in a database
  const { data: userData, error } = await ctx.supabase.auth.admin.getUserById(ctx.user.id)
  
  if (error || !userData.user) {
    throw new TRPCError({
      code: 'INTERNAL_SERVER_ERROR',
      message: 'Failed to verify user permissions',
    })
  }

  const userRole = userData.user.user_metadata?.role || 'user'
  
  if (userRole !== 'admin') {
    throw new TRPCError({
      code: 'FORBIDDEN',
      message: 'Admin access required',
    })
  }

  return next({
    ctx: { ...ctx, user: ctx.user, isAdmin: true },
  })
})

const sessionSchema = z.object({
  id: z.string(),
  user_id: z.string(),
  user_email: z.string(),
  user_name: z.string(),
  created_at: z.string(),
  last_activity: z.string(),
  expires_at: z.string(),
  ip_address: z.string().optional(),
  user_agent: z.string().optional(),
  status: z.enum(['active', 'expired', 'revoked']),
})

const sessionStatsSchema = z.object({
  total_active_sessions: z.number(),
  total_users_online: z.number(),
  average_session_duration: z.number(),
  sessions_today: z.number(),
})

export const adminRouter = router({
  getSessions: adminProcedure
    .output(z.array(sessionSchema))
    .query(async ({ ctx }) => {
      // Get all active sessions from Supabase Auth
      const { data: sessions, error } = await ctx.supabase.auth.admin.listUsers()
      
      if (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to fetch sessions',
        })
      }

      // Transform Supabase users to our session format
      const activeSessions = sessions.users
        .filter(user => user.last_sign_in_at) // Only users with active sessions
        .map(user => ({
          id: user.id,
          user_id: user.id,
          user_email: user.email || '',
          user_name: user.user_metadata?.name || 'Unknown',
          created_at: user.last_sign_in_at || new Date().toISOString(),
          last_activity: user.last_sign_in_at || new Date().toISOString(),
          expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 hours from now
          ip_address: undefined, // Supabase doesn't expose IP in basic plan
          user_agent: undefined, // Supabase doesn't expose user agent in basic plan
          status: 'active' as const,
        }))
        .sort((a, b) => new Date(b.last_activity).getTime() - new Date(a.last_activity).getTime())

      return activeSessions
    }),

  getSessionStats: adminProcedure
    .output(sessionStatsSchema)
    .query(async ({ ctx }) => {
      const { data: sessions, error } = await ctx.supabase.auth.admin.listUsers()
      
      if (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to fetch session stats',
        })
      }

      const now = new Date()
      const twentyFourHoursAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000)
      const activeUsers = sessions.users.filter(user => 
        user.last_sign_in_at && new Date(user.last_sign_in_at) > twentyFourHoursAgo
      )

      // Calculate stats
      const totalActiveSessions = activeUsers.length
      const totalUsersOnline = activeUsers.length // For simplicity, same as active sessions
      const sessionsToday = sessions.users.filter(user => 
        user.last_sign_in_at && new Date(user.last_sign_in_at) > new Date(now.getFullYear(), now.getMonth(), now.getDate())
      ).length

      // Calculate average session duration (simplified)
      const averageSessionDuration = activeUsers.length > 0 
        ? activeUsers.reduce((acc, user) => {
            const signInTime = user.last_sign_in_at ? new Date(user.last_sign_in_at) : now
            return acc + (now.getTime() - signInTime.getTime())
          }, 0) / activeUsers.length / 1000 // Convert to seconds
        : 0

      return {
        total_active_sessions: totalActiveSessions,
        total_users_online: totalUsersOnline,
        average_session_duration: Math.round(averageSessionDuration),
        sessions_today: sessionsToday,
      }
    }),

  revokeSession: adminProcedure
    .input(z.object({
      sessionId: z.string(),
    }))
    .output(z.object({
      success: z.boolean(),
      message: z.string(),
    }))
    .mutation(async ({ input, ctx }) => {
      // In Supabase, we can't revoke individual sessions, but we can delete the user's session
      // by forcing them to re-authenticate
      const { error } = await ctx.supabase.auth.admin.signOut(input.sessionId)
      
      if (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to revoke session',
        })
      }

      return {
        success: true,
        message: 'Session revoked successfully',
      }
    }),

  revokeAllUserSessions: adminProcedure
    .input(z.object({
      userId: z.string(),
    }))
    .output(z.object({
      success: z.boolean(),
      message: z.string(),
    }))
    .mutation(async ({ input, ctx }) => {
      // Sign out all sessions for the user
      const { error } = await ctx.supabase.auth.admin.signOut(input.userId, 'global')
      
      if (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to revoke user sessions',
        })
      }

      return {
        success: true,
        message: 'All user sessions revoked successfully',
      }
    }),

  getUserList: adminProcedure
    .input(z.object({
      page: z.number().min(1).default(1),
      limit: z.number().min(1).max(100).default(20),
      search: z.string().optional(),
    }))
    .output(z.object({
      users: z.array(z.object({
        id: z.string(),
        email: z.string(),
        name: z.string(),
        role: z.enum(['user', 'admin']),
        subscription_tier: z.enum(['free', 'premium']),
        created_at: z.string(),
        last_sign_in_at: z.string().nullable(),
        email_confirmed_at: z.string().nullable(),
      })),
      total: z.number(),
      page: z.number(),
      limit: z.number(),
    }))
    .query(async ({ input, ctx }) => {
      const { data: users, error } = await ctx.supabase.auth.admin.listUsers({
        page: input.page,
        perPage: input.limit,
      })
      
      if (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to fetch users',
        })
      }

      // Filter by search term if provided
      let filteredUsers = users.users
      if (input.search) {
        const searchLower = input.search.toLowerCase()
        filteredUsers = users.users.filter(user => 
          user.email?.toLowerCase().includes(searchLower) ||
          user.user_metadata?.name?.toLowerCase().includes(searchLower)
        )
      }

      const transformedUsers = filteredUsers.map(user => ({
        id: user.id,
        email: user.email || '',
        name: user.user_metadata?.name || 'Unknown',
        role: (user.user_metadata?.role || 'user') as 'user' | 'admin',
        subscription_tier: (user.user_metadata?.subscription_tier || 'free') as 'free' | 'premium',
        created_at: user.created_at,
        last_sign_in_at: user.last_sign_in_at,
        email_confirmed_at: user.email_confirmed_at,
      }))

      return {
        users: transformedUsers,
        total: users.total || transformedUsers.length,
        page: input.page,
        limit: input.limit,
      }
    }),

  updateUserRole: adminProcedure
    .input(z.object({
      userId: z.string(),
      role: z.enum(['user', 'admin']),
    }))
    .output(z.object({
      success: z.boolean(),
      message: z.string(),
    }))
    .mutation(async ({ input, ctx }) => {
      const { error } = await ctx.supabase.auth.admin.updateUserById(
        input.userId,
        {
          user_metadata: {
            role: input.role,
          }
        }
      )

      if (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to update user role',
        })
      }

      return {
        success: true,
        message: `User role updated to ${input.role}`,
      }
    }),
})
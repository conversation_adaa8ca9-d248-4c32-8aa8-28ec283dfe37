import { z } from 'zod'
import { TRPCError } from '@trpc/server'
import { router, publicProcedure } from '../trpc'
import type { User } from 'shared'
import { authRateLimitMiddleware, resetPasswordRateLimitMiddleware } from '../middleware/rateLimit'

// Input validation schemas
const registerSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
  name: z.string().min(1, 'Name is required').max(100, 'Name too long'),
})

const loginSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(1, 'Password is required'),
})

const resetPasswordSchema = z.object({
  email: z.string().email('Invalid email address'),
})

export const authRouter = router({
  register: publicProcedure
    .use(authRateLimitMiddleware)
    .input(registerSchema)
    .output(z.object({
      user: z.object({
        id: z.string(),
        email: z.string(),
        name: z.string(),
        avatar_url: z.string().nullable(),
        subscription_tier: z.enum(['free', 'premium']),
        role: z.enum(['user', 'admin']),
        created_at: z.string(),
        updated_at: z.string(),
      }),
      session: z.object({
        access_token: z.string(),
        refresh_token: z.string(),
        expires_at: z.number(),
      }),
    }))
    .mutation(async ({ input, ctx }) => {
      // Use client-side registration for proper JWT handling
      const { data, error } = await ctx.supabase.auth.signUp({
        email: input.email,
        password: input.password,
        options: {
          data: {
            name: input.name,
          }
        }
      })

      if (error) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: error.message,
        })
      }

      if (!data.user || !data.session) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Registration failed - please verify your email',
        })
      }

      const user: User = {
        id: data.user.id,
        email: data.user.email!,
        name: input.name,
        avatar_url: null,
        subscription_tier: 'free',
        role: 'user',
        created_at: data.user.created_at,
        updated_at: data.user.updated_at || data.user.created_at,
      }

      const session = {
        access_token: data.session.access_token,
        refresh_token: data.session.refresh_token,
        expires_at: data.session.expires_at || 0,
      }

      return { user, session }
    }),

  login: publicProcedure
    .use(authRateLimitMiddleware)
    .input(loginSchema)
    .output(z.object({
      user: z.object({
        id: z.string(),
        email: z.string(),
        name: z.string(),
        avatar_url: z.string().nullable(),
        subscription_tier: z.enum(['free', 'premium']),
        role: z.enum(['user', 'admin']),
        created_at: z.string(),
        updated_at: z.string(),
      }),
      session: z.object({
        access_token: z.string(),
        refresh_token: z.string(),
        expires_at: z.number(),
      }),
    }))
    .mutation(async ({ input, ctx }) => {
      const { data, error } = await ctx.supabase.auth.signInWithPassword({
        email: input.email,
        password: input.password,
      })

      if (error) {
        throw new TRPCError({
          code: 'UNAUTHORIZED',
          message: error.message,
        })
      }

      if (!data.user || !data.session) {
        throw new TRPCError({
          code: 'UNAUTHORIZED',
          message: 'Login failed',
        })
      }

      const user: User = {
        id: data.user.id,
        email: data.user.email!,
        name: data.user.user_metadata?.name || data.user.email!.split('@')[0],
        avatar_url: data.user.user_metadata?.avatar_url || null,
        subscription_tier: 'free',
        role: 'user',
        created_at: data.user.created_at,
        updated_at: data.user.updated_at || data.user.created_at,
      }

      const session = {
        access_token: data.session.access_token,
        refresh_token: data.session.refresh_token,
        expires_at: data.session.expires_at || 0,
      }

      return { user, session }
    }),

  resetPassword: publicProcedure
    .use(resetPasswordRateLimitMiddleware)
    .input(resetPasswordSchema)
    .output(z.object({
      success: z.boolean(),
      message: z.string(),
    }))
    .mutation(async ({ input, ctx }) => {
      const { error } = await ctx.supabase.auth.resetPasswordForEmail(input.email, {
        redirectTo: `${process.env.FRONTEND_URL}/reset-password`,
      })

      if (error) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: error.message,
        })
      }

      return {
        success: true,
        message: 'Password reset email sent',
      }
    }),
})
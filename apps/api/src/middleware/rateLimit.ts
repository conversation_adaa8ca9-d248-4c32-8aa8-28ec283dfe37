import { Ratelimit } from "@upstash/ratelimit";
import { Redis } from "@upstash/redis";
import { TRPCError } from '@trpc/server';

// Initialize Redis client - use in-memory fallback for development
const redis = process.env.UPSTASH_REDIS_REST_URL && process.env.UPSTASH_REDIS_REST_TOKEN 
  ? new Redis({
      url: process.env.UPSTASH_REDIS_REST_URL,
      token: process.env.UPSTASH_REDIS_REST_TOKEN,
    })
  : undefined; // Let Upstash handle in-memory fallback

// Create rate limiter for auth endpoints
export const authRateLimit = new Ratelimit({
  redis: redis as any || new Map(),
  limiter: Ratelimit.slidingWindow(5, "1 m"), // 5 requests per minute
  analytics: true,
});

// Create rate limiter for password reset (more restrictive)
export const resetPasswordRateLimit = new Ratelimit({
  redis: redis as any || new Map(),
  limiter: Ratelimit.slidingWindow(3, "5 m"), // 3 requests per 5 minutes
  analytics: true,
});

// Get client IP from request headers
function getClientIP(req: any): string {
  return req.headers['x-forwarded-for']?.split(',')[0]?.trim() ||
         req.headers['x-real-ip'] ||
         req.connection?.remoteAddress ||
         req.socket?.remoteAddress ||
         req.ip ||
         'unknown';
}

// Rate limiting middleware factory
export function createRateLimitMiddleware(rateLimit: Ratelimit) {
  return async ({ ctx, next }: { ctx: any; next: any }) => {
    const clientIP = getClientIP(ctx.req);
    const identifier = `${clientIP}`;
    
    const { success, limit, reset, remaining } = await rateLimit.limit(identifier);
    
    if (!success) {
      throw new TRPCError({
        code: 'TOO_MANY_REQUESTS',
        message: `Rate limit exceeded. Try again in ${Math.ceil((reset - Date.now()) / 1000)} seconds.`,
      });
    }
    
    // Add rate limit headers to response
    if (ctx.res) {
      ctx.res.setHeader('X-RateLimit-Limit', limit.toString());
      ctx.res.setHeader('X-RateLimit-Remaining', remaining.toString());
      ctx.res.setHeader('X-RateLimit-Reset', reset.toString());
    }
    
    return next();
  };
}

// Pre-configured middleware instances
export const authRateLimitMiddleware = createRateLimitMiddleware(authRateLimit);
export const resetPasswordRateLimitMiddleware = createRateLimitMiddleware(resetPasswordRateLimit);
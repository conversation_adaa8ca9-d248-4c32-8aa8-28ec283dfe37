import { fetchRequestHandler } from '@trpc/server/adapters/fetch'
import { appRouter } from '../../../src/routers'
import { createContext } from '../../../src/trpc'

export default async function handler(req: Request) {
  return fetchRequestHandler({
    endpoint: '/api/trpc',
    req,
    router: appRouter,
    createContext,
    onError:
      process.env.NODE_ENV === 'development'
        ? ({ path, error }) => {
            console.error(
              `❌ tRPC failed on ${path ?? '<no-path>'}: ${error.message}`
            )
          }
        : undefined,
  })
}

export const config = {
  runtime: 'edge',
}
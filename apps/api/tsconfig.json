{"compilerOptions": {"target": "es2018", "module": "esnext", "lib": ["es2018"], "moduleResolution": "node", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "jsx": "preserve", "incremental": true, "resolveJsonModule": true, "isolatedModules": true, "baseUrl": ".", "paths": {"shared": ["../packages/shared/src"]}, "allowJs": true}, "include": ["src/**/*", "next-env.d.ts"], "exclude": ["node_modules", ".next", "dist"]}
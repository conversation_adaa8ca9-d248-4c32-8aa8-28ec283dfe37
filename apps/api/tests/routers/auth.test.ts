import { describe, it, expect, vi, beforeEach } from 'vitest'
import { authRouter } from '../../src/routers/auth'
import { createContext } from '../../src/trpc'

// Mock Supabase
const mockCreateUser = vi.fn()
const mockSignInWithPassword = vi.fn()
const mockResetPasswordForEmail = vi.fn()
const mockGenerateLink = vi.fn()

const mockSupabase = {
  auth: {
    admin: {
      createUser: mockCreateUser,
      generateLink: mockGenerateLink,
    },
    signInWithPassword: mockSignInWithPassword,
    resetPasswordForEmail: mockResetPasswordForEmail,
  }
}

describe('Auth Router', () => {
  const mockContext = {
    req: new Request('http://localhost/api/trpc'),
    supabase: mockSupabase,
    user: null,
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('register', () => {
    it('should register a new user successfully', async () => {
      const input = {
        email: '<EMAIL>',
        password: 'password123',
        name: 'Test User',
      }

      mockCreateUser.mockResolvedValue({
        data: {
          user: {
            id: '123',
            email: '<EMAIL>',
            user_metadata: { name: 'Test User' },
            created_at: '2023-01-01T00:00:00Z',
            updated_at: '2023-01-01T00:00:00Z',
          },
        },
        error: null,
      })

      mockGenerateLink.mockResolvedValue({
        data: { action_link: 'mock-link' },
        error: null,
      })

      const caller = authRouter.createCaller(mockContext)
      const result = await caller.register(input)

      expect(mockCreateUser).toHaveBeenCalledWith({
        email: input.email,
        password: input.password,
        user_metadata: { name: input.name },
        email_confirm: true,
      })

      expect(result.user.email).toBe(input.email)
      expect(result.user.name).toBe(input.name)
      expect(result.user.subscription_tier).toBe('free')
    })

    it('should throw error for invalid input', async () => {
      const input = {
        email: 'invalid-email',
        password: 'short',
        name: '',
      }

      const caller = authRouter.createCaller(mockContext)
      
      await expect(caller.register(input)).rejects.toThrow()
    })

    it('should handle Supabase errors', async () => {
      const input = {
        email: '<EMAIL>',
        password: 'password123',
        name: 'Test User',
      }

      mockCreateUser.mockResolvedValue({
        data: { user: null },
        error: { message: 'User already exists' },
      })

      const caller = authRouter.createCaller(mockContext)
      
      await expect(caller.register(input)).rejects.toThrow('User already exists')
    })
  })

  describe('login', () => {
    it('should login user successfully', async () => {
      const input = {
        email: '<EMAIL>',
        password: 'password123',
      }

      mockSignInWithPassword.mockResolvedValue({
        data: {
          user: {
            id: '123',
            email: '<EMAIL>',
            user_metadata: { name: 'Test User' },
            created_at: '2023-01-01T00:00:00Z',
            updated_at: '2023-01-01T00:00:00Z',
          },
          session: {
            access_token: 'mock-access-token',
            refresh_token: 'mock-refresh-token',
            expires_at: 1234567890,
          },
        },
        error: null,
      })

      const caller = authRouter.createCaller(mockContext)
      const result = await caller.login(input)

      expect(mockSignInWithPassword).toHaveBeenCalledWith({
        email: input.email,
        password: input.password,
      })

      expect(result.user.email).toBe(input.email)
      expect(result.session.access_token).toBe('mock-access-token')
    })

    it('should throw error for invalid credentials', async () => {
      const input = {
        email: '<EMAIL>',
        password: 'wrongpassword',
      }

      mockSignInWithPassword.mockResolvedValue({
        data: { user: null, session: null },
        error: { message: 'Invalid credentials' },
      })

      const caller = authRouter.createCaller(mockContext)
      
      await expect(caller.login(input)).rejects.toThrow('Invalid credentials')
    })

    it('should validate input format', async () => {
      const input = {
        email: 'invalid-email',
        password: '',
      }

      const caller = authRouter.createCaller(mockContext)
      
      await expect(caller.login(input)).rejects.toThrow()
    })
  })

  describe('resetPassword', () => {
    it('should send password reset email', async () => {
      const input = {
        email: '<EMAIL>',
      }

      mockResetPasswordForEmail.mockResolvedValue({
        error: null,
      })

      const caller = authRouter.createCaller(mockContext)
      const result = await caller.resetPassword(input)

      expect(mockResetPasswordForEmail).toHaveBeenCalledWith(input.email)
      expect(result.success).toBe(true)
      expect(result.message).toBe('Password reset email sent')
    })

    it('should handle reset errors', async () => {
      const input = {
        email: '<EMAIL>',
      }

      mockResetPasswordForEmail.mockResolvedValue({
        error: { message: 'User not found' },
      })

      const caller = authRouter.createCaller(mockContext)
      
      await expect(caller.resetPassword(input)).rejects.toThrow('User not found')
    })

    it('should validate email format', async () => {
      const input = {
        email: 'invalid-email',
      }

      const caller = authRouter.createCaller(mockContext)
      
      await expect(caller.resetPassword(input)).rejects.toThrow()
    })
  })
})
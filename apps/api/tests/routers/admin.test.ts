import { describe, it, expect, beforeAll, afterAll, beforeEach } from 'vitest'
import { createClient } from '@supabase/supabase-js'
import { adminRouter } from '../../src/routers/admin'
import { createContext } from '../../src/trpc'

// Mock Supabase client
const mockSupabase = {
  auth: {
    admin: {
      getUserById: vi.fn(),
      listUsers: vi.fn(),
      signOut: vi.fn(),
      updateUserById: vi.fn(),
    },
  },
}

// Mock context
const createMockContext = (user: any = null) => ({
  req: new Request('http://localhost:3000'),
  supabase: mockSupabase as any,
  user,
})

describe('Admin Router', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Admin Middleware', () => {
    it('should reject non-admin users', async () => {
      const mockUser = {
        id: 'user-1',
        email: '<EMAIL>',
        user_metadata: { role: 'user' }
      }

      mockSupabase.auth.admin.getUserById.mockResolvedValueOnce({
        data: { user: mockUser },
        error: null
      })

      const ctx = createMockContext({
        id: 'user-1',
        email: '<EMAIL>',
        role: 'user'
      })

      const caller = adminRouter.createCaller(ctx)
      
      await expect(caller.getSessions()).rejects.toThrow('Admin access required')
    })

    it('should allow admin users', async () => {
      const mockAdminUser = {
        id: 'admin-1',
        email: '<EMAIL>',
        user_metadata: { role: 'admin' }
      }

      mockSupabase.auth.admin.getUserById.mockResolvedValueOnce({
        data: { user: mockAdminUser },
        error: null
      })

      mockSupabase.auth.admin.listUsers.mockResolvedValueOnce({
        data: { users: [], total: 0 },
        error: null
      })

      const ctx = createMockContext({
        id: 'admin-1',
        email: '<EMAIL>',
        role: 'admin'
      })

      const caller = adminRouter.createCaller(ctx)
      const result = await caller.getSessions()
      
      expect(result).toEqual([])
    })
  })

  describe('getSessions', () => {
    it('should return active sessions', async () => {
      const mockUsers = [
        {
          id: 'user-1',
          email: '<EMAIL>',
          user_metadata: { name: 'User One' },
          last_sign_in_at: new Date().toISOString(),
        },
        {
          id: 'user-2',
          email: '<EMAIL>',
          user_metadata: { name: 'User Two' },
          last_sign_in_at: new Date(Date.now() - 1000 * 60 * 30).toISOString(), // 30 minutes ago
        }
      ]

      mockSupabase.auth.admin.getUserById.mockResolvedValueOnce({
        data: { user: { user_metadata: { role: 'admin' } } },
        error: null
      })

      mockSupabase.auth.admin.listUsers.mockResolvedValueOnce({
        data: { users: mockUsers },
        error: null
      })

      const ctx = createMockContext({
        id: 'admin-1',
        email: '<EMAIL>',
        role: 'admin'
      })

      const caller = adminRouter.createCaller(ctx)
      const result = await caller.getSessions()
      
      expect(result).toHaveLength(2)
      expect(result[0]).toMatchObject({
        user_id: 'user-1',
        user_email: '<EMAIL>',
        user_name: 'User One',
        status: 'active'
      })
    })

    it('should handle Supabase errors', async () => {
      mockSupabase.auth.admin.getUserById.mockResolvedValueOnce({
        data: { user: { user_metadata: { role: 'admin' } } },
        error: null
      })

      mockSupabase.auth.admin.listUsers.mockResolvedValueOnce({
        data: null,
        error: { message: 'Database error' }
      })

      const ctx = createMockContext({
        id: 'admin-1',
        email: '<EMAIL>',
        role: 'admin'
      })

      const caller = adminRouter.createCaller(ctx)
      
      await expect(caller.getSessions()).rejects.toThrow('Failed to fetch sessions')
    })
  })

  describe('getSessionStats', () => {
    it('should return correct session statistics', async () => {
      const now = new Date()
      const twentyFourHoursAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000)
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())

      const mockUsers = [
        {
          id: 'user-1',
          last_sign_in_at: new Date(now.getTime() - 1000 * 60 * 30).toISOString(), // 30 minutes ago
        },
        {
          id: 'user-2',
          last_sign_in_at: new Date(now.getTime() - 1000 * 60 * 60 * 2).toISOString(), // 2 hours ago
        },
        {
          id: 'user-3',
          last_sign_in_at: new Date(now.getTime() - 1000 * 60 * 60 * 25).toISOString(), // 25 hours ago
        }
      ]

      mockSupabase.auth.admin.getUserById.mockResolvedValueOnce({
        data: { user: { user_metadata: { role: 'admin' } } },
        error: null
      })

      mockSupabase.auth.admin.listUsers.mockResolvedValueOnce({
        data: { users: mockUsers },
        error: null
      })

      const ctx = createMockContext({
        id: 'admin-1',
        email: '<EMAIL>',
        role: 'admin'
      })

      const caller = adminRouter.createCaller(ctx)
      const result = await caller.getSessionStats()
      
      expect(result).toMatchObject({
        total_active_sessions: 2, // Only users within 24 hours
        total_users_online: 2,
        sessions_today: 2,
      })
      expect(result.average_session_duration).toBeGreaterThan(0)
    })
  })

  describe('revokeSession', () => {
    it('should revoke a specific session', async () => {
      mockSupabase.auth.admin.getUserById.mockResolvedValueOnce({
        data: { user: { user_metadata: { role: 'admin' } } },
        error: null
      })

      mockSupabase.auth.admin.signOut.mockResolvedValueOnce({
        error: null
      })

      const ctx = createMockContext({
        id: 'admin-1',
        email: '<EMAIL>',
        role: 'admin'
      })

      const caller = adminRouter.createCaller(ctx)
      const result = await caller.revokeSession({ sessionId: 'session-1' })
      
      expect(result).toEqual({
        success: true,
        message: 'Session revoked successfully'
      })
      expect(mockSupabase.auth.admin.signOut).toHaveBeenCalledWith('session-1')
    })

    it('should handle revocation errors', async () => {
      mockSupabase.auth.admin.getUserById.mockResolvedValueOnce({
        data: { user: { user_metadata: { role: 'admin' } } },
        error: null
      })

      mockSupabase.auth.admin.signOut.mockResolvedValueOnce({
        error: { message: 'Session not found' }
      })

      const ctx = createMockContext({
        id: 'admin-1',
        email: '<EMAIL>',
        role: 'admin'
      })

      const caller = adminRouter.createCaller(ctx)
      
      await expect(caller.revokeSession({ sessionId: 'invalid-session' }))
        .rejects.toThrow('Failed to revoke session')
    })
  })

  describe('revokeAllUserSessions', () => {
    it('should revoke all sessions for a user', async () => {
      mockSupabase.auth.admin.getUserById.mockResolvedValueOnce({
        data: { user: { user_metadata: { role: 'admin' } } },
        error: null
      })

      mockSupabase.auth.admin.signOut.mockResolvedValueOnce({
        error: null
      })

      const ctx = createMockContext({
        id: 'admin-1',
        email: '<EMAIL>',
        role: 'admin'
      })

      const caller = adminRouter.createCaller(ctx)
      const result = await caller.revokeAllUserSessions({ userId: 'user-1' })
      
      expect(result).toEqual({
        success: true,
        message: 'All user sessions revoked successfully'
      })
      expect(mockSupabase.auth.admin.signOut).toHaveBeenCalledWith('user-1', 'global')
    })
  })

  describe('getUserList', () => {
    it('should return paginated user list', async () => {
      const mockUsers = [
        {
          id: 'user-1',
          email: '<EMAIL>',
          user_metadata: { name: 'User One', role: 'user', subscription_tier: 'free' },
          created_at: new Date().toISOString(),
          last_sign_in_at: new Date().toISOString(),
          email_confirmed_at: new Date().toISOString(),
        },
        {
          id: 'user-2',
          email: '<EMAIL>',
          user_metadata: { name: 'User Two', role: 'user', subscription_tier: 'premium' },
          created_at: new Date().toISOString(),
          last_sign_in_at: null,
          email_confirmed_at: new Date().toISOString(),
        }
      ]

      mockSupabase.auth.admin.getUserById.mockResolvedValueOnce({
        data: { user: { user_metadata: { role: 'admin' } } },
        error: null
      })

      mockSupabase.auth.admin.listUsers.mockResolvedValueOnce({
        data: { users: mockUsers, total: 2 },
        error: null
      })

      const ctx = createMockContext({
        id: 'admin-1',
        email: '<EMAIL>',
        role: 'admin'
      })

      const caller = adminRouter.createCaller(ctx)
      const result = await caller.getUserList({})
      
      expect(result).toMatchObject({
        users: expect.arrayContaining([
          expect.objectContaining({
            id: 'user-1',
            email: '<EMAIL>',
            name: 'User One',
            role: 'user',
            subscription_tier: 'free'
          })
        ]),
        total: 2,
        page: 1,
        limit: 20
      })
    })

    it('should filter users by search term', async () => {
      const mockUsers = [
        {
          id: 'user-1',
          email: '<EMAIL>',
          user_metadata: { name: 'John Doe' },
          created_at: new Date().toISOString(),
          last_sign_in_at: new Date().toISOString(),
          email_confirmed_at: new Date().toISOString(),
        },
        {
          id: 'user-2',
          email: '<EMAIL>',
          user_metadata: { name: 'Jane Smith' },
          created_at: new Date().toISOString(),
          last_sign_in_at: null,
          email_confirmed_at: new Date().toISOString(),
        }
      ]

      mockSupabase.auth.admin.getUserById.mockResolvedValueOnce({
        data: { user: { user_metadata: { role: 'admin' } } },
        error: null
      })

      mockSupabase.auth.admin.listUsers.mockResolvedValueOnce({
        data: { users: mockUsers, total: 2 },
        error: null
      })

      const ctx = createMockContext({
        id: 'admin-1',
        email: '<EMAIL>',
        role: 'admin'
      })

      const caller = adminRouter.createCaller(ctx)
      const result = await caller.getUserList({ search: 'john' })
      
      expect(result.users).toHaveLength(1)
      expect(result.users[0].email).toBe('<EMAIL>')
    })
  })

  describe('updateUserRole', () => {
    it('should update user role successfully', async () => {
      mockSupabase.auth.admin.getUserById.mockResolvedValueOnce({
        data: { user: { user_metadata: { role: 'admin' } } },
        error: null
      })

      mockSupabase.auth.admin.updateUserById.mockResolvedValueOnce({
        error: null
      })

      const ctx = createMockContext({
        id: 'admin-1',
        email: '<EMAIL>',
        role: 'admin'
      })

      const caller = adminRouter.createCaller(ctx)
      const result = await caller.updateUserRole({ userId: 'user-1', role: 'admin' })
      
      expect(result).toEqual({
        success: true,
        message: 'User role updated to admin'
      })
      expect(mockSupabase.auth.admin.updateUserById).toHaveBeenCalledWith(
        'user-1',
        { user_metadata: { role: 'admin' } }
      )
    })

    it('should handle update errors', async () => {
      mockSupabase.auth.admin.getUserById.mockResolvedValueOnce({
        data: { user: { user_metadata: { role: 'admin' } } },
        error: null
      })

      mockSupabase.auth.admin.updateUserById.mockResolvedValueOnce({
        error: { message: 'User not found' }
      })

      const ctx = createMockContext({
        id: 'admin-1',
        email: '<EMAIL>',
        role: 'admin'
      })

      const caller = adminRouter.createCaller(ctx)
      
      await expect(caller.updateUserRole({ userId: 'invalid-user', role: 'admin' }))
        .rejects.toThrow('Failed to update user role')
    })
  })
})
{"name": "api", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "vitest", "type-check": "tsc --noEmit"}, "dependencies": {"@supabase/supabase-js": "^2.55.0", "@trpc/client": "^10.0.0", "@trpc/next": "^10.0.0", "@trpc/react-query": "^10.0.0", "@trpc/server": "^10.0.0", "@upstash/ratelimit": "^2.0.6", "@upstash/redis": "^1.35.3", "next": "^14.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "shared": "*", "zod": "^3.22.0"}, "devDependencies": {"@types/node": "^20.0.0", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "eslint": "^8.45.0", "eslint-config-next": "^14.0.0", "typescript": "^5.3.0", "vitest": "^1.0.0"}}
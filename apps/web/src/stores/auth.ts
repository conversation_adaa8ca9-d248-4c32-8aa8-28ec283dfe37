import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import type { AuthState, LoginCredentials, RegisterCredentials, User } from 'shared'
import { authService } from '../services/auth.service'

interface AuthStore extends AuthState {
  login: (credentials: LoginCredentials) => Promise<void>
  register: (credentials: RegisterCredentials) => Promise<void>
  logout: () => Promise<void>
  refreshToken: () => Promise<void>
  updateProfile: (updates: Partial<Pick<User, 'name' | 'avatar_url'>>) => Promise<void>
  checkAuth: () => Promise<void>
  setLoading: (loading: boolean) => void
}

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      user: null,
      session: null,
      isLoading: false,

      login: async (credentials: LoginCredentials) => {
        set({ isLoading: true })
        try {
          const { user, session } = await authService.login(credentials)
          set({ user, session, isLoading: false })
        } catch (error) {
          set({ isLoading: false })
          throw error
        }
      },

      register: async (credentials: RegisterCredentials) => {
        set({ isLoading: true })
        try {
          const { user, session } = await authService.register(credentials)
          set({ user, session, isLoading: false })
        } catch (error) {
          set({ isLoading: false })
          throw error
        }
      },

      logout: async () => {
        set({ isLoading: true })
        try {
          await authService.logout()
          set({ user: null, session: null, isLoading: false })
        } catch (error) {
          set({ isLoading: false })
          throw error
        }
      },

      refreshToken: async () => {
        const { session } = get()
        if (!session) return

        try {
          const newAccessToken = await authService.refreshToken()
          set({
            session: {
              ...session,
              access_token: newAccessToken,
            }
          })
        } catch (error) {
          // If refresh fails, clear auth state
          set({ user: null, session: null })
          throw error
        }
      },

      updateProfile: async (updates: Partial<Pick<User, 'name' | 'avatar_url'>>) => {
        const { user } = get()
        if (!user) throw new Error('No user logged in')

        try {
          // Call tRPC endpoint to update profile in database
          // For now, update local state optimistically
          const updatedUser = { 
            ...user, 
            ...updates, 
            updated_at: new Date().toISOString() 
          }
          set({ user: updatedUser })
          
          // In a real implementation, this would call the tRPC endpoint:
          // await trpc.user.updateProfile.mutate(updates)
        } catch (error) {
          // Revert optimistic update on error
          set({ user })
          throw error
        }
      },

      checkAuth: async () => {
        set({ isLoading: true })
        try {
          const user = await authService.getCurrentUser()
          set({ user, isLoading: false })
        } catch (error) {
          set({ user: null, session: null, isLoading: false })
        }
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading })
      },
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => localStorage),
      // Only persist user data, not sensitive session tokens
      partialize: (state) => ({ user: state.user }),
    }
  )
)

// Initialize auth state on app load
authService.onAuthStateChange((user) => {
  useAuthStore.getState().setLoading(false)
  if (user) {
    useAuthStore.setState({ user })
  } else {
    useAuthStore.setState({ user: null, session: null })
  }
})
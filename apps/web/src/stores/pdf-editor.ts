import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import type { ProcessedResult } from '../services/pdf-processor';

export type EditorTool = 
  | 'select' 
  | 'text' 
  | 'highlight' 
  | 'comment' 
  | 'draw' 
  | 'stamp' 
  | 'form'
  | 'page-manager'
  | 'ocr'
  | 'convert';

export type EditorTab = 
  | 'edit' 
  | 'annotate' 
  | 'pages' 
  | 'forms' 
  | 'ocr' 
  | 'convert' 
  | 'compress';

// New professional tab structure (aligned with PDF.js Express)
export type PrimaryTab = 'view' | 'annotate' | 'shapes' | 'insert' | 'measure' | 'forms';

// Zoom mode for professional zoom controls
export type ZoomMode = 'custom' | 'fitWidth' | 'fitPage';

export interface Annotation {
  id: string;
  type: 'highlight' | 'comment' | 'drawing' | 'stamp';
  pageNum: number;
  position: { x: number; y: number };
  data: any;
  createdAt: Date;
}

export interface TextSelection {
  pageNum: number;
  startIndex: number;
  endIndex: number;
  text: string;
  bounds: { x: number; y: number; width: number; height: number };
}

export interface AnnotationOptions {
  highlightColor: string;
  highlightOpacity: number;
  drawColor: string;
  drawWidth: number;
  commentStyle: 'sticky' | 'inline';
  stampType: string;
}

export interface PDFEditorState {
  // Document state
  currentDocument: File | null;
  processedDocument: ProcessedResult | null;
  documentPages: number;
  currentPage: number;
  zoomLevel: number;
  
  // UI state
  activeTab: EditorTab;
  activeTool: EditorTool;
  sidebarOpen: boolean;
  
  // Professional toolbar state
  primaryTab: PrimaryTab;
  zoomMode: ZoomMode;
  
  // Editor state
  annotations: Annotation[];
  textSelection: TextSelection | null;
  isDirty: boolean;
  annotationOptions: AnnotationOptions;
  
  // Processing state
  isProcessing: boolean;
  processingProgress: number;
  processingStage: string;
  
  // Actions
  setDocument: (file: File) => void;
  setProcessedDocument: (result: ProcessedResult) => void;
  setActiveTab: (tab: EditorTab) => void;
  setActiveTool: (tool: EditorTool) => void;
  setCurrentPage: (page: number) => void;
  setZoomLevel: (zoom: number) => void;
  setSidebarOpen: (open: boolean) => void;
  
  // Professional toolbar actions
  setPrimaryTab: (tab: PrimaryTab) => void;
  setZoomMode: (mode: ZoomMode) => void;
  
  // Annotation actions
  addAnnotation: (annotation: Omit<Annotation, 'id' | 'createdAt'>) => void;
  removeAnnotation: (id: string) => void;
  updateAnnotation: (id: string, data: Partial<Annotation>) => void;
  
  // Text selection actions
  setTextSelection: (selection: TextSelection | null) => void;
  
  // Annotation options actions
  setAnnotationOptions: (options: Partial<AnnotationOptions>) => void;
  
  // Processing actions
  setProcessing: (processing: boolean, progress?: number, stage?: string) => void;
  
  // Document actions
  markDirty: () => void;
  markClean: () => void;
  
  // Reset actions
  resetEditor: () => void;
}

export const usePDFEditorStore = create<PDFEditorState>()(
  persist(
    (set, get) => ({
      // Initial state
      currentDocument: null,
      processedDocument: null,
      documentPages: 0,
      currentPage: 1,
      zoomLevel: 1.0,
      
      activeTab: 'edit',
      activeTool: 'select',
      sidebarOpen: true,
      
      // Professional toolbar state
      primaryTab: 'view',
      zoomMode: 'custom',
      
      annotations: [],
      textSelection: null,
      isDirty: false,
      annotationOptions: {
        highlightColor: '#FFFF00',
        highlightOpacity: 0.5,
        drawColor: '#FF0000',
        drawWidth: 2,
        commentStyle: 'sticky',
        stampType: 'approved'
      },
      
      isProcessing: false,
      processingProgress: 0,
      processingStage: '',
      
      // Actions
      setDocument: (file) => set({ 
        currentDocument: file,
        currentPage: 1,
        isDirty: false 
      }),
      
      setProcessedDocument: (result) => set({ 
        processedDocument: result,
        documentPages: result.metadata?.pages || 0 
      }),
      
      setActiveTab: (tab) => {
        // Reset active tool when switching tabs to prevent cross-contamination
        let newActiveTool: EditorTool = 'select';
        
        // Set appropriate default tool for each tab
        switch (tab) {
          case 'edit':
            newActiveTool = 'text';
            break;
          case 'annotate':
            newActiveTool = 'highlight';
            break;
          case 'pages':
            newActiveTool = 'page-manager';
            break;
          case 'forms':
            newActiveTool = 'form';
            break;
          case 'ocr':
            newActiveTool = 'ocr';
            break;
          case 'convert':
            newActiveTool = 'convert';
            break;
          case 'compress':
            newActiveTool = 'select';
            break;
          default:
            newActiveTool = 'select';
            break;
        }
        
        set({ 
          activeTab: tab, 
          activeTool: newActiveTool 
        });
      },
      
      setActiveTool: (tool) => set({ activeTool: tool }),
      
      setCurrentPage: (page) => {
        const { documentPages } = get();
        if (page >= 1 && page <= documentPages) {
          set({ currentPage: page });
        }
      },
      
      setZoomLevel: (zoom) => {
        if (zoom >= 0.1 && zoom <= 5.0) {
          set({ zoomLevel: zoom });
        }
      },
      
      setSidebarOpen: (open) => set({ sidebarOpen: open }),
      
      // Professional toolbar actions
      setPrimaryTab: (tab) => set({ primaryTab: tab }),
      setZoomMode: (mode) => set({ zoomMode: mode }),
      
      // Annotation actions
      addAnnotation: (annotationData) => {
        const annotation: Annotation = {
          ...annotationData,
          id: `annotation_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          createdAt: new Date()
        };
        
        set(state => ({
          annotations: [...state.annotations, annotation],
          isDirty: true
        }));
      },
      
      removeAnnotation: (id) => set(state => ({
        annotations: state.annotations.filter(a => a.id !== id),
        isDirty: true
      })),
      
      updateAnnotation: (id, data) => set(state => ({
        annotations: state.annotations.map(a => 
          a.id === id ? { ...a, ...data } : a
        ),
        isDirty: true
      })),
      
      // Text selection actions
      setTextSelection: (selection) => set({ textSelection: selection }),
      
      // Annotation options actions
      setAnnotationOptions: (options) => set(state => ({
        annotationOptions: { ...state.annotationOptions, ...options }
      })),
      
      // Processing actions
      setProcessing: (processing, progress = 0, stage = '') => set({
        isProcessing: processing,
        processingProgress: progress,
        processingStage: stage
      }),
      
      // Document actions
      markDirty: () => set({ isDirty: true }),
      markClean: () => set({ isDirty: false }),
      
      // Reset actions
      resetEditor: () => set({
        currentDocument: null,
        processedDocument: null,
        documentPages: 0,
        currentPage: 1,
        zoomLevel: 1.0,
        activeTab: 'edit',
        activeTool: 'select',
        primaryTab: 'view',
        zoomMode: 'custom',
        annotations: [],
        textSelection: null,
        isDirty: false,
        isProcessing: false,
        processingProgress: 0,
        processingStage: ''
      })
    }),
    {
      name: 'pdf-editor-storage',
      // Only persist UI preferences, not document data
      partialize: (state) => ({
        activeTab: state.activeTab,
        sidebarOpen: state.sidebarOpen,
        zoomLevel: state.zoomLevel
      })
    }
  )
);
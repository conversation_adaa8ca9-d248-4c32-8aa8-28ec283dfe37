import { PDFDocument, rgb } from 'pdf-lib';

export interface WorkerMessage {
  id: string;
  type: 'process' | 'compress' | 'addSignature' | 'addWatermark' | 'validate' | 'getInfo';
  payload: {
    file?: ArrayBuffer;
    fileName?: string;
    options?: any;
    targetSize?: number;
    signature?: any;
    position?: any;
    watermark?: any;
  };
}

export interface WorkerResponse {
  id: string;
  type: 'success' | 'error' | 'progress';
  payload: any;
}

class PDFWorkerProcessor {
  constructor() {
    self.addEventListener('message', this.handleMessage.bind(this));
  }

  private async handleMessage(event: MessageEvent<WorkerMessage>) {
    const { id, type, payload } = event.data;

    try {
      switch (type) {
        case 'process':
          await this.processDocument(id, payload);
          break;
        case 'compress':
          await this.compressDocument(id, payload);
          break;
        case 'addSignature':
          await this.addSignature(id, payload);
          break;
        case 'addWatermark':
          await this.addWatermark(id, payload);
          break;
        case 'validate':
          await this.validatePDF(id, payload);
          break;
        case 'getInfo':
          await this.getPDFInfo(id, payload);
          break;
        default:
          this.sendError(id, new Error(`Unknown message type: ${type}`));
      }
    } catch (error) {
      this.sendError(id, error instanceof Error ? error : new Error('Unknown error'));
    }
  }

  private async processDocument(id: string, payload: any) {
    if (!payload.file || !payload.fileName) {
      throw new Error('File data and filename are required');
    }

    this.sendProgress(id, {
      stage: 'loading',
      percentage: 0,
      currentStep: 'Loading PDF document...'
    });

    const pdfDoc = await PDFDocument.load(payload.file);

    this.sendProgress(id, {
      stage: 'processing',
      percentage: 25,
      currentStep: 'Processing document...'
    });

    // Get document metadata
    const pageCount = pdfDoc.getPageCount();
    const title = pdfDoc.getTitle();
    const author = pdfDoc.getAuthor();

    this.sendProgress(id, {
      stage: 'compressing',
      percentage: 50,
      currentStep: 'Applying compression...'
    });

    // Apply basic optimization
    const processedBytes = await pdfDoc.save({
      useObjectStreams: false
    });

    this.sendProgress(id, {
      stage: 'finalizing',
      percentage: 100,
      currentStep: 'Complete!'
    });

    const result = {
      data: new Uint8Array(processedBytes),
      originalSize: payload.file.byteLength,
      processedSize: processedBytes.length,
      compressionRatio: payload.file.byteLength / processedBytes.length,
      processingTimeMs: performance.now(),
      metadata: {
        pages: pageCount,
        title: title || undefined,
        author: author || undefined
      }
    };

    this.sendSuccess(id, result);
  }

  private async compressDocument(id: string, payload: any) {
    if (!payload.file || !payload.fileName || !payload.targetSize) {
      throw new Error('File data, filename, and target size are required');
    }

    this.sendProgress(id, {
      stage: 'loading',
      percentage: 0,
      currentStep: 'Loading PDF for compression...'
    });

    const pdfDoc = await PDFDocument.load(payload.file);

    this.sendProgress(id, {
      stage: 'compressing',
      percentage: 50,
      currentStep: 'Compressing to target size...'
    });

    // Apply compression (simplified for now)
    const processedBytes = await pdfDoc.save({
      useObjectStreams: false
    });

    this.sendProgress(id, {
      stage: 'finalizing',
      percentage: 100,
      currentStep: 'Compression complete!'
    });

    this.sendSuccess(id, { data: new Uint8Array(processedBytes) });
  }

  private async addSignature(id: string, payload: any) {
    if (!payload.file || !payload.fileName || !payload.signature || !payload.position) {
      throw new Error('File data, filename, signature, and position are required');
    }

    const pdfDoc = await PDFDocument.load(payload.file);
    const pages = pdfDoc.getPages();
    
    if (payload.position.page >= pages.length) {
      throw new Error(`Page ${payload.position.page} does not exist`);
    }

    const page = pages[payload.position.page];
    const signatureImage = await pdfDoc.embedPng(payload.signature.imageData);

    page.drawImage(signatureImage, {
      x: payload.position.x,
      y: payload.position.y,
      width: payload.signature.width,
      height: payload.signature.height
    });

    const processedBytes = await pdfDoc.save();
    this.sendSuccess(id, { data: new Uint8Array(processedBytes) });
  }

  private async addWatermark(id: string, payload: any) {
    if (!payload.file || !payload.fileName || !payload.watermark) {
      throw new Error('File data, filename, and watermark options are required');
    }

    const pdfDoc = await PDFDocument.load(payload.file);
    const pages = pdfDoc.getPages();

    for (const page of pages) {
      const { width, height } = page.getSize();

      if (payload.watermark.text) {
        const fontSize = payload.watermark.fontSize || 50;
        let x: number, y: number;

        switch (payload.watermark.position) {
          case 'center':
            x = width / 2;
            y = height / 2;
            break;
          case 'top-left':
            x = 50;
            y = height - 50;
            break;
          case 'top-right':
            x = width - 50;
            y = height - 50;
            break;
          case 'bottom-left':
            x = 50;
            y = 50;
            break;
          case 'bottom-right':
            x = width - 50;
            y = 50;
            break;
          default:
            x = width / 2;
            y = height / 2;
        }

        page.drawText(payload.watermark.text, {
          x,
          y,
          size: fontSize,
          color: rgb(0.5, 0.5, 0.5),
          opacity: payload.watermark.opacity
        });
      }
    }

    const processedBytes = await pdfDoc.save();
    this.sendSuccess(id, { data: new Uint8Array(processedBytes) });
  }

  private async validatePDF(id: string, payload: any) {
    if (!payload.file || !payload.fileName) {
      throw new Error('File data and filename are required');
    }

    try {
      await PDFDocument.load(payload.file);
      this.sendSuccess(id, { valid: true });
    } catch {
      this.sendSuccess(id, { valid: false });
    }
  }

  private async getPDFInfo(id: string, payload: any) {
    if (!payload.file || !payload.fileName) {
      throw new Error('File data and filename are required');
    }

    const pdfDoc = await PDFDocument.load(payload.file);
    
    const info = {
      pages: pdfDoc.getPageCount(),
      size: payload.file.byteLength,
      title: pdfDoc.getTitle() || undefined,
      author: pdfDoc.getAuthor() || undefined
    };

    this.sendSuccess(id, info);
  }

  private sendSuccess(id: string, payload: any) {
    const response: WorkerResponse = {
      id,
      type: 'success',
      payload
    };
    self.postMessage(response);
  }

  private sendError(id: string, error: Error) {
    const response: WorkerResponse = {
      id,
      type: 'error',
      payload: {
        message: error.message,
        name: error.name
      }
    };
    self.postMessage(response);
  }

  private sendProgress(id: string, progress: any) {
    const response: WorkerResponse = {
      id,
      type: 'progress',
      payload: progress
    };
    self.postMessage(response);
  }
}

// Initialize the worker processor
new PDFWorkerProcessor();
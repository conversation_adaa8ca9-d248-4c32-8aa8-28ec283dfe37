import type { WorkerMessage, WorkerResponse } from '../workers/pdf-processor.worker';
import { memoryManager, type MemoryInfo } from '../utils/memory-manager';

export interface ProcessingOptions {
  targetSize?: number;
  qualityLevel?: number;
  preserveMetadata?: boolean;
  compressionType?: 'lossless' | 'lossy' | 'adaptive';
}

export interface ProcessedResult {
  data: Uint8Array;
  originalSize: number;
  processedSize: number;
  compressionRatio: number;
  processingTimeMs: number;
  metadata?: {
    pages: number;
    title?: string;
    author?: string;
  };
}

export interface ProcessingProgress {
  stage: 'loading' | 'processing' | 'compressing' | 'finalizing';
  percentage: number;
  currentStep: string;
  estimatedTimeRemaining?: number;
}

export type ProcessingProgressCallback = (progress: ProcessingProgress) => void;

export class ProcessingError extends Error {
  constructor(
    message: string,
    public code: 'INVALID_PDF' | 'MEMORY_EXCEEDED' | 'PROCESSING_FAILED' | 'WASM_ERROR' = 'PROCESSING_FAILED',
    public originalError?: Error
  ) {
    super(message);
    this.name = 'ProcessingError';
  }
}

class PDFProcessorService {
  private static instance: PDFProcessorService;
  private worker: Worker | null = null;
  private messageId = 0;
  private pendingOperations = new Map<string, {
    resolve: (value: any) => void;
    reject: (error: Error) => void;
    progressCallback?: ProcessingProgressCallback;
  }>();

  private constructor() {}

  public static getInstance(): PDFProcessorService {
    if (!PDFProcessorService.instance) {
      PDFProcessorService.instance = new PDFProcessorService();
    }
    return PDFProcessorService.instance;
  }

  private async initializeWorker(): Promise<void> {
    if (this.worker) {
      return;
    }

    try {
      // Import the worker using Vite's worker syntax
      const WorkerClass = await import('../workers/pdf-processor.worker?worker');
      this.worker = new WorkerClass.default();

      if (this.worker) {
        this.worker.addEventListener('message', this.handleWorkerMessage.bind(this));
        this.worker.addEventListener('error', this.handleWorkerError.bind(this));
        
        // Test worker is responsive
        setTimeout(() => {
          if (this.worker) {
            console.log('PDF Worker initialized successfully');
          }
        }, 100);
      }
    } catch (error) {
      console.warn('Failed to initialize PDF worker, will use fallback processing:', error);
      // Don't throw error - let it fall back to direct processing
      this.worker = null;
    }
  }

  public async processDocument(
    file: File,
    options: ProcessingOptions = {},
    progressCallback?: ProcessingProgressCallback
  ): Promise<ProcessedResult> {
    // Check if file is safe to process
    const safetyCheck = memoryManager.isFileSafeToProcess(file.size);
    if (!safetyCheck.safe) {
      throw new ProcessingError(
        safetyCheck.reason || 'File too large to process safely',
        'MEMORY_EXCEEDED'
      );
    }

    // Monitor memory during processing
    const memoryMonitor = memoryManager.createMemoryMonitor((memoryInfo: MemoryInfo) => {
      console.warn('High memory pressure detected:', memoryInfo);
    });

    try {
      // Try worker first, fall back to direct processing if it fails
      try {
        await this.initializeWorker();

        // If worker initialization failed, throw immediately to trigger fallback
        if (!this.worker) {
          throw new Error('Worker initialization failed');
        }

        const fileArrayBuffer = await file.arrayBuffer();
        const messageId = this.generateMessageId();

        return await this.sendWorkerMessage({
          id: messageId,
          type: 'process',
          payload: {
            file: fileArrayBuffer,
            fileName: file.name,
            options
          }
        }, progressCallback);
      } catch (workerError) {
        console.warn('Worker failed, falling back to direct processing:', workerError);
        return await this.processDocumentDirect(file, options, progressCallback);
      }
    } finally {
      memoryMonitor.stop();
    }
  }

  // Fallback method for direct PDF processing when worker fails
  // Fallback method for direct PDF processing when worker fails
  private async processDocumentDirect(
    file: File,
    options: ProcessingOptions = {},
    progressCallback?: ProcessingProgressCallback
  ): Promise<ProcessedResult> {
    const startTime = Date.now();
    
    // Report progress
    progressCallback?.({
      stage: 'loading',
      percentage: 10,
      currentStep: 'Loading PDF document...'
    });

    try {
      // Dynamic import of pdf-lib to avoid bundling issues
      const { PDFDocument } = await import('pdf-lib');
      
      // Read the file
      const fileArrayBuffer = await file.arrayBuffer();
      const originalSize = fileArrayBuffer.byteLength;

      progressCallback?.({
        stage: 'processing',
        percentage: 30,
        currentStep: 'Parsing PDF structure...'
      });

      // Load the PDF document
      const pdfDoc = await PDFDocument.load(fileArrayBuffer, { 
        ignoreEncryption: true,
        capNumbers: true
      });

      progressCallback?.({
        stage: 'compressing',
        percentage: 60,
        currentStep: 'Optimizing PDF...'
      });

      // Apply compression based on options
      const pages = pdfDoc.getPages();
      const metadata = {
        pages: pages.length,
        title: pdfDoc.getTitle(),
        author: pdfDoc.getAuthor()
      };

      // Apply metadata removal if not preserving
      if (!options.preserveMetadata) {
        pdfDoc.setTitle('');
        pdfDoc.setAuthor('');
        pdfDoc.setSubject('');
        pdfDoc.setKeywords([]);
        pdfDoc.setProducer('');
        pdfDoc.setCreator('');
      }

      // Apply comprehensive page-level optimizations
      const qualityLevel = options.qualityLevel || 0.8;
      
      // Always apply basic optimizations
      for (const page of pages) {
        const pageDict = page.node;
        
        // Remove optional metadata that takes up space
        if (!options.preserveMetadata) {
          pageDict.delete('UserUnit');
          pageDict.delete('VP'); // Viewport
          pageDict.delete('BoxColorInfo');
          pageDict.delete('Metadata');
          pageDict.delete('PieceInfo');
          pageDict.delete('LastModified');
          pageDict.delete('SeparationInfo');
        }
        
        // Remove unused content streams and resources
        const resources = pageDict.get('Resources');
        if (resources) {
          // Clean up unused font references
          const fonts = resources.get('Font');
          if (fonts && fonts.size() === 0) {
            resources.delete('Font');
          }
          
          // Clean up unused XObject references  
          const xObjects = resources.get('XObject');
          if (xObjects && xObjects.size() === 0) {
            resources.delete('XObject');
          }
          
          // Clean up unused pattern references
          const patterns = resources.get('Pattern');
          if (patterns && patterns.size() === 0) {
            resources.delete('Pattern');
          }
        }
      }
      
      // Advanced compression for lossy/adaptive modes
      if (options.compressionType === 'lossy' || options.compressionType === 'adaptive') {
        console.log('Applying advanced compression techniques...');
        
        // 1. Remove duplicate objects and consolidate resources
        const context = pdfDoc.context;
        const objectsToOptimize: any[] = [];
        
        // Collect objects for optimization
        const objects = context.enumerateIndirectObjects();
        for (const [ref, object] of objects) {
          if (object) {
            objectsToOptimize.push({ ref, object });
          }
        }
        
        console.log(`Found ${objectsToOptimize.length} objects to optimize`);
        
        // 2. Optimize images and XObjects
        for (const { ref, object } of objectsToOptimize) {
          try {
            const obj = object as any;
            
            // Handle XObject (Image) optimization
            if (obj.dict && obj.dict.get && obj.dict.get('Type')?.toString() === 'XObject') {
              const subtype = obj.dict.get('Subtype')?.toString();
              
              if (subtype === 'Image') {
                console.log('Optimizing image object:', ref.toString());
                
                // Remove optional image metadata
                obj.dict.delete('Name');
                obj.dict.delete('Intent');
                obj.dict.delete('Metadata');
                obj.dict.delete('OPI');
                obj.dict.delete('Alternates');
                
                // For lossy compression, reduce image quality parameters
                if (options.compressionType === 'lossy') {
                  // Try to reduce image quality by modifying compression settings
                  const filter = obj.dict.get('Filter');
                  if (filter && !obj.dict.has('DecodeParams')) {
                    // Add decode parameters for compression
                    const decodeParams = context.obj({
                      Predictor: 12,
                      Colors: 3,
                      BitsPerComponent: 8,
                      Columns: obj.dict.get('Width') || 100
                    });
                    obj.dict.set('DecodeParams', decodeParams);
                  }
                }
              }
            }
            
            // Handle Font optimization
            if (obj.dict && obj.dict.get && obj.dict.get('Type')?.toString() === 'Font') {
              console.log('Optimizing font object:', ref.toString());
              
              // Remove optional font metadata
              obj.dict.delete('Name');
              obj.dict.delete('ToUnicode');
              obj.dict.delete('Encoding'); // Use default encoding to save space
              obj.dict.delete('Widths'); // Remove width information for standard fonts
            }
            
            // Handle Content Stream optimization
            if (obj.dict && obj.contents) {
              console.log('Optimizing content stream:', ref.toString());
              
              // Remove optional content stream metadata
              obj.dict.delete('Name');
              obj.dict.delete('DecodeParms');
              obj.dict.delete('DL'); // Download hint
              obj.dict.delete('F'); // External file reference
              obj.dict.delete('FFilter');
              obj.dict.delete('FDecodeParms');
              
              // Force compression for content streams
              if (!obj.dict.has('Filter')) {
                obj.dict.set('Filter', context.name('FlateDecode'));
              }
            }
            
            // Remove all optional metadata from objects
            if (obj.dict && obj.dict.delete) {
              // Remove creation/modification metadata
              obj.dict.delete('Producer');
              obj.dict.delete('Creator');
              obj.dict.delete('CreationDate');
              obj.dict.delete('ModDate');
              obj.dict.delete('Title');
              obj.dict.delete('Author');
              obj.dict.delete('Subject');
              obj.dict.delete('Keywords');
              obj.dict.delete('Trapped');
              obj.dict.delete('AcroForm');
              obj.dict.delete('Metadata');
              obj.dict.delete('PieceInfo');
              obj.dict.delete('PermsObj');
            }
            
          } catch (e) {
            // Skip objects that can't be optimized
            console.warn('Could not optimize object:', ref.toString(), e);
          }
        }
        
        // 3. Force removal of optional PDF features
        try {
          // Remove optional catalog entries
          const catalog = pdfDoc.catalog;
          const catalogDict = (catalog as any).dict;
          
          if (catalogDict && catalogDict.delete) {
            catalogDict.delete('Metadata');
            catalogDict.delete('StructTreeRoot');
            catalogDict.delete('MarkInfo');
            catalogDict.delete('Lang');
            catalogDict.delete('SpiderInfo');
            catalogDict.delete('OutputIntents');
            catalogDict.delete('PieceInfo');
            catalogDict.delete('Perms');
            catalogDict.delete('Legal');
            catalogDict.delete('Requirements');
            catalogDict.delete('Collection');
            catalogDict.delete('NeedsRendering');
          }
        } catch (e) {
          console.warn('Could not optimize catalog:', e);
        }
        
        console.log('Advanced compression techniques applied');
      }

      progressCallback?.({
        stage: 'finalizing',
        percentage: 90,
        currentStep: 'Generating optimized PDF...'
      });

      // Determine compression settings based on options
      const useCompression = options.compressionType !== 'lossless';
      
      // Base compression settings
      let saveOptions: any = {
        useObjectStreams: useCompression,
        addDefaultPage: false,
        objectsPerTick: 50, // Process more objects per tick for better compression
      };

      // Apply compression based on type
      if (useCompression) {
        saveOptions.compressStreams = true;
        
        // Adaptive compression based on quality level
        if (options.compressionType === 'adaptive') {
          saveOptions.objectsPerTick = Math.max(10, Math.floor(qualityLevel * 50));
        }
        
        // Aggressive compression for lossy mode
        if (options.compressionType === 'lossy') {
          saveOptions = {
            ...saveOptions,
            objectsPerTick: Math.max(5, Math.floor(qualityLevel * 20)), // More aggressive
            compressStreams: true,
            // Force compression of all compressible streams
            updateFieldAppearances: false, // Skip appearance updates to save space
          };
        }
      }
      
      console.log('Using save options:', saveOptions, 'for compression type:', options.compressionType);
      
      // Save with compression settings
      const processedData = await pdfDoc.save(saveOptions);

      const processedSize = processedData.byteLength;
      const compressionRatio = processedSize / originalSize;
      const processingTimeMs = Date.now() - startTime;

      progressCallback?.({
        stage: 'finalizing',
        percentage: 100,
        currentStep: 'Processing complete!'
      });

      return {
        data: new Uint8Array(processedData),
        originalSize,
        processedSize,
        compressionRatio,
        processingTimeMs,
        metadata
      };

    } catch (error) {
      throw new ProcessingError(
        `Direct processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'PROCESSING_FAILED',
        error instanceof Error ? error : undefined
      );
    }
  }

  public async compressToTarget(
    file: File,
    targetSize: number,
    progressCallback?: ProcessingProgressCallback
  ): Promise<Uint8Array> {
    // Check if file is safe to process
    const safetyCheck = memoryManager.isFileSafeToProcess(file.size);
    if (!safetyCheck.safe) {
      throw new ProcessingError(
        safetyCheck.reason || 'File too large to process safely',
        'MEMORY_EXCEEDED'
      );
    }

    await this.initializeWorker();

    const fileArrayBuffer = await file.arrayBuffer();
    const messageId = this.generateMessageId();

    const result = await this.sendWorkerMessage({
      id: messageId,
      type: 'compress',
      payload: {
        file: fileArrayBuffer,
        fileName: file.name,
        targetSize
      }
    }, progressCallback);

    return result.data;
  }

  public async validatePDF(file: File): Promise<boolean> {
    // First do a quick client-side validation
    try {
      const firstBytes = await file.slice(0, 4).arrayBuffer();
      const header = new Uint8Array(firstBytes);
      const pdfMagic = [0x25, 0x50, 0x44, 0x46]; // %PDF
      
      const hasValidHeader = pdfMagic.every((byte, index) => header[index] === byte);
      if (!hasValidHeader) {
        return false;
      }

      // Then validate with the worker for more thorough check
      await this.initializeWorker();

      const fileArrayBuffer = await file.arrayBuffer();
      const messageId = this.generateMessageId();

      const result = await this.sendWorkerMessage({
        id: messageId,
        type: 'validate',
        payload: {
          file: fileArrayBuffer,
          fileName: file.name
        }
      });

      return result.valid;
    } catch {
      return false;
    }
  }

  public async getPDFInfo(file: File): Promise<{
    pages: number;
    size: number;
    title?: string;
    author?: string;
  }> {
    await this.initializeWorker();

    const fileArrayBuffer = await file.arrayBuffer();
    const messageId = this.generateMessageId();

    return this.sendWorkerMessage({
      id: messageId,
      type: 'getInfo',
      payload: {
        file: fileArrayBuffer,
        fileName: file.name
      }
    });
  }

  public getMemoryUsage(): MemoryInfo {
    return memoryManager.getMemoryInfo();
  }

  public checkMemoryPressure(): boolean {
    return memoryManager.isMemoryPressureHigh();
  }

  public forceGarbageCollection(): void {
    memoryManager.forceGarbageCollection();
  }

  public getMemoryUsagePercentage(): number {
    return memoryManager.getMemoryUsagePercentage();
  }

  public isSharedArrayBufferSupported(): boolean {
    return memoryManager.isSharedArrayBufferSupported();
  }

  public dispose(): void {
    if (this.worker) {
      this.worker.terminate();
      this.worker = null;
    }

    // Reject all pending operations
    for (const [, operation] of this.pendingOperations) {
      operation.reject(new ProcessingError('PDF processor was disposed'));
    }
    this.pendingOperations.clear();
  }

  private generateMessageId(): string {
    return `msg_${++this.messageId}_${Date.now()}`;
  }

  private sendWorkerMessage(message: WorkerMessage, progressCallback?: ProcessingProgressCallback): Promise<any> {
    return new Promise((resolve, reject) => {
      if (!this.worker) {
        reject(new ProcessingError('Worker not initialized', 'WASM_ERROR'));
        return;
      }

      this.pendingOperations.set(message.id, {
        resolve,
        reject,
        progressCallback
      });

      this.worker.postMessage(message);
    });
  }

  private handleWorkerMessage(event: MessageEvent<WorkerResponse>): void {
    const { id, type, payload } = event.data;
    const operation = this.pendingOperations.get(id);

    if (!operation) {
      console.warn(`Received message for unknown operation: ${id}`);
      return;
    }

    switch (type) {
      case 'success':
        this.pendingOperations.delete(id);
        operation.resolve(payload);
        break;

      case 'error':
        this.pendingOperations.delete(id);
        const error = new ProcessingError(
          payload.message,
          payload.code || 'PROCESSING_FAILED'
        );
        operation.reject(error);
        break;

      case 'progress':
        if (operation.progressCallback) {
          operation.progressCallback(payload);
        }
        break;

      default:
        console.warn(`Unknown message type: ${type}`);
    }
  }

  private handleWorkerError(error: ErrorEvent): void {
    console.error('PDF Worker error:', error);
    
    // Reset worker to null so future operations fall back to direct processing
    this.worker = null;
    
    // Reject all pending operations - they will be retried with fallback
    for (const [, operation] of this.pendingOperations) {
      const processingError = new ProcessingError(
        `Worker error: ${error.message}`,
        'WASM_ERROR'
      );
      operation.reject(processingError);
    }
    
    this.pendingOperations.clear();
  }
}

// Export singleton instance
export const pdfProcessor = PDFProcessorService.getInstance();
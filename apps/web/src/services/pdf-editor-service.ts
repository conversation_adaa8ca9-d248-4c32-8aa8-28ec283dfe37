import { getPDFService } from '../../../../packages/pdf-engine/src/index.js';
import type { 
  PDFService,
  PDFTextEditor,
  PDFPageManager,
  PDFAnnotationSystem,
  PDFOCRProcessor,
  PDFFormatConverter,
  FormProcessor,
  IntelligentCompressionEngine,
  MuPDFWASMWrapper,
  TextBlock,
  TextLayout,
  TextModification,
  TextRange,
  TextStyle,
  Annotation,
  FormField,
  SaveOptions
} from '../../../../packages/pdf-engine/src/index.js';
import type { ProcessedResult } from './pdf-processor.js';

// PDF Editor Service Types
export interface PDFDocument {
  file: File;
  data: ArrayBuffer;
  pageCount: number;
  title?: string;
  author?: string;
}

export interface EditorDocument {
  document: PDFDocument;
  engineDoc: any; // MuPDF document handle
  textEditor: PDFTextEditor;
  pageManager: PDFPageManager;
  annotationSystem: PDFAnnotationSystem;
  ocrProcessor: PDFOCRProcessor;
  formatConverter: PDFFormatConverter;
  formProcessor: FormProcessor;
  compressionEngine: IntelligentCompressionEngine;
}

export interface RenderOptions {
  page: number;
  scale: number;
  rotation?: number;
}

export interface TextInsertOptions {
  text: string;
  position: { x: number; y: number };
  style: TextStyle;
  page: number;
}

export interface AnnotationOptions {
  type: 'highlight' | 'comment' | 'stamp' | 'drawing';
  position: { x: number; y: number; width?: number; height?: number };
  color: string;
  opacity: number;
  content?: string;
  page: number;
}

/**
 * PDF Editor Service - High-level interface for PDF editing operations
 * Integrates the PDF engine with the editor UI components
 */
export class PDFEditorService {
  private static instance: PDFEditorService;
  private pdfService: PDFService;
  private currentDocument: EditorDocument | null = null;
  private wasmWrapper: MuPDFWASMWrapper | null = null;

  private constructor() {
    this.pdfService = getPDFService();
  }

  public static getInstance(): PDFEditorService {
    if (!PDFEditorService.instance) {
      PDFEditorService.instance = new PDFEditorService();
    }
    return PDFEditorService.instance;
  }

  /**
   * Initialize the service and WASM engine
   */
  public async initialize(): Promise<void> {
    try {
      await this.pdfService.initialize();
      console.log('PDF Editor Service initialized successfully (using fallback processing)');
    } catch (error) {
      console.warn('PDF Service initialization failed, using minimal fallback:', error);
      // Don't throw error - we can still use basic PDF functionality
    }
  }

  /**
   * Load a PDF document for editing
   */
  public async loadDocument(file: File): Promise<PDFDocument> {
    try {
      // Basic validation - check file type
      if (file.type !== 'application/pdf' && !file.name.toLowerCase().endsWith('.pdf')) {
        throw new Error('Please select a valid PDF file');
      }

      // Load file data
      const data = await file.arrayBuffer();

      // Create basic PDF document info
      const document: PDFDocument = {
        file,
        data,
        pageCount: 1, // Default, will be updated when PDF is loaded
        title: file.name,
        author: undefined
      };

      console.log('PDF document loaded successfully:', document);
      return document;
    } catch (error) {
      console.error('Failed to load document:', error);
      throw error;
    }
  }

  /**
   * Render a page as ImageData for display
   */
  public async renderPage(options: RenderOptions): Promise<ImageData> {
    if (!this.currentDocument) {
      throw new Error('No document loaded');
    }

    try {
      // Return a placeholder ImageData for now
      // In a full implementation, this would use MuPDF WASM to render the page
      const canvas = document.createElement('canvas');
      canvas.width = 800;
      canvas.height = 1000;
      const ctx = canvas.getContext('2d')!;
      
      // Fill with white background
      ctx.fillStyle = 'white';
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      
      // Add placeholder text
      ctx.fillStyle = 'black';
      ctx.font = '20px Arial';
      ctx.fillText(`Page ${options.page}`, 50, 50);
      ctx.fillText('PDF Preview (MuPDF WASM not loaded)', 50, 100);
      
      return ctx.getImageData(0, 0, canvas.width, canvas.height);
    } catch (error) {
      console.error('Failed to render page:', error);
      throw error;
    }
  }

  /**
   * Extract text blocks from a page
   */
  public async extractTextBlocks(pageNum: number): Promise<TextBlock[]> {
    if (!this.wasmWrapper || !this.currentDocument) {
      throw new Error('No document loaded');
    }

    try {
      return await this.wasmWrapper.extractTextBlocks(this.currentDocument.engineDoc, pageNum);
    } catch (error) {
      console.error('Failed to extract text blocks:', error);
      throw error;
    }
  }

  /**
   * Get text layout for a page
   */
  public async getTextLayout(pageNum: number): Promise<TextLayout> {
    if (!this.wasmWrapper || !this.currentDocument) {
      throw new Error('No document loaded');
    }

    try {
      return await this.wasmWrapper.getTextLayout(this.currentDocument.engineDoc, pageNum);
    } catch (error) {
      console.error('Failed to get text layout:', error);
      throw error;
    }
  }

  /**
   * Insert text at specified position
   */
  public async insertText(options: TextInsertOptions): Promise<void> {
    if (!this.currentDocument) {
      throw new Error('No document loaded');
    }

    try {
      await this.currentDocument.textEditor.insertText(
        options.page,
        options.position,
        options.text,
        options.style
      );
    } catch (error) {
      console.error('Failed to insert text:', error);
      throw error;
    }
  }

  /**
   * Delete text in specified range
   */
  public async deleteText(pageNum: number, textRange: TextRange): Promise<void> {
    if (!this.wasmWrapper || !this.currentDocument) {
      throw new Error('No document loaded');
    }

    try {
      await this.wasmWrapper.deleteText(this.currentDocument.engineDoc, pageNum, textRange);
    } catch (error) {
      console.error('Failed to delete text:', error);
      throw error;
    }
  }

  /**
   * Add annotation to document
   */
  public async addAnnotation(options: AnnotationOptions): Promise<void> {
    if (!this.currentDocument) {
      throw new Error('No document loaded');
    }

    try {
      const annotation: Annotation = {
        id: `annotation_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        type: options.type,
        pageNum: options.page,
        position: options.position,
        color: options.color,
        opacity: options.opacity,
        content: options.content || '',
        createdAt: new Date()
      };

      await this.currentDocument.annotationSystem.addHighlight(
        annotation.pageNum,
        annotation.position,
        annotation.color,
        annotation.opacity
      );
    } catch (error) {
      console.error('Failed to add annotation:', error);
      throw error;
    }
  }

  /**
   * Perform OCR on a page
   */
  public async performOCR(pageNum: number, language: string = 'eng'): Promise<string> {
    if (!this.currentDocument) {
      throw new Error('No document loaded');
    }

    try {
      const result = await this.currentDocument.ocrProcessor.processPage(
        pageNum,
        { language }
      );
      return result.text;
    } catch (error) {
      console.error('Failed to perform OCR:', error);
      throw error;
    }
  }

  /**
   * Get form fields from document
   */
  public async getFormFields(): Promise<FormField[]> {
    if (!this.wasmWrapper || !this.currentDocument) {
      throw new Error('No document loaded');
    }

    try {
      return await this.wasmWrapper.getFormFields(this.currentDocument.engineDoc);
    } catch (error) {
      console.error('Failed to get form fields:', error);
      throw error;
    }
  }

  /**
   * Convert document to different format
   */
  public async convertToFormat(format: 'docx' | 'png' | 'jpg' | 'tiff'): Promise<Uint8Array> {
    if (!this.currentDocument) {
      throw new Error('No document loaded');
    }

    try {
      switch (format) {
        case 'docx':
          return await this.currentDocument.formatConverter.convertToWord();
        case 'png':
          return await this.currentDocument.formatConverter.convertToImages('png');
        case 'jpg':
          return await this.currentDocument.formatConverter.convertToImages('jpeg');
        case 'tiff':
          return await this.currentDocument.formatConverter.convertToImages('tiff');
        default:
          throw new Error(`Unsupported format: ${format}`);
      }
    } catch (error) {
      console.error('Failed to convert document:', error);
      throw error;
    }
  }

  /**
   * Compress document with specified options
   */
  public async compressDocument(
    compressionLevel: 'low' | 'medium' | 'high' = 'medium'
  ): Promise<Uint8Array> {
    if (!this.currentDocument) {
      throw new Error('No document loaded');
    }

    try {
      const result = await this.currentDocument.compressionEngine.compress({
        level: compressionLevel,
        preserveQuality: compressionLevel === 'low',
        optimizeImages: true,
        removeMetadata: compressionLevel === 'high'
      });
      return result.data;
    } catch (error) {
      console.error('Failed to compress document:', error);
      throw error;
    }
  }

  /**
   * Save document with current modifications
   */
  public async saveDocument(options: SaveOptions = {}): Promise<Uint8Array> {
    if (!this.wasmWrapper || !this.currentDocument) {
      throw new Error('No document loaded');
    }

    try {
      const result = await this.wasmWrapper.saveDocument(this.currentDocument.engineDoc, options);
      return new Uint8Array(result);
    } catch (error) {
      console.error('Failed to save document:', error);
      throw error;
    }
  }

  /**
   * Get page count of current document
   */
  public getPageCount(): number {
    if (!this.currentDocument) {
      return 0;
    }

    try {
      return this.currentDocument.document.pageCount;
    } catch (error) {
      console.error('Failed to get page count:', error);
      return 1; // Default to 1 page
    }
  }

  /**
   * Close current document and cleanup resources
   */
  public closeDocument(): void {
    if (this.currentDocument) {
      console.log('Closing PDF document');
    }
    this.currentDocument = null;
  }

  /**
   * Get current document
   */
  public getCurrentDocument(): PDFDocument | null {
    return this.currentDocument?.document || null;
  }

  /**
   * Check if service is ready
   */
  public isReady(): boolean {
    return this.wasmWrapper?.isReady || false;
  }

  /**
   * Get memory usage information
   */
  public getMemoryUsage(): any {
    return this.pdfService.getMemoryUsage();
  }

  /**
   * Cleanup all resources
   */
  public dispose(): void {
    this.closeDocument();
    if (this.wasmWrapper) {
      this.wasmWrapper.cleanup();
      this.wasmWrapper = null;
    }
    this.pdfService.dispose();
  }
}

// Export singleton instance
export const pdfEditorService = PDFEditorService.getInstance();
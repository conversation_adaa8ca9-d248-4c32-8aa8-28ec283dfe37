import { supabase } from './supabase'
import type { 
  User, 
  Session,
  LoginCredentials, 
  RegisterCredentials, 
  AuthResult 
} from 'shared'

export class AuthService {
  async login(credentials: LoginCredentials): Promise<AuthResult> {
    const { data, error } = await supabase.auth.signInWithPassword({
      email: credentials.email,
      password: credentials.password,
    })

    if (error) {
      throw new Error(error.message)
    }

    if (!data.user || !data.session) {
      throw new Error('Login failed')
    }

    // Fetch user data from database to get subscription tier and role
    const { data: userData } = await supabase
      .from('users')
      .select('subscription_tier, role')
      .eq('id', data.user.id)
      .single()

    const user: User = {
      id: data.user.id,
      email: data.user.email!,
      name: data.user.user_metadata?.name || data.user.email!.split('@')[0],
      avatar_url: data.user.user_metadata?.avatar_url || null,
      subscription_tier: userData?.subscription_tier || 'free',
      role: userData?.role || 'user', // Fetch from database or default to 'user'
      created_at: data.user.created_at,
      updated_at: data.user.updated_at || data.user.created_at,
    }

    const session: Session = {
      access_token: data.session.access_token,
      refresh_token: data.session.refresh_token,
      expires_at: data.session.expires_at || 0,
      user,
    }

    return { user, session }
  }

  async register(credentials: RegisterCredentials): Promise<AuthResult> {
    const { data, error } = await supabase.auth.signUp({
      email: credentials.email,
      password: credentials.password,
      options: {
        data: {
          name: credentials.name,
        }
      }
    })

    if (error) {
      throw new Error(error.message)
    }

    if (!data.user || !data.session) {
      throw new Error('Registration failed')
    }

    // Create user record in database first, then fetch to get subscription tier
    const { error: insertError } = await supabase
      .from('users')
      .insert({
        id: data.user.id,
        email: data.user.email!,
        name: credentials.name,
        avatar_url: null,
        subscription_tier: 'free'
      })

    if (insertError && insertError.code !== '23505') { // Ignore unique constraint violations
      console.warn('Failed to create user record:', insertError.message)
    }

    // Fetch user data from database to get subscription tier and role
    const { data: userData } = await supabase
      .from('users')
      .select('subscription_tier, role')
      .eq('id', data.user.id)
      .single()

    const user: User = {
      id: data.user.id,
      email: data.user.email!,
      name: credentials.name,
      avatar_url: null,
      subscription_tier: userData?.subscription_tier || 'free',
      role: userData?.role || 'user',
      created_at: data.user.created_at,
      updated_at: data.user.updated_at || data.user.created_at,
    }

    const session: Session = {
      access_token: data.session.access_token,
      refresh_token: data.session.refresh_token,
      expires_at: data.session.expires_at || 0,
      user,
    }

    return { user, session }
  }

  async logout(): Promise<void> {
    const { error } = await supabase.auth.signOut()
    if (error) {
      throw new Error(error.message)
    }
  }

  async refreshToken(): Promise<string> {
    const { data, error } = await supabase.auth.refreshSession()
    
    if (error) {
      throw new Error(error.message)
    }

    if (!data.session) {
      throw new Error('Failed to refresh token')
    }

    return data.session.access_token
  }

  async getCurrentUser(): Promise<User | null> {
    const { data, error } = await supabase.auth.getUser()
    
    if (error || !data.user) {
      return null
    }

    // Fetch user data from database to get subscription tier and role
    const { data: userData } = await supabase
      .from('users')
      .select('subscription_tier, role')
      .eq('id', data.user.id)
      .single()

    return {
      id: data.user.id,
      email: data.user.email!,
      name: data.user.user_metadata?.name || data.user.email!.split('@')[0],
      avatar_url: data.user.user_metadata?.avatar_url || null,
      subscription_tier: userData?.subscription_tier || 'free',
      role: userData?.role || 'user',
      created_at: data.user.created_at,
      updated_at: data.user.updated_at || data.user.created_at,
    }
  }

  async resetPassword(email: string): Promise<void> {
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/reset-password`,
    })

    if (error) {
      throw new Error(error.message)
    }
  }

  async updatePassword(newPassword: string): Promise<void> {
    const { error } = await supabase.auth.updateUser({ 
      password: newPassword 
    })

    if (error) {
      throw new Error(error.message)
    }
  }

  async deleteAccount(): Promise<void> {
    // Account deletion must be handled server-side through tRPC
    // This requires a protected endpoint that handles user data cleanup
    try {
      // This would call a tRPC endpoint that handles account deletion
      const response = await fetch('/api/trpc/user.deleteAccount', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${await this.getCurrentToken()}`,
        },
      })
      
      if (!response.ok) {
        throw new Error('Failed to delete account')
      }
    } catch (error) {
      throw new Error('Account deletion failed. Please contact support.')
    }
  }

  private async getCurrentToken(): Promise<string> {
    const { data, error } = await supabase.auth.getSession()
    
    if (error || !data.session) {
      throw new Error('No active session')
    }
    
    return data.session.access_token
  }

  // Listen to auth state changes
  onAuthStateChange(callback: (user: User | null) => void) {
    return supabase.auth.onAuthStateChange(async (_event, session) => {
      if (session?.user) {
        // Fetch user data from database to get subscription tier and role
        const { data: userData } = await supabase
          .from('users')
          .select('subscription_tier, role')
          .eq('id', session.user.id)
          .single()

        const user: User = {
          id: session.user.id,
          email: session.user.email!,
          name: session.user.user_metadata?.name || session.user.email!.split('@')[0],
          avatar_url: session.user.user_metadata?.avatar_url || null,
          subscription_tier: userData?.subscription_tier || 'free',
          role: userData?.role || 'user',
          created_at: session.user.created_at,
          updated_at: session.user.updated_at || session.user.created_at,
        }
        callback(user)
      } else {
        callback(null)
      }
    })
  }
}

export const authService = new AuthService()
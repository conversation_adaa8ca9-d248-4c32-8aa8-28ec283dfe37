const DB_NAME = 'mobilepdf-pro';
const DB_VERSION = 1;
const PROCESSING_STORE = 'processing-history';
const USER_DATA_STORE = 'user-data';

class IndexedDBService {
  private db: IDBDatabase | null = null;

  async init(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(DB_NAME, DB_VERSION);

      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        this.db = request.result;
        resolve();
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;

        if (!db.objectStoreNames.contains(PROCESSING_STORE)) {
          const processingStore = db.createObjectStore(PROCESSING_STORE, {
            keyPath: 'id',
            autoIncrement: true
          });
          processingStore.createIndex('timestamp', 'timestamp', { unique: false });
        }

        if (!db.objectStoreNames.contains(USER_DATA_STORE)) {
          db.createObjectStore(USER_DATA_STORE, {
            keyPath: 'key'
          });
        }
      };
    });
  }

  async saveProcessingHistory(data: any): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    const transaction = this.db.transaction([PROCESSING_STORE], 'readwrite');
    const store = transaction.objectStore(PROCESSING_STORE);
    
    await store.add({
      ...data,
      timestamp: Date.now()
    });
  }

  async getProcessingHistory(): Promise<any[]> {
    if (!this.db) throw new Error('Database not initialized');

    const transaction = this.db.transaction([PROCESSING_STORE], 'readonly');
    const store = transaction.objectStore(PROCESSING_STORE);
    
    return new Promise((resolve, reject) => {
      const request = store.getAll();
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }

  async saveUserData(key: string, value: any): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    const transaction = this.db.transaction([USER_DATA_STORE], 'readwrite');
    const store = transaction.objectStore(USER_DATA_STORE);
    
    await store.put({ key, value });
  }

  async getUserData(key: string): Promise<any> {
    if (!this.db) throw new Error('Database not initialized');

    const transaction = this.db.transaction([USER_DATA_STORE], 'readonly');
    const store = transaction.objectStore(USER_DATA_STORE);
    
    return new Promise((resolve, reject) => {
      const request = store.get(key);
      request.onsuccess = () => resolve(request.result?.value);
      request.onerror = () => reject(request.error);
    });
  }
}

export const dbService = new IndexedDBService();
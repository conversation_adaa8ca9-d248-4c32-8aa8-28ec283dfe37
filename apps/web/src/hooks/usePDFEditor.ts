import { useEffect, useState, useCallback } from 'react';
import { pdfEditorService, type PDFDocument, type RenderOptions } from '../services/pdf-editor-service';
import { usePDFEditorStore } from '../stores/pdf-editor';

interface UsePDFEditorReturn {
  // Service state
  isServiceReady: boolean;
  isInitializing: boolean;
  initializationError: string | null;
  
  // Document operations
  loadDocument: (file: File) => Promise<void>;
  renderPage: (options: RenderOptions) => Promise<ImageData | null>;
  saveDocument: () => Promise<Uint8Array | null>;
  closeDocument: () => void;
  
  // Text operations
  insertText: (options: {
    text: string;
    position: { x: number; y: number };
    style: any;
    page: number;
  }) => Promise<void>;
  
  // Annotation operations
  addAnnotation: (options: {
    type: 'highlight' | 'comment' | 'stamp' | 'drawing';
    position: { x: number; y: number; width?: number; height?: number };
    color: string;
    opacity: number;
    content?: string;
    page: number;
  }) => Promise<void>;
  
  // OCR operations
  performOCR: (pageNum: number, language?: string) => Promise<string>;
  
  // Format conversion
  convertToFormat: (format: 'docx' | 'png' | 'jpg' | 'tiff') => Promise<Uint8Array | null>;
  
  // Document info
  currentDocument: PDFDocument | null;
  pageCount: number;
  memoryUsage: any;
}

export const usePDFEditor = (): UsePDFEditorReturn => {
  const [isServiceReady, setIsServiceReady] = useState(false);
  const [isInitializing, setIsInitializing] = useState(false);
  const [initializationError, setInitializationError] = useState<string | null>(null);
  const [currentDocument, setCurrentDocument] = useState<PDFDocument | null>(null);
  
  const {
    setProcessing,
    markDirty,
    setProcessedDocument
  } = usePDFEditorStore();

  // Initialize service
  useEffect(() => {
    const initializeService = async () => {
      if (isServiceReady || isInitializing) return;
      
      setIsInitializing(true);
      setInitializationError(null);
      
      try {
        await pdfEditorService.initialize();
        setIsServiceReady(true);
        console.log('PDF Editor service initialized successfully');
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        setInitializationError(errorMessage);
        console.error('Failed to initialize PDF Editor service:', error);
      } finally {
        setIsInitializing(false);
      }
    };

    initializeService();
  }, [isServiceReady, isInitializing]);

  // Load document
  const loadDocument = useCallback(async (file: File) => {
    if (!isServiceReady) {
      throw new Error('PDF Editor service not ready');
    }

    setProcessing(true, 0, 'Loading document...');
    
    try {
      const document = await pdfEditorService.loadDocument(file);
      setCurrentDocument(document);
      
      // Update store with document info
      setProcessedDocument({
        data: new Uint8Array(document.data),
        originalSize: document.data.byteLength,
        processedSize: document.data.byteLength,
        compressionRatio: 1.0,
        processingTimeMs: 0,
        metadata: {
          pages: document.pageCount,
          title: document.title,
          author: document.author
        }
      });
      
      setProcessing(false);
      console.log('Document loaded successfully:', document);
    } catch (error) {
      setProcessing(false);
      console.error('Failed to load document:', error);
      throw error;
    }
  }, [isServiceReady, setProcessing, setProcessedDocument]);

  // Render page
  const renderPage = useCallback(async (options: RenderOptions): Promise<ImageData | null> => {
    if (!isServiceReady || !currentDocument) {
      return null;
    }

    try {
      return await pdfEditorService.renderPage(options);
    } catch (error) {
      console.error('Failed to render page:', error);
      return null;
    }
  }, [isServiceReady, currentDocument]);

  // Insert text
  const insertText = useCallback(async (options: {
    text: string;
    position: { x: number; y: number };
    style: any;
    page: number;
  }) => {
    if (!isServiceReady || !currentDocument) {
      throw new Error('No document loaded');
    }

    try {
      await pdfEditorService.insertText(options);
      markDirty();
      console.log('Text inserted successfully');
    } catch (error) {
      console.error('Failed to insert text:', error);
      throw error;
    }
  }, [isServiceReady, currentDocument, markDirty]);

  // Add annotation
  const addAnnotation = useCallback(async (options: {
    type: 'highlight' | 'comment' | 'stamp' | 'drawing';
    position: { x: number; y: number; width?: number; height?: number };
    color: string;
    opacity: number;
    content?: string;
    page: number;
  }) => {
    if (!isServiceReady || !currentDocument) {
      throw new Error('No document loaded');
    }

    try {
      await pdfEditorService.addAnnotation(options);
      markDirty();
      console.log('Annotation added successfully');
    } catch (error) {
      console.error('Failed to add annotation:', error);
      throw error;
    }
  }, [isServiceReady, currentDocument, markDirty]);

  // Perform OCR
  const performOCR = useCallback(async (pageNum: number, language: string = 'eng'): Promise<string> => {
    if (!isServiceReady || !currentDocument) {
      throw new Error('No document loaded');
    }

    setProcessing(true, 0, `Performing OCR on page ${pageNum}...`);
    
    try {
      const result = await pdfEditorService.performOCR(pageNum, language);
      setProcessing(false);
      console.log('OCR completed successfully');
      return result;
    } catch (error) {
      setProcessing(false);
      console.error('Failed to perform OCR:', error);
      throw error;
    }
  }, [isServiceReady, currentDocument, setProcessing]);

  // Convert to format
  const convertToFormat = useCallback(async (format: 'docx' | 'png' | 'jpg' | 'tiff'): Promise<Uint8Array | null> => {
    if (!isServiceReady || !currentDocument) {
      return null;
    }

    setProcessing(true, 0, `Converting to ${format.toUpperCase()}...`);
    
    try {
      const result = await pdfEditorService.convertToFormat(format);
      setProcessing(false);
      console.log(`Conversion to ${format} completed successfully`);
      return result;
    } catch (error) {
      setProcessing(false);
      console.error(`Failed to convert to ${format}:`, error);
      throw error;
    }
  }, [isServiceReady, currentDocument, setProcessing]);

  // Save document
  const saveDocument = useCallback(async (): Promise<Uint8Array | null> => {
    if (!isServiceReady || !currentDocument) {
      return null;
    }

    setProcessing(true, 0, 'Saving document...');
    
    try {
      const result = await pdfEditorService.saveDocument();
      setProcessing(false);
      console.log('Document saved successfully');
      return result;
    } catch (error) {
      setProcessing(false);
      console.error('Failed to save document:', error);
      throw error;
    }
  }, [isServiceReady, currentDocument, setProcessing]);

  // Close document
  const closeDocument = useCallback(() => {
    pdfEditorService.closeDocument();
    setCurrentDocument(null);
    console.log('Document closed');
  }, []);

  // Get page count
  const pageCount = isServiceReady ? pdfEditorService.getPageCount() : 0;

  // Get memory usage
  const memoryUsage = isServiceReady ? pdfEditorService.getMemoryUsage() : null;

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (currentDocument) {
        closeDocument();
      }
    };
  }, [currentDocument, closeDocument]);

  return {
    // Service state
    isServiceReady,
    isInitializing,
    initializationError,
    
    // Document operations
    loadDocument,
    renderPage,
    saveDocument,
    closeDocument,
    
    // Text operations
    insertText,
    
    // Annotation operations
    addAnnotation,
    
    // OCR operations
    performOCR,
    
    // Format conversion
    convertToFormat,
    
    // Document info
    currentDocument,
    pageCount,
    memoryUsage
  };
};
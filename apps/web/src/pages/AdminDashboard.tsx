import React from 'react';
import { SessionManager, AdminRoute } from '../components/admin';

export const AdminDashboard: React.FC = () => {
  return (
    <AdminRoute>
      <div className="min-h-screen bg-gray-50">
        <div className="container mx-auto py-8">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
            <p className="text-gray-600 mt-2">Manage users, sessions, and system settings</p>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Quick Stats */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h2>
                <div className="space-y-3">
                  <button className="w-full text-left px-4 py-2 bg-blue-50 hover:bg-blue-100 rounded-md text-blue-700 transition-colors">
                    View All Users
                  </button>
                  <button className="w-full text-left px-4 py-2 bg-green-50 hover:bg-green-100 rounded-md text-green-700 transition-colors">
                    System Settings
                  </button>
                  <button className="w-full text-left px-4 py-2 bg-purple-50 hover:bg-purple-100 rounded-md text-purple-700 transition-colors">
                    Analytics Report
                  </button>
                </div>
              </div>
            </div>
            
            {/* Session Manager */}
            <div className="lg:col-span-2">
              <SessionManager className="bg-white rounded-lg shadow" />
            </div>
          </div>
        </div>
      </div>
    </AdminRoute>
  );
};
import React from 'react';

export const Dashboard: React.FC = () => {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900 sm:text-3xl">
          Dashboard
        </h1>
        <p className="mt-2 text-gray-600">
          Welcome to MobilePDF Pro. Manage your PDF processing workflows.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900">Recent Activity</h3>
          <p className="mt-2 text-gray-600">No recent activity</p>
        </div>
        
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900">Processing Stats</h3>
          <p className="mt-2 text-gray-600">0 documents processed</p>
        </div>
        
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900">Storage Used</h3>
          <p className="mt-2 text-gray-600">0 MB / 100 MB</p>
        </div>
      </div>
    </div>
  );
};
import React, { useState, useEffect } from 'react';
import { FileDropZone } from '../components/ui/FileDropZone';
import { ProcessingProgress } from '../components/ui/ProcessingProgress';
import { PDFEditorLayoutEnhanced } from '../components/pdf-editor';
import { usePDFEditorStore } from '../stores/pdf-editor';
import { usePDFEditor } from '../hooks/usePDFEditor';
import { pdfProcessor, type ProcessingOptions, type ProcessedResult, type ProcessingProgress as ProcessingProgressType } from '../services/pdf-processor';

export const Process: React.FC = () => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingProgress, setProcessingProgress] = useState<ProcessingProgressType | null>(null);
  const [processedResult, setProcessedResult] = useState<ProcessedResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [showEditor, setShowEditor] = useState(false);
  const [processingOptions, setProcessingOptions] = useState<ProcessingOptions>({
    qualityLevel: 0.8,
    preserveMetadata: true,
    compressionType: 'adaptive'
  });

  // PDF Editor store and service
  const { setDocument, setProcessedDocument, resetEditor } = usePDFEditorStore();
  const { 
    isServiceReady, 
    isInitializing, 
    initializationError,
    loadDocument: loadEditorDocument,
    closeDocument: closeEditorDocument 
  } = usePDFEditor();

  const handleFileSelect = async (file: File) => {
    setSelectedFile(file);
    setError(null);
    setProcessedResult(null);
    setShowEditor(false);
    
    // Reset PDF editor state
    resetEditor();
    setDocument(file);
  };

  const handleValidationError = (errorMessage: string) => {
    setError(errorMessage);
    setSelectedFile(null);
  };

  const handleProcessFile = async () => {
    if (!selectedFile) return;

    setIsProcessing(true);
    setError(null);
    setProcessingProgress(null);

    try {
      const result = await pdfProcessor.processDocument(
        selectedFile,
        processingOptions,
        (progress) => {
          setProcessingProgress(progress);
        }
      );

      setProcessedResult(result);
      
      // Update PDF editor with processed document
      setProcessedDocument(result);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Processing failed');
    } finally {
      setIsProcessing(false);
      setProcessingProgress(null);
    }
  };

  const handleDownload = () => {
    if (!processedResult || !selectedFile) return;

    const blob = new Blob([processedResult.data], { type: 'application/pdf' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `processed_${selectedFile.name}`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const handleReset = () => {
    setSelectedFile(null);
    setProcessedResult(null);
    setError(null);
    setProcessingProgress(null);
    setShowEditor(false);
    closeEditorDocument(); // Close editor service document
    resetEditor();
  };

  const handleOpenEditor = async () => {
    if (!selectedFile) return;
    
    try {
      // First set up the store state
      setDocument(selectedFile);
      if (processedResult) {
        setProcessedDocument(processedResult);
      }
      
      // Then load the document into the PDF editor service
      if (isServiceReady) {
        await loadEditorDocument(selectedFile);
      }
      
      setShowEditor(true);
    } catch (error) {
      console.error('Failed to open editor:', error);
      setError(error instanceof Error ? error.message : 'Failed to open editor');
    }
  };

  const formatFileSize = (bytes: number): string => {
    const mb = bytes / (1024 * 1024);
    return `${mb.toFixed(2)} MB`;
  };

  // Show PDF Editor if file is selected and editor is enabled
  if (showEditor && selectedFile) {
    return (
      <div className="h-screen flex flex-col">
        {/* Editor Header */}
        <div className="bg-white border-b border-gray-200 px-4 py-3 flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => setShowEditor(false)}
              className="text-gray-600 hover:text-gray-900 font-medium text-sm"
            >
              ← Back to Processing
            </button>
            <div>
              <h1 className="text-lg font-semibold text-gray-900">PDF Editor</h1>
              <p className="text-sm text-gray-500">{selectedFile.name}</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={handleReset}
              className="px-3 py-1 text-sm text-gray-600 hover:text-gray-900 border border-gray-300 rounded"
            >
              Close
            </button>
          </div>
        </div>
        
        {/* PDF Editor */}
        <div className="flex-1 overflow-hidden">
          {isInitializing ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p className="text-gray-600">Initializing PDF editor...</p>
              </div>
            </div>
          ) : initializationError ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center max-w-md">
                <div className="text-red-500 mb-2 text-2xl">⚠️</div>
                <p className="text-red-600 mb-4">PDF Editor initialization failed: {initializationError}</p>
                <button
                  onClick={() => window.location.reload()}
                  className="px-4 py-2 text-sm bg-blue-600 text-white rounded hover:bg-blue-700"
                >
                  Reload Page
                </button>
              </div>
            </div>
          ) : (
            <PDFEditorLayoutEnhanced />
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900 sm:text-3xl">
          Process PDFs
        </h1>
        <p className="mt-2 text-gray-600">
          Upload and process your PDF documents with advanced editing tools.
        </p>
      </div>

      {/* File Upload Section */}
      {!selectedFile && !processedResult && (
        <div className="bg-white rounded-lg shadow">
          <FileDropZone
            onFileSelect={handleFileSelect}
            onValidationError={handleValidationError}
            className="p-8"
          />
          {error && (
            <div className="mx-8 mb-8 p-4 bg-red-50 border border-red-200 rounded-md">
              <p className="text-sm text-red-600">{error}</p>
            </div>
          )}
        </div>
      )}

      {/* Processing Options */}
      {selectedFile && !isProcessing && !processedResult && (
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Processing Options</h3>
          
          {error && (
            <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-md">
              <p className="text-sm text-red-600">{error}</p>
            </div>
          )}
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Quality Level: {(processingOptions.qualityLevel! * 100).toFixed(0)}%
              </label>
              <input
                type="range"
                min="0.1"
                max="1"
                step="0.1"
                value={processingOptions.qualityLevel}
                onChange={(e) => setProcessingOptions(prev => ({
                  ...prev,
                  qualityLevel: parseFloat(e.target.value)
                }))}
                className="w-full"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Compression Type
              </label>
              <select
                value={processingOptions.compressionType}
                onChange={(e) => setProcessingOptions(prev => ({
                  ...prev,
                  compressionType: e.target.value as 'lossless' | 'lossy' | 'adaptive'
                }))}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="adaptive">Adaptive (Recommended)</option>
                <option value="lossless">Lossless</option>
                <option value="lossy">Lossy</option>
              </select>
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="preserveMetadata"
                checked={processingOptions.preserveMetadata}
                onChange={(e) => setProcessingOptions(prev => ({
                  ...prev,
                  preserveMetadata: e.target.checked
                }))}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="preserveMetadata" className="ml-2 block text-sm text-gray-700">
                Preserve metadata
              </label>
            </div>
          </div>

          <div className="mt-6 pt-4 border-t border-gray-200">
            <div className="flex items-center justify-between mb-4">
              <div>
                <p className="text-sm font-medium text-gray-700">{selectedFile.name}</p>
                <p className="text-sm text-gray-500">Size: {formatFileSize(selectedFile.size)}</p>
              </div>
              <button
                onClick={handleReset}
                className="text-sm text-gray-500 hover:text-gray-700"
              >
                Choose different file
              </button>
            </div>

            <div className="flex space-x-3">
              <button
                onClick={handleProcessFile}
                className="flex-1 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 touch-target"
              >
                Process PDF
              </button>
              <button
                onClick={handleOpenEditor}
                disabled={isInitializing || initializationError !== null}
                className={`flex-1 px-4 py-2 rounded-md focus:outline-none focus:ring-2 touch-target ${
                  isInitializing || initializationError !== null
                    ? 'bg-gray-400 text-gray-700 cursor-not-allowed'
                    : 'bg-green-600 text-white hover:bg-green-700 focus:ring-green-500'
                }`}
              >
                {isInitializing ? 'Loading Editor...' : 'Open Editor'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Processing Progress */}
      {isProcessing && (
        <div className="bg-white rounded-lg shadow p-6">
          {processingProgress ? (
            <ProcessingProgress
              progress={processingProgress}
              fileName={selectedFile?.name || ''}
            />
          ) : (
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Initializing PDF processing...</p>
            </div>
          )}
        </div>
      )}

      {/* Results */}
      {processedResult && selectedFile && (
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Processing Complete</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div className="space-y-2">
              <p className="text-sm font-medium text-gray-700">Original Size</p>
              <p className="text-lg">{formatFileSize(processedResult.originalSize)}</p>
            </div>
            <div className="space-y-2">
              <p className="text-sm font-medium text-gray-700">Processed Size</p>
              <p className="text-lg text-green-600">{formatFileSize(processedResult.processedSize)}</p>
            </div>
            <div className="space-y-2">
              <p className="text-sm font-medium text-gray-700">Compression Ratio</p>
              <p className="text-lg">{(processedResult.compressionRatio * 100).toFixed(1)}%</p>
            </div>
            <div className="space-y-2">
              <p className="text-sm font-medium text-gray-700">Processing Time</p>
              <p className="text-lg">{(processedResult.processingTimeMs / 1000).toFixed(1)}s</p>
            </div>
          </div>

          {processedResult.metadata && (
            <div className="mb-6 p-4 bg-gray-50 rounded-md">
              <h4 className="text-sm font-medium text-gray-700 mb-2">Document Info</h4>
              <div className="text-sm text-gray-600 space-y-1">
                <p>Pages: {processedResult.metadata.pages}</p>
                {processedResult.metadata.title && <p>Title: {processedResult.metadata.title}</p>}
                {processedResult.metadata.author && <p>Author: {processedResult.metadata.author}</p>}
              </div>
            </div>
          )}

          <div className="flex space-x-3">
            <button
              onClick={handleDownload}
              className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 touch-target"
            >
              Download Processed PDF
            </button>
            <button
              onClick={handleOpenEditor}
              disabled={isInitializing || initializationError !== null}
              className={`px-4 py-2 rounded-md focus:outline-none focus:ring-2 touch-target ${
                isInitializing || initializationError !== null
                  ? 'bg-gray-400 text-gray-700 cursor-not-allowed'
                  : 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500'
              }`}
            >
              {isInitializing ? 'Loading Editor...' : 'Open in Editor'}
            </button>
            <button
              onClick={handleReset}
              className="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 touch-target"
            >
              Process Another File
            </button>
          </div>
        </div>
      )}
    </div>
  );
};
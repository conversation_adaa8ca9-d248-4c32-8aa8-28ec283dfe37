import { useState } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { BaseLayout } from './components/layout/BaseLayout';
import { Header } from './components/layout/Header';
import { Sidebar } from './components/layout/Sidebar';
import { MainContent } from './components/layout/MainContent';
import { Dashboard } from './pages/Dashboard';
import { Process } from './pages/Process';
import { History } from './pages/History';
import { Settings } from './pages/Settings';

function App() {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);

  const handleMenuToggle = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  const handleSidebarClose = () => {
    setIsSidebarOpen(false);
  };

  return (
    <BaseLayout>
      <Header 
        onMenuToggle={handleMenuToggle}
        isMenuOpen={isSidebarOpen}
      />
      <Sidebar 
        isOpen={isSidebarOpen}
        onClose={handleSidebarClose}
      />
      <MainContent>
        <Routes>
          <Route path="/" element={<Navigate to="/dashboard" replace />} />
          <Route path="/dashboard" element={<Dashboard />} />
          <Route path="/process" element={<Process />} />
          <Route path="/history" element={<History />} />
          <Route path="/settings" element={<Settings />} />
        </Routes>
      </MainContent>
    </BaseLayout>
  );
}

export default App;
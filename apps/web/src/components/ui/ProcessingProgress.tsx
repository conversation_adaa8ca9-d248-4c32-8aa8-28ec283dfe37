import React from 'react';
import { clsx } from 'clsx';

interface ProcessingProgressProps {
  stage: 'loading' | 'processing' | 'compressing' | 'finalizing';
  percentage: number;
  currentStep: string;
  estimatedTimeRemaining?: number;
  className?: string;
  showCancel?: boolean;
  onCancel?: () => void;
}

const stageLabels = {
  loading: 'Loading',
  processing: 'Processing',
  compressing: 'Compressing',
  finalizing: 'Finalizing'
};

const stageIcons = {
  loading: (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 12l2 2 4-4" />
    </svg>
  ),
  processing: (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
    </svg>
  ),
  compressing: (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
    </svg>
  ),
  finalizing: (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
  )
};

export const ProcessingProgress: React.FC<ProcessingProgressProps> = ({
  stage,
  percentage,
  currentStep,
  estimatedTimeRemaining,
  className,
  showCancel = false,
  onCancel
}) => {
  const formatTime = (seconds: number): string => {
    if (seconds < 60) {
      return `${Math.round(seconds)}s`;
    }
    
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.round(seconds % 60);
    
    return `${minutes}m ${remainingSeconds}s`;
  };

  const getStageColor = (currentStage: string, targetStage: string): string => {
    const stageOrder = ['loading', 'processing', 'compressing', 'finalizing'];
    const currentIndex = stageOrder.indexOf(currentStage);
    const targetIndex = stageOrder.indexOf(targetStage);

    if (currentIndex > targetIndex) {
      return 'text-green-600 bg-green-100'; // Completed
    } else if (currentIndex === targetIndex) {
      return 'text-blue-600 bg-blue-100'; // Current
    } else {
      return 'text-gray-400 bg-gray-100'; // Pending
    }
  };

  return (
    <div className={clsx('bg-white rounded-lg border shadow-sm p-6', className)}>
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-gray-900">
          Processing PDF
        </h3>
        
        {showCancel && onCancel && (
          <button
            onClick={onCancel}
            className="text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 rounded-full p-1"
            aria-label="Cancel processing"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        )}
      </div>

      {/* Progress Bar */}
      <div className="mb-6">
        <div className="flex justify-between items-center mb-2">
          <span className="text-sm font-medium text-gray-900">
            {Math.round(percentage)}% Complete
          </span>
          {estimatedTimeRemaining && estimatedTimeRemaining > 0 && (
            <span className="text-sm text-gray-500">
              ~{formatTime(estimatedTimeRemaining)} remaining
            </span>
          )}
        </div>
        
        <div className="w-full bg-gray-200 rounded-full h-2.5">
          <div
            className="bg-blue-600 h-2.5 rounded-full transition-all duration-300 ease-out"
            style={{ width: `${Math.min(100, Math.max(0, percentage))}%` }}
          />
        </div>
      </div>

      {/* Current Step */}
      <div className="mb-6">
        <div className="flex items-center text-sm text-gray-700">
          <div className="animate-spin mr-3">
            <svg className="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
          </div>
          <span className="font-medium">{currentStep}</span>
        </div>
      </div>

      {/* Stage Indicators */}
      <div className="grid grid-cols-2 sm:grid-cols-4 gap-3">
        {Object.entries(stageLabels).map(([stageKey, label]) => (
          <div
            key={stageKey}
            className={clsx(
              'flex flex-col items-center p-3 rounded-lg text-center',
              getStageColor(stage, stageKey)
            )}
          >
            <div className="mb-2">
              {stageIcons[stageKey as keyof typeof stageIcons]}
            </div>
            <span className="text-xs font-medium">
              {label}
            </span>
          </div>
        ))}
      </div>

      {/* Mobile-optimized layout */}
      <div className="mt-4 sm:hidden">
        <div className="text-center text-sm text-gray-600">
          Step {Object.keys(stageLabels).indexOf(stage) + 1} of {Object.keys(stageLabels).length}
        </div>
      </div>
    </div>
  );
};
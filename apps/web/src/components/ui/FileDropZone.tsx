import React, { useCallback, useState, useRef } from 'react';
import { clsx } from 'clsx';
import { memoryManager } from '../../utils/memory-manager';

interface FileDropZoneProps {
  onFileSelect: (file: File) => void;
  onValidationError: (error: string) => void;
  maxSize?: number; // in bytes, default 4GB
  acceptedTypes?: string[];
  disabled?: boolean;
  className?: string;
  children?: React.ReactNode;
}

export const FileDropZone: React.FC<FileDropZoneProps> = ({
  onFileSelect,
  onValidationError,
  maxSize = 4 * 1024 * 1024 * 1024, // 4GB default
  acceptedTypes = ['application/pdf'],
  disabled = false,
  className,
  children
}) => {
  const [isDragOver, setIsDragOver] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const validateFile = useCallback((file: File): boolean => {
    // Check file size
    if (file.size > maxSize) {
      const maxSizeMB = Math.round(maxSize / (1024 * 1024));
      onValidationError(`File size exceeds ${maxSizeMB}MB limit`);
      return false;
    }

    // Check file type
    if (!acceptedTypes.includes(file.type) && !file.name.toLowerCase().endsWith('.pdf')) {
      onValidationError('Please select a valid PDF file');
      return false;
    }

    // Check if file is empty
    if (file.size === 0) {
      onValidationError('File appears to be empty');
      return false;
    }

    // Memory-aware validation
    const safetyCheck = memoryManager.isFileSafeToProcess(file.size);
    if (!safetyCheck.safe) {
      const recommendedSizeMB = safetyCheck.recommendedMaxSize 
        ? Math.round(safetyCheck.recommendedMaxSize / (1024 * 1024))
        : 100;
      onValidationError(
        `${safetyCheck.reason} Recommended max size: ${recommendedSizeMB}MB`
      );
      return false;
    }

    return true;
  }, [maxSize, acceptedTypes, onValidationError]);

  const handleFileSelection = useCallback(async (file: File) => {
    if (!validateFile(file)) {
      return;
    }

    setIsProcessing(true);
    
    try {
      // Basic PDF validation by attempting to read first few bytes
      const firstBytes = await file.slice(0, 4).arrayBuffer();
      const header = new Uint8Array(firstBytes);
      const pdfMagic = [0x25, 0x50, 0x44, 0x46]; // %PDF
      
      const isValidPDF = pdfMagic.every((byte, index) => header[index] === byte);
      
      if (!isValidPDF) {
        onValidationError('Selected file is not a valid PDF document');
        return;
      }

      onFileSelect(file);
    } catch (error) {
      onValidationError('Error reading file. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  }, [validateFile, onFileSelect, onValidationError]);

  const handleDrop = useCallback(async (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);

    if (disabled || isProcessing) return;

    const files = Array.from(e.dataTransfer.files);
    if (files.length === 0) return;

    const file = files[0];
    await handleFileSelection(file);
  }, [disabled, isProcessing, handleFileSelection]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    if (!disabled && !isProcessing) {
      setIsDragOver(true);
    }
  }, [disabled, isProcessing]);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    // Only set to false if we're leaving the drop zone entirely
    if (!e.currentTarget.contains(e.relatedTarget as Node)) {
      setIsDragOver(false);
    }
  }, []);

  const handleFileInputChange = useCallback(async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    const file = files[0];
    await handleFileSelection(file);

    // Reset input value so same file can be selected again
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, [handleFileSelection]);

  const handleClick = useCallback(() => {
    if (!disabled && !isProcessing && fileInputRef.current) {
      fileInputRef.current.click();
    }
  }, [disabled, isProcessing]);

  const handleCameraCapture = useCallback(() => {
    if (!disabled && !isProcessing && fileInputRef.current) {
      // For mobile devices, this will open camera when capture="environment" is set
      fileInputRef.current.click();
    }
  }, [disabled, isProcessing]);

  // Fixed button click handler to prevent double dialog while maintaining user gesture
  const handleButtonClick = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation(); // Stop the click from bubbling to parent drop zone
    if (!disabled && !isProcessing && fileInputRef.current) {
      fileInputRef.current.click();
    }
  }, [disabled, isProcessing]);

  return (
    <div
      className={clsx(
        'relative border-2 border-dashed rounded-lg transition-all duration-200 ease-in-out',
        'focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-blue-500',
        {
          'border-gray-300 hover:border-gray-400': !isDragOver && !disabled,
          'border-blue-500 bg-blue-50': isDragOver && !disabled,
          'border-gray-200 bg-gray-50 cursor-not-allowed': disabled,
          'cursor-pointer': !disabled && !isProcessing,
          'animate-pulse': isProcessing
        },
        className
      )}
      onDrop={handleDrop}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onClick={handleClick}
      role="button"
      tabIndex={0}
      aria-label="File drop zone"
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          handleClick();
        }
      }}
    >
      <input
        ref={fileInputRef}
        type="file"
        accept=".pdf,application/pdf"
        onChange={handleFileInputChange}
        className="hidden"
        disabled={disabled || isProcessing}
        capture="environment" // For mobile camera integration
      />

      {children || (
        <div className="flex flex-col items-center justify-center py-12 px-6 text-center">
          {isProcessing ? (
            <>
              <div className="mb-4">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
              </div>
              <p className="text-sm text-gray-600 mb-2">Validating file...</p>
            </>
          ) : (
            <>
              <div className="mb-4">
                <svg
                  className="mx-auto h-12 w-12 text-gray-400"
                  stroke="currentColor"
                  fill="none"
                  viewBox="0 0 48 48"
                  aria-hidden="true"
                >
                  <path
                    d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                    strokeWidth={2}
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </div>

              <p className="text-lg font-medium text-gray-900 mb-2">
                {isDragOver ? 'Drop your PDF here' : 'Select or drop a PDF file'}
              </p>
              
              <p className="text-sm text-gray-600 mb-4">
                Up to {Math.round(maxSize / (1024 * 1024))}MB
              </p>

              <div className="flex flex-col sm:flex-row gap-2">
                <button
                  type="button"
                  className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                  disabled={disabled || isProcessing}
                  onClick={handleButtonClick}
                >
                  <svg className="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                  </svg>
                  Choose File
                </button>

                {/* Mobile camera button - only show on mobile devices */}
                <button
                  type="button"
                  className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed sm:hidden"
                  disabled={disabled || isProcessing}
                  onClick={handleButtonClick}
                >
                  <svg className="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0118.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                  Scan Document
                </button>
              </div>
            </>
          )}
        </div>
      )}
    </div>
  );
};
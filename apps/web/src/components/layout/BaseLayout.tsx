import React from 'react';
import { cn } from 'shared/utils';

interface BaseLayoutProps {
  children: React.ReactNode;
  className?: string;
}

export const BaseLayout: React.FC<BaseLayoutProps> = ({ 
  children, 
  className 
}) => {
  return (
    <div className={cn(
      "min-h-screen bg-gray-50",
      "grid grid-cols-1 lg:grid-cols-[250px_1fr]",
      "grid-rows-[auto_1fr] lg:grid-rows-1",
      className
    )}>
      {children}
    </div>
  );
};
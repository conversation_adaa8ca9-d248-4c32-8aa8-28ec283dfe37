import React from 'react';
import { cn } from 'shared/utils';

interface HeaderProps {
  onMenuToggle: () => void;
  isMenuOpen: boolean;
}

export const Header: React.FC<HeaderProps> = ({ 
  onMenuToggle, 
  isMenuOpen 
}) => {
  return (
    <header className={cn(
      "bg-white border-b border-gray-200",
      "px-4 sm:px-6 lg:px-8",
      "h-16 flex items-center justify-between",
      "lg:col-span-2"
    )}>
      <div className="flex items-center space-x-4">
        <button
          onClick={onMenuToggle}
          className={cn(
            "touch-target lg:hidden",
            "rounded-md p-2 text-gray-600 hover:text-gray-900",
            "hover:bg-gray-100 focus:outline-none focus:ring-2",
            "focus:ring-inset focus:ring-blue-500"
          )}
          aria-label="Open menu"
        >
          <svg
            className="h-6 w-6"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            {isMenuOpen ? (
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            ) : (
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 6h16M4 12h16M4 18h16"
              />
            )}
          </svg>
        </button>
        
        <div className="flex items-center">
          <img
            className="h-8 w-8"
            src="/icon-192x192.png"
            alt="MobilePDF Pro"
          />
          <h1 className="ml-2 text-xl font-semibold text-gray-900 hidden sm:block">
            MobilePDF Pro
          </h1>
        </div>
      </div>

      <div className="flex items-center space-x-4">
        <button
          className={cn(
            "touch-target",
            "rounded-full p-2 text-gray-600 hover:text-gray-900",
            "hover:bg-gray-100 focus:outline-none focus:ring-2",
            "focus:ring-inset focus:ring-blue-500"
          )}
          aria-label="User menu"
        >
          <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
          </svg>
        </button>
      </div>
    </header>
  );
};
import React from 'react';
import { cn } from 'shared/utils';

interface MainContentProps {
  children: React.ReactNode;
  className?: string;
}

export const MainContent: React.FC<MainContentProps> = ({ 
  children, 
  className 
}) => {
  return (
    <main className={cn(
      "flex-1 overflow-auto",
      "lg:col-start-2 lg:row-start-2",
      className
    )}>
      <div className="container-mobile py-6">
        {children}
      </div>
    </main>
  );
};
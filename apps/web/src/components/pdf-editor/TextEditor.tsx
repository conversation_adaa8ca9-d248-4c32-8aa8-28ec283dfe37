import React, { useState } from 'react';
import { Switch } from '@headlessui/react';
import { 
  CursorArrowRaysIcon,
  PencilIcon,
  TrashIcon,
  PaintBrushIcon 
} from '@heroicons/react/24/outline';
import { usePDFEditorStore } from '../../stores/pdf-editor';

export const TextEditor: React.FC = () => {
  const { activeTool, setActiveTool } = usePDFEditorStore();
  const [textOptions, setTextOptions] = useState({
    fontSize: 12,
    fontFamily: 'Arial',
    color: '#000000',
    bold: false,
    italic: false,
    underline: false
  });

  const tools = [
    { id: 'select', name: 'Select', icon: CursorArrowRaysIcon },
    { id: 'text', name: 'Add Text', icon: PencilIcon },
    { id: 'delete', name: 'Delete Text', icon: TrashIcon },
    { id: 'format', name: 'Format Text', icon: PaintBrushIcon }
  ];

  const fontFamilies = [
    'Arial',
    'Helvetica',
    'Times New Roman',
    'Courier New',
    'Georgia',
    'Verdana'
  ];

  const fontSizes = [8, 9, 10, 11, 12, 14, 16, 18, 20, 24, 28, 32, 36, 48, 72];

  return (
    <div className="space-y-6">
      {/* Tool Selection */}
      <div>
        <h3 className="text-sm font-medium text-gray-900 mb-3">Text Tools</h3>
        <div className="grid grid-cols-2 gap-2">
          {tools.map((tool) => (
            <button
              key={tool.id}
              onClick={() => setActiveTool(tool.id as any)}
              className={`flex items-center space-x-2 p-2 rounded-md border text-sm ${
                activeTool === tool.id
                  ? 'border-blue-500 bg-blue-50 text-blue-700'
                  : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
              }`}
            >
              <tool.icon className="h-4 w-4" />
              <span>{tool.name}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Text Formatting Options */}
      {(activeTool === 'text') && (
        <div className="space-y-4">
          <h3 className="text-sm font-medium text-gray-900">Text Formatting</h3>
          
          {/* Font Family */}
          <div>
            <label className="block text-xs font-medium text-gray-700 mb-1">
              Font Family
            </label>
            <select
              value={textOptions.fontFamily}
              onChange={(e) => setTextOptions(prev => ({ ...prev, fontFamily: e.target.value }))}
              className="w-full text-sm border border-gray-300 rounded-md px-3 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              {fontFamilies.map(font => (
                <option key={font} value={font}>{font}</option>
              ))}
            </select>
          </div>

          {/* Font Size */}
          <div>
            <label className="block text-xs font-medium text-gray-700 mb-1">
              Font Size
            </label>
            <select
              value={textOptions.fontSize}
              onChange={(e) => setTextOptions(prev => ({ ...prev, fontSize: Number(e.target.value) }))}
              className="w-full text-sm border border-gray-300 rounded-md px-3 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              {fontSizes.map(size => (
                <option key={size} value={size}>{size}px</option>
              ))}
            </select>
          </div>

          {/* Text Color */}
          <div>
            <label className="block text-xs font-medium text-gray-700 mb-1">
              Text Color
            </label>
            <div className="flex items-center space-x-2">
              <input
                type="color"
                value={textOptions.color}
                onChange={(e) => setTextOptions(prev => ({ ...prev, color: e.target.value }))}
                className="w-8 h-8 rounded border border-gray-300 cursor-pointer"
              />
              <input
                type="text"
                value={textOptions.color}
                onChange={(e) => setTextOptions(prev => ({ ...prev, color: e.target.value }))}
                className="flex-1 text-sm border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>

          {/* Text Style Options */}
          <div>
            <label className="block text-xs font-medium text-gray-700 mb-2">
              Text Style
            </label>
            <div className="space-y-2">
              <Switch.Group>
                <div className="flex items-center justify-between">
                  <Switch.Label className="text-sm text-gray-700">Bold</Switch.Label>
                  <Switch
                    checked={textOptions.bold}
                    onChange={(checked) => setTextOptions(prev => ({ ...prev, bold: checked }))}
                    className={`${textOptions.bold ? 'bg-blue-600' : 'bg-gray-200'} relative inline-flex h-5 w-9 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2`}
                  >
                    <span className={`${textOptions.bold ? 'translate-x-5' : 'translate-x-1'} inline-block h-3 w-3 transform rounded-full bg-white transition-transform`} />
                  </Switch>
                </div>
              </Switch.Group>

              <Switch.Group>
                <div className="flex items-center justify-between">
                  <Switch.Label className="text-sm text-gray-700">Italic</Switch.Label>
                  <Switch
                    checked={textOptions.italic}
                    onChange={(checked) => setTextOptions(prev => ({ ...prev, italic: checked }))}
                    className={`${textOptions.italic ? 'bg-blue-600' : 'bg-gray-200'} relative inline-flex h-5 w-9 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2`}
                  >
                    <span className={`${textOptions.italic ? 'translate-x-5' : 'translate-x-1'} inline-block h-3 w-3 transform rounded-full bg-white transition-transform`} />
                  </Switch>
                </div>
              </Switch.Group>

              <Switch.Group>
                <div className="flex items-center justify-between">
                  <Switch.Label className="text-sm text-gray-700">Underline</Switch.Label>
                  <Switch
                    checked={textOptions.underline}
                    onChange={(checked) => setTextOptions(prev => ({ ...prev, underline: checked }))}
                    className={`${textOptions.underline ? 'bg-blue-600' : 'bg-gray-200'} relative inline-flex h-5 w-9 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2`}
                  >
                    <span className={`${textOptions.underline ? 'translate-x-5' : 'translate-x-1'} inline-block h-3 w-3 transform rounded-full bg-white transition-transform`} />
                  </Switch>
                </div>
              </Switch.Group>
            </div>
          </div>

          {/* Preview */}
          <div>
            <label className="block text-xs font-medium text-gray-700 mb-1">
              Preview
            </label>
            <div 
              className="w-full border border-gray-300 rounded-md p-3 bg-white"
              style={{
                fontFamily: textOptions.fontFamily,
                fontSize: `${textOptions.fontSize}px`,
                color: textOptions.color,
                fontWeight: textOptions.bold ? 'bold' : 'normal',
                fontStyle: textOptions.italic ? 'italic' : 'normal',
                textDecoration: textOptions.underline ? 'underline' : 'none'
              }}
            >
              Sample text with current formatting
            </div>
          </div>
        </div>
      )}

      {/* Instructions */}
      <div className="text-xs text-gray-500 bg-gray-50 rounded-md p-3">
        <h4 className="font-medium mb-1">How to use:</h4>
        <ul className="space-y-1">
          <li>• Select "Add Text" and click on the PDF to place text</li>
          <li>• Use "Select" to choose existing text for editing</li>
          <li>• "Delete Text" removes selected text elements</li>
          <li>• "Format Text" applies styling to selected text</li>
        </ul>
      </div>
    </div>
  );
};
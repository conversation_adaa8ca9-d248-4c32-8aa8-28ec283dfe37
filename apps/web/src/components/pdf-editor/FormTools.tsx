import React, { useState } from 'react';
import { 
  MagnifyingGlassIcon,
  PlusIcon,
  PencilSquareIcon,
  DocumentTextIcon,
  ArrowDownTrayIcon 
} from '@heroicons/react/24/outline';

export const FormTools: React.FC = () => {
  const [formFields, setFormFields] = useState([
    { id: '1', name: 'First Name', type: 'text', required: true, value: '' },
    { id: '2', name: 'Email', type: 'email', required: true, value: '' },
    { id: '3', name: 'Signature', type: 'signature', required: false, value: '' }
  ]);

  const [newFieldType, setNewFieldType] = useState('text');

  const fieldTypes = [
    { value: 'text', label: 'Text Field' },
    { value: 'email', label: 'Email Field' },
    { value: 'number', label: 'Number Field' },
    { value: 'date', label: 'Date Field' },
    { value: 'checkbox', label: 'Checkbox' },
    { value: 'radio', label: '<PERSON> Button' },
    { value: 'dropdown', label: 'Dropdown' },
    { value: 'signature', label: 'Signature' }
  ];

  const formOperations = [
    { id: 'detect', name: 'Detect Fields', icon: MagnifyingGlassIcon },
    { id: 'create', name: 'Create Field', icon: PlusIcon },
    { id: 'fill', name: 'Fill Forms', icon: PencilSquareIcon },
    { id: 'export', name: 'Export Data', icon: ArrowDownTrayIcon }
  ];

  const handleOperation = (operation: string) => {
    console.log(`Form operation: ${operation}`);
    // TODO: Implement actual form operations
  };

  const handleFieldValueChange = (fieldId: string, value: string) => {
    setFormFields(prev => 
      prev.map(field => 
        field.id === fieldId ? { ...field, value } : field
      )
    );
  };

  return (
    <div className="space-y-6">
      {/* Form Operations */}
      <div>
        <h3 className="text-sm font-medium text-gray-900 mb-3">Form Operations</h3>
        <div className="grid grid-cols-2 gap-2">
          {formOperations.map((operation) => (
            <button
              key={operation.id}
              onClick={() => handleOperation(operation.id)}
              className="flex items-center space-x-2 p-2 rounded-md border border-gray-200 hover:border-gray-300 hover:bg-gray-50 text-sm"
            >
              <operation.icon className="h-4 w-4" />
              <span>{operation.name}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Create New Field */}
      <div>
        <h3 className="text-sm font-medium text-gray-900 mb-3">Create New Field</h3>
        <div className="space-y-2">
          <select
            value={newFieldType}
            onChange={(e) => setNewFieldType(e.target.value)}
            className="w-full text-sm border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            {fieldTypes.map(type => (
              <option key={type.value} value={type.value}>
                {type.label}
              </option>
            ))}
          </select>
          <button
            onClick={() => handleOperation('create')}
            className="w-full bg-blue-600 text-white text-sm py-2 px-3 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            Add {fieldTypes.find(t => t.value === newFieldType)?.label}
          </button>
        </div>
      </div>

      {/* Detected/Existing Form Fields */}
      <div>
        <h3 className="text-sm font-medium text-gray-900 mb-3">
          Form Fields ({formFields.length})
        </h3>
        {formFields.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <DocumentTextIcon className="h-8 w-8 mx-auto mb-2 text-gray-400" />
            <p className="text-sm">No form fields detected</p>
            <button
              onClick={() => handleOperation('detect')}
              className="mt-2 text-blue-600 hover:text-blue-800 text-sm underline"
            >
              Scan for form fields
            </button>
          </div>
        ) : (
          <div className="space-y-3 max-h-64 overflow-y-auto">
            {formFields.map((field) => (
              <div key={field.id} className="border border-gray-200 rounded-md p-3">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium text-gray-900">
                      {field.name}
                    </span>
                    {field.required && (
                      <span className="text-xs text-red-600 bg-red-50 px-1 rounded">
                        Required
                      </span>
                    )}
                  </div>
                  <span className="text-xs text-gray-500 capitalize">
                    {field.type}
                  </span>
                </div>
                
                {/* Field Value Input */}
                {field.type === 'checkbox' ? (
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={field.value === 'true'}
                      onChange={(e) => handleFieldValueChange(field.id, e.target.checked.toString())}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="text-sm text-gray-700">Checked</span>
                  </label>
                ) : field.type === 'signature' ? (
                  <div className="border border-gray-300 rounded-md p-2 bg-gray-50 text-center text-sm text-gray-500">
                    Click to add signature
                  </div>
                ) : (
                  <input
                    type={field.type === 'email' ? 'email' : field.type === 'number' ? 'number' : field.type === 'date' ? 'date' : 'text'}
                    value={field.value}
                    onChange={(e) => handleFieldValueChange(field.id, e.target.value)}
                    placeholder={`Enter ${field.name.toLowerCase()}`}
                    className="w-full text-sm border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                )}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Export Options */}
      {formFields.some(f => f.value) && (
        <div>
          <h3 className="text-sm font-medium text-gray-900 mb-3">Export Form Data</h3>
          <div className="grid grid-cols-3 gap-2">
            {['JSON', 'CSV', 'XML'].map(format => (
              <button
                key={format}
                onClick={() => console.log(`Export as ${format}`)}
                className="text-sm py-2 px-3 border border-gray-300 rounded hover:bg-gray-50"
              >
                {format}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Instructions */}
      <div className="text-xs text-gray-500 bg-gray-50 rounded-md p-3">
        <h4 className="font-medium mb-1">How to use:</h4>
        <ul className="space-y-1">
          <li>• <strong>Detect Fields:</strong> Automatically find form fields in PDF</li>
          <li>• <strong>Create Field:</strong> Add new form fields manually</li>
          <li>• <strong>Fill Forms:</strong> Enter data in detected fields</li>
          <li>• <strong>Export Data:</strong> Save form data in various formats</li>
        </ul>
      </div>
    </div>
  );
};
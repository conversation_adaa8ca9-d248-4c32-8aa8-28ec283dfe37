import React, { useState } from 'react';
import { 
  ArrowsPointingInIcon,
  ChartBarIcon,
  Cog6ToothIcon,
  EyeIcon 
} from '@heroicons/react/24/outline';

export const CompressionTools: React.FC = () => {
  const [compressionStrategy, setCompressionStrategy] = useState('balanced');
  const [advancedOptions, setAdvancedOptions] = useState({
    imageQuality: 85,
    removeMetadata: true,
    optimizeFonts: true,
    compressImages: true,
    targetSize: 0, // 0 means no target
    preserveQuality: false
  });
  const [compressionPreview, setCompressionPreview] = useState({
    originalSize: 5.2, // MB
    estimatedSize: 2.8, // MB
    estimatedRatio: 54,
    processing: false
  });

  const strategies = [
    {
      id: 'quality',
      name: 'Preserve Quality',
      description: 'Minimal compression, maximum quality retention',
      expectedReduction: '10-30%'
    },
    {
      id: 'balanced',
      name: 'Balanced',
      description: 'Good balance between file size and quality',
      expectedReduction: '40-60%'
    },
    {
      id: 'size',
      name: 'Maximum Compression',
      description: 'Aggressive compression for smallest file size',
      expectedReduction: '60-80%'
    }
  ];

  const handlePreviewCompression = () => {
    setCompressionPreview(prev => ({ ...prev, processing: true }));
    
    // Simulate compression preview calculation
    setTimeout(() => {
      let reductionFactor;
      switch (compressionStrategy) {
        case 'quality': reductionFactor = 0.8; break;
        case 'balanced': reductionFactor = 0.5; break;
        case 'size': reductionFactor = 0.3; break;
        default: reductionFactor = 0.5;
      }
      
      // Adjust based on advanced options
      if (!advancedOptions.compressImages) reductionFactor += 0.2;
      if (!advancedOptions.removeMetadata) reductionFactor += 0.05;
      if (!advancedOptions.optimizeFonts) reductionFactor += 0.1;
      
      const estimatedSize = compressionPreview.originalSize * reductionFactor;
      const estimatedRatio = Math.round((1 - reductionFactor) * 100);
      
      setCompressionPreview(prev => ({
        ...prev,
        estimatedSize: Math.max(0.1, estimatedSize),
        estimatedRatio,
        processing: false
      }));
    }, 1000);
  };

  const handleCompress = () => {
    console.log('Starting compression with strategy:', compressionStrategy);
    console.log('Advanced options:', advancedOptions);
  };

  return (
    <div className="space-y-6">
      {/* Compression Strategy */}
      <div>
        <h3 className="text-sm font-medium text-gray-900 mb-3">Compression Strategy</h3>
        <div className="space-y-2">
          {strategies.map((strategy) => (
            <label
              key={strategy.id}
              className={`block p-3 rounded-md border cursor-pointer hover:bg-gray-50 ${
                compressionStrategy === strategy.id
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200'
              }`}
            >
              <div className="flex items-start space-x-3">
                <input
                  type="radio"
                  name="strategy"
                  value={strategy.id}
                  checked={compressionStrategy === strategy.id}
                  onChange={(e) => setCompressionStrategy(e.target.value)}
                  className="mt-1 text-blue-600 focus:ring-blue-500 border-gray-300"
                />
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-900">
                      {strategy.name}
                    </span>
                    <span className="text-xs text-green-600 font-medium">
                      {strategy.expectedReduction}
                    </span>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    {strategy.description}
                  </p>
                </div>
              </div>
            </label>
          ))}
        </div>
      </div>

      {/* Advanced Options */}
      <div>
        <h3 className="text-sm font-medium text-gray-900 mb-3">Advanced Options</h3>
        <div className="space-y-4">
          
          {/* Image Quality */}
          <div>
            <label className="block text-xs font-medium text-gray-700 mb-1">
              Image Quality: {advancedOptions.imageQuality}%
            </label>
            <input
              type="range"
              min="10"
              max="100"
              step="5"
              value={advancedOptions.imageQuality}
              onChange={(e) => setAdvancedOptions(prev => ({ ...prev, imageQuality: parseInt(e.target.value) }))}
              className="w-full"
            />
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>Smaller file</span>
              <span>Better quality</span>
            </div>
          </div>

          {/* Target File Size */}
          <div>
            <label className="block text-xs font-medium text-gray-700 mb-1">
              Target File Size (MB, 0 = no limit)
            </label>
            <input
              type="number"
              min="0"
              step="0.1"
              value={advancedOptions.targetSize}
              onChange={(e) => setAdvancedOptions(prev => ({ ...prev, targetSize: parseFloat(e.target.value) || 0 }))}
              placeholder="Enter target size in MB"
              className="w-full text-sm border border-gray-300 rounded-md px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Compression Options */}
          <div className="space-y-2">
            <label className="flex items-center justify-between">
              <span className="text-sm text-gray-700">Compress images</span>
              <input
                type="checkbox"
                checked={advancedOptions.compressImages}
                onChange={(e) => setAdvancedOptions(prev => ({ ...prev, compressImages: e.target.checked }))}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
            </label>
            <label className="flex items-center justify-between">
              <span className="text-sm text-gray-700">Remove metadata</span>
              <input
                type="checkbox"
                checked={advancedOptions.removeMetadata}
                onChange={(e) => setAdvancedOptions(prev => ({ ...prev, removeMetadata: e.target.checked }))}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
            </label>
            <label className="flex items-center justify-between">
              <span className="text-sm text-gray-700">Optimize fonts</span>
              <input
                type="checkbox"
                checked={advancedOptions.optimizeFonts}
                onChange={(e) => setAdvancedOptions(prev => ({ ...prev, optimizeFonts: e.target.checked }))}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
            </label>
            <label className="flex items-center justify-between">
              <span className="text-sm text-gray-700">Preserve original quality</span>
              <input
                type="checkbox"
                checked={advancedOptions.preserveQuality}
                onChange={(e) => setAdvancedOptions(prev => ({ ...prev, preserveQuality: e.target.checked }))}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
            </label>
          </div>
        </div>
      </div>

      {/* Compression Preview */}
      <div className="bg-gray-50 rounded-md p-4">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-sm font-medium text-gray-900">Compression Preview</h3>
          <button
            onClick={handlePreviewCompression}
            disabled={compressionPreview.processing}
            className="flex items-center space-x-1 text-xs text-blue-600 hover:text-blue-800 disabled:opacity-50"
          >
            {compressionPreview.processing ? (
              <>
                <div className="animate-spin rounded-full h-3 w-3 border-b border-blue-600"></div>
                <span>Calculating...</span>
              </>
            ) : (
              <>
                <EyeIcon className="h-3 w-3" />
                <span>Update Preview</span>
              </>
            )}
          </button>
        </div>

        <div className="grid grid-cols-3 gap-4 text-center">
          <div>
            <div className="text-lg font-semibold text-gray-900">
              {compressionPreview.originalSize.toFixed(1)} MB
            </div>
            <div className="text-xs text-gray-500">Original</div>
          </div>
          <div>
            <div className="text-lg font-semibold text-green-600">
              {compressionPreview.estimatedSize.toFixed(1)} MB
            </div>
            <div className="text-xs text-gray-500">Compressed</div>
          </div>
          <div>
            <div className="text-lg font-semibold text-blue-600">
              {compressionPreview.estimatedRatio}%
            </div>
            <div className="text-xs text-gray-500">Reduction</div>
          </div>
        </div>

        {/* Visual Size Comparison */}
        <div className="mt-4 space-y-1">
          <div className="flex items-center space-x-2">
            <div className="w-12 text-xs text-gray-600">Original</div>
            <div className="flex-1 bg-gray-300 h-2 rounded"></div>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-12 text-xs text-gray-600">New</div>
            <div className="flex-1 bg-gray-200 h-2 rounded relative">
              <div 
                className="bg-green-500 h-full rounded"
                style={{ width: `${(compressionPreview.estimatedSize / compressionPreview.originalSize) * 100}%` }}
              ></div>
            </div>
          </div>
        </div>
      </div>

      {/* Compression Actions */}
      <div className="flex space-x-2">
        <button
          onClick={handleCompress}
          className="flex-1 bg-blue-600 text-white text-sm py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center justify-center space-x-2"
        >
          <ArrowsPointingInIcon className="h-4 w-4" />
          <span>Compress PDF</span>
        </button>
        <button
          onClick={() => console.log('Save preset')}
          className="px-4 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <Cog6ToothIcon className="h-4 w-4" />
        </button>
      </div>

      {/* Instructions */}
      <div className="text-xs text-gray-500 bg-gray-50 rounded-md p-3">
        <h4 className="font-medium mb-1">Compression tips:</h4>
        <ul className="space-y-1">
          <li>• <strong>Quality:</strong> Best for documents you plan to edit later</li>
          <li>• <strong>Balanced:</strong> Good for most sharing and storage needs</li>
          <li>• <strong>Size:</strong> Maximum compression for email or web sharing</li>
          <li>• Use target file size for specific size requirements</li>
        </ul>
      </div>
    </div>
  );
};
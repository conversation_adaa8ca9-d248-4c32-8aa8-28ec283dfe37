import React, { useEffect, useState, useRef } from 'react';
import { usePDFEditorStore } from '../../../stores/pdf-editor';

interface ThumbnailsPanelProps {
  onPageSelect?: (pageNumber: number) => void;
}

interface PageThumbnailProps {
  pageNumber: number;
  isActive: boolean;
  onClick: () => void;
}

const PageThumbnail: React.FC<PageThumbnailProps> = ({ pageNumber, isActive, onClick }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { currentDocument, processedDocument } = usePDFEditorStore();

  useEffect(() => {
    const generateThumbnail = async () => {
      console.log('Generating thumbnail for page', pageNumber, {
        hasCanvas: !!canvasRef.current,
        hasProcessedDoc: !!processedDocument?.data,
        hasCurrentDoc: !!currentDocument
      });

      if (!canvasRef.current || (!processedDocument?.data && !currentDocument)) {
        console.log('Thumbnail generation skipped - missing requirements');
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);
        console.log('Starting thumbnail generation...');

        // Use the same approach as PDFViewer - check for global PDF.js
        // @ts-ignore
        if (!window.pdfjsLib) {
          console.log('PDF.js not loaded globally, importing...');
          const pdfjsLib = await import('pdfjs-dist');
          pdfjsLib.GlobalWorkerOptions.workerSrc = '/pdf.worker.mjs';
          // @ts-ignore
          window.pdfjsLib = pdfjsLib;
        }

        // @ts-ignore
        const pdfjsLib = window.pdfjsLib;

        // Prepare data for PDF.js (same as PDFViewer)
        let arrayBuffer: ArrayBuffer;
        if (processedDocument && processedDocument.data) {
          console.log('Using processed buffer for thumbnail:', processedDocument.data.byteLength, 'bytes');
          arrayBuffer = processedDocument.data.buffer.slice(
            processedDocument.data.byteOffset,
            processedDocument.data.byteOffset + processedDocument.data.byteLength
          ) as ArrayBuffer;
        } else if (currentDocument) {
          console.log('Using current document for thumbnail');
          arrayBuffer = await currentDocument.arrayBuffer();
        } else {
          throw new Error('No PDF data available');
        }

        // Load PDF document
        const loadingTask = pdfjsLib.getDocument({ data: arrayBuffer });
        const pdf = await loadingTask.promise;
        console.log('PDF loaded for thumbnail, pages:', pdf.numPages);

        // Get the page
        const page = await pdf.getPage(pageNumber);
        console.log('Got page for thumbnail:', pageNumber);

        // Calculate thumbnail size (small for sidebar)
        const viewport = page.getViewport({ scale: 1 });
        const thumbnailWidth = 80;
        const scale = thumbnailWidth / viewport.width;
        const scaledViewport = page.getViewport({ scale });
        console.log('Thumbnail viewport:', scaledViewport.width, 'x', scaledViewport.height);

        // Set canvas dimensions
        const canvas = canvasRef.current;
        const context = canvas.getContext('2d');
        if (!context) {
          console.error('Could not get canvas context for thumbnail');
          return;
        }

        canvas.width = scaledViewport.width;
        canvas.height = scaledViewport.height;
        canvas.style.width = `${scaledViewport.width}px`;
        canvas.style.height = `${scaledViewport.height}px`;
        console.log('Canvas setup for thumbnail:', canvas.width, 'x', canvas.height);

        // Render page
        const renderContext = {
          canvasContext: context,
          viewport: scaledViewport,
        };

        console.log('Starting thumbnail render...');
        await page.render(renderContext).promise;
        console.log('Thumbnail rendered successfully');
        setIsLoading(false);
      } catch (err) {
        console.error('Error generating thumbnail:', err);
        setError('Failed to load thumbnail');
        setIsLoading(false);
      }
    };

    generateThumbnail();
  }, [pageNumber, processedDocument, currentDocument]);

  return (
    <div
      onClick={onClick}
      className={`
        relative cursor-pointer rounded border-2 transition-all duration-200
        ${isActive
          ? 'border-blue-500 bg-blue-50'
          : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
        }
      `}
    >
      <div className="aspect-[3/4] bg-white rounded flex items-center justify-center">
        {isLoading ? (
          <div className="text-xs text-gray-400">Loading...</div>
        ) : error ? (
          <div className="text-center text-xs text-gray-400">
            <div className="w-8 h-10 bg-gray-100 rounded mb-1 mx-auto flex items-center justify-center">
              📄
            </div>
            <div>Page {pageNumber}</div>
          </div>
        ) : (
          <canvas
            ref={canvasRef}
            className="max-w-full max-h-full rounded"
          />
        )}
      </div>

      {/* Page number indicator */}
      <div className="absolute bottom-1 left-1 right-1 text-center">
        <span className="text-xs bg-white bg-opacity-90 px-1 rounded">
          {pageNumber}
        </span>
      </div>
    </div>
  );
};

export const ThumbnailsPanel: React.FC<ThumbnailsPanelProps> = ({ onPageSelect }) => {
  const { currentDocument, currentPage, setCurrentPage, documentPages } = usePDFEditorStore();

  const handlePageClick = (pageNumber: number) => {
    setCurrentPage(pageNumber);
    onPageSelect?.(pageNumber);
  };

  if (!currentDocument) {
    return (
      <div className="flex items-center justify-center h-32 text-gray-500 text-sm">
        No document loaded
      </div>
    );
  }

  // Generate thumbnail placeholders for now
  const pages = Array.from({ length: documentPages || 1 }, (_, i) => i + 1);

  return (
    <div className="space-y-2">
      <div className="text-xs font-medium text-gray-700 mb-3">
        Pages ({documentPages || 1})
      </div>
      
      <div className="space-y-2 max-h-96 overflow-y-auto">
        {pages.map((pageNumber) => (
          <PageThumbnail
            key={pageNumber}
            pageNumber={pageNumber}
            isActive={currentPage === pageNumber}
            onClick={() => handlePageClick(pageNumber)}
          />
        ))}
      </div>
    </div>
  );
};

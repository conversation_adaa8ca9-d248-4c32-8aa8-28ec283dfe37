import React, { useState } from 'react';
import { 
  RectangleStackIcon, 
  MagnifyingGlassIcon, 
  DocumentTextIcon, 
  ChatBubbleLeftIcon 
} from '@heroicons/react/24/outline';
import { ThumbnailsPanel } from './ThumbnailsPanel';
import { SearchPanel } from './SearchPanel';
import { OutlinesPanel } from './OutlinesPanel';
import { CommentsPanel } from './CommentsPanel';

export type SidebarPanelType = 'thumbnails' | 'search' | 'outlines' | 'comments';

interface SidebarPanelsProps {
  activePanel?: SidebarPanelType;
  onPanelChange?: (panel: SidebarPanelType) => void;
}

export const SidebarPanels: React.FC<SidebarPanelsProps> = ({ 
  activePanel = 'thumbnails', 
  onPanelChange 
}) => {
  const [currentPanel, setCurrentPanel] = useState<SidebarPanelType>(activePanel);

  const panels = [
    {
      id: 'thumbnails' as const,
      name: 'Pages',
      icon: RectangleStackIcon,
      component: ThumbnailsPanel
    },
    {
      id: 'search' as const,
      name: 'Search',
      icon: MagnifyingGlassIcon,
      component: SearchPanel
    },
    {
      id: 'outlines' as const,
      name: 'Outline',
      icon: DocumentTextIcon,
      component: OutlinesPanel
    },
    {
      id: 'comments' as const,
      name: 'Comments',
      icon: ChatBubbleLeftIcon,
      component: CommentsPanel
    }
  ];

  const handlePanelClick = (panelId: SidebarPanelType) => {
    setCurrentPanel(panelId);
    onPanelChange?.(panelId);
  };

  const ActivePanelComponent = panels.find(panel => panel.id === currentPanel)?.component;

  return (
    <div className="h-full flex flex-col">
      {/* Panel Tabs */}
      <div className="flex border-b border-gray-200 bg-gray-50">
        {panels.map((panel) => {
          const Icon = panel.icon;
          const isActive = currentPanel === panel.id;
          
          return (
            <button
              key={panel.id}
              onClick={() => handlePanelClick(panel.id)}
              className={`
                flex-1 flex flex-col items-center justify-center py-2 px-1 text-xs font-medium transition-colors
                ${isActive 
                  ? 'text-blue-600 bg-white border-b-2 border-blue-600' 
                  : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
                }
              `}
              title={panel.name}
            >
              <Icon className="h-4 w-4 mb-1" />
              <span className="truncate">{panel.name}</span>
            </button>
          );
        })}
      </div>

      {/* Panel Content */}
      <div className="flex-1 overflow-hidden">
        <div className="h-full p-3 overflow-y-auto">
          {ActivePanelComponent && (
            <ActivePanelComponent
              onPageSelect={(pageNumber: number) => {
                // Handle page selection from thumbnails
                console.log('Navigate to page:', pageNumber);
              }}
              onSearchResult={(result: any) => {
                // Handle search result selection
                console.log('Search result:', result);
              }}
              onOutlineClick={(pageNumber: number) => {
                // Handle outline item click
                console.log('Navigate to page from outline:', pageNumber);
              }}
              onCommentClick={(comment: any) => {
                // Handle comment click
                console.log('Navigate to comment:', comment);
              }}
              onCommentDelete={(commentId: string) => {
                // Handle comment deletion
                console.log('Delete comment:', commentId);
              }}
            />
          )}
        </div>
      </div>
    </div>
  );
};

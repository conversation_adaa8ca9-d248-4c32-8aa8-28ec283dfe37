import React, { useState, useCallback } from 'react';
import { MagnifyingGlassIcon, ChevronUpIcon, ChevronDownIcon } from '@heroicons/react/24/outline';
import { usePDFEditorStore } from '../../../stores/pdf-editor';

interface SearchResult {
  pageNumber: number;
  text: string;
  context: string;
  index: number;
}

interface SearchPanelProps {
  onSearchResult?: (result: SearchResult) => void;
}

export const SearchPanel: React.FC<SearchPanelProps> = ({ onSearchResult }) => {
  const { currentDocument } = usePDFEditorStore();
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [currentResultIndex, setCurrentResultIndex] = useState(-1);
  const [isSearching, setIsSearching] = useState(false);
  const [caseSensitive, setCaseSensitive] = useState(false);
  const [wholeWords, setWholeWords] = useState(false);

  const performSearch = useCallback(async (term: string) => {
    if (!term.trim() || !currentDocument) {
      setSearchResults([]);
      setCurrentResultIndex(-1);
      return;
    }

    setIsSearching(true);

    try {
      console.log('Starting PDF text search for:', term);

      // Get PDF data from store
      const { processedDocument } = usePDFEditorStore.getState();

      // Use the same approach as PDFViewer to load PDF
      // @ts-ignore
      if (!window.pdfjsLib) {
        const pdfjsLib = await import('pdfjs-dist');
        pdfjsLib.GlobalWorkerOptions.workerSrc = '/pdf.worker.mjs';
        // @ts-ignore
        window.pdfjsLib = pdfjsLib;
      }

      // @ts-ignore
      const pdfjsLib = window.pdfjsLib;

      // Prepare data for PDF.js
      let arrayBuffer: ArrayBuffer;
      if (processedDocument && processedDocument.data) {
        arrayBuffer = processedDocument.data.buffer.slice(
          processedDocument.data.byteOffset,
          processedDocument.data.byteOffset + processedDocument.data.byteLength
        ) as ArrayBuffer;
      } else if (currentDocument) {
        arrayBuffer = await currentDocument.arrayBuffer();
      } else {
        throw new Error('No PDF data available for search');
      }

      // Load PDF document
      const loadingTask = pdfjsLib.getDocument({ data: arrayBuffer });
      const pdf = await loadingTask.promise;
      console.log('PDF loaded for search, pages:', pdf.numPages);

      const results: SearchResult[] = [];
      const searchTermLower = caseSensitive ? term : term.toLowerCase();

      // Search through all pages
      for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
        const page = await pdf.getPage(pageNum);
        const textContent = await page.getTextContent();

        // Extract text from page
        const pageText = textContent.items
          .map((item: any) => item.str)
          .join(' ');

        const searchText = caseSensitive ? pageText : pageText.toLowerCase();

        // Find matches
        let searchIndex = 0;
        let matchIndex = 0;

        while ((searchIndex = searchText.indexOf(searchTermLower, searchIndex)) !== -1) {
          // Create context around the match
          const contextStart = Math.max(0, searchIndex - 50);
          const contextEnd = Math.min(pageText.length, searchIndex + term.length + 50);
          const context = pageText.substring(contextStart, contextEnd);

          results.push({
            pageNumber: pageNum,
            text: term,
            context: contextStart > 0 ? '...' + context : context + (contextEnd < pageText.length ? '...' : ''),
            index: matchIndex++
          });

          searchIndex += term.length;
        }
      }

      console.log('Search completed, found', results.length, 'results');
      setSearchResults(results);
      setCurrentResultIndex(results.length > 0 ? 0 : -1);

      if (results.length > 0) {
        onSearchResult?.(results[0]);
      }
    } catch (error) {
      console.error('Search error:', error);
      setSearchResults([]);
      setCurrentResultIndex(-1);
    } finally {
      setIsSearching(false);
    }
  }, [currentDocument, onSearchResult, caseSensitive]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    performSearch(searchTerm);
  };

  const navigateToResult = (direction: 'next' | 'prev') => {
    if (searchResults.length === 0) return;
    
    let newIndex;
    if (direction === 'next') {
      newIndex = currentResultIndex < searchResults.length - 1 ? currentResultIndex + 1 : 0;
    } else {
      newIndex = currentResultIndex > 0 ? currentResultIndex - 1 : searchResults.length - 1;
    }
    
    setCurrentResultIndex(newIndex);
    onSearchResult?.(searchResults[newIndex]);
  };

  const selectResult = (index: number) => {
    setCurrentResultIndex(index);
    onSearchResult?.(searchResults[index]);
  };

  return (
    <div className="space-y-4">
      <div className="text-xs font-medium text-gray-700 mb-3">
        Search Document
      </div>
      
      {/* Search Form */}
      <form onSubmit={handleSearch} className="space-y-3">
        <div className="relative">
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="Search in document..."
            className="w-full pl-8 pr-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          <MagnifyingGlassIcon className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-400" />
        </div>
        
        {/* Search Options */}
        <div className="space-y-2">
          <label className="flex items-center text-xs text-gray-600">
            <input
              type="checkbox"
              checked={caseSensitive}
              onChange={(e) => setCaseSensitive(e.target.checked)}
              className="mr-2 h-3 w-3"
            />
            Case sensitive
          </label>
          <label className="flex items-center text-xs text-gray-600">
            <input
              type="checkbox"
              checked={wholeWords}
              onChange={(e) => setWholeWords(e.target.checked)}
              className="mr-2 h-3 w-3"
            />
            Whole words only
          </label>
        </div>
      </form>

      {/* Search Results */}
      {searchResults.length > 0 && (
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-xs text-gray-600">
              {searchResults.length} result{searchResults.length !== 1 ? 's' : ''}
            </span>
            <div className="flex items-center space-x-1">
              <button
                onClick={() => navigateToResult('prev')}
                disabled={searchResults.length === 0}
                className="p-1 text-gray-400 hover:text-gray-600 disabled:opacity-50"
              >
                <ChevronUpIcon className="h-4 w-4" />
              </button>
              <button
                onClick={() => navigateToResult('next')}
                disabled={searchResults.length === 0}
                className="p-1 text-gray-400 hover:text-gray-600 disabled:opacity-50"
              >
                <ChevronDownIcon className="h-4 w-4" />
              </button>
            </div>
          </div>
          
          <div className="max-h-64 overflow-y-auto space-y-2">
            {searchResults.map((result, index) => (
              <div
                key={index}
                onClick={() => selectResult(index)}
                className={`
                  p-2 rounded cursor-pointer text-xs transition-colors
                  ${index === currentResultIndex 
                    ? 'bg-blue-100 border border-blue-300' 
                    : 'bg-gray-50 hover:bg-gray-100 border border-transparent'
                  }
                `}
              >
                <div className="font-medium text-gray-700 mb-1">
                  Page {result.pageNumber}
                </div>
                <div className="text-gray-600 line-clamp-2">
                  {result.context}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {isSearching && (
        <div className="text-center text-xs text-gray-500 py-4">
          Searching...
        </div>
      )}

      {searchTerm && searchResults.length === 0 && !isSearching && (
        <div className="text-center text-xs text-gray-500 py-4">
          No results found
        </div>
      )}
    </div>
  );
};

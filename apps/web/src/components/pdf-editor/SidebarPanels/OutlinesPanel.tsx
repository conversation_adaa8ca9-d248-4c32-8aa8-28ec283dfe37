import React, { useState } from 'react';
import { ChevronRightIcon, ChevronDownIcon, DocumentTextIcon } from '@heroicons/react/24/outline';
import { usePDFEditorStore } from '../../../stores/pdf-editor';

interface OutlineItem {
  title: string;
  pageNumber: number;
  level: number;
  children?: OutlineItem[];
  expanded?: boolean;
}

interface OutlinesPanelProps {
  onOutlineClick?: (pageNumber: number) => void;
}

export const OutlinesPanel: React.FC<OutlinesPanelProps> = ({ onOutlineClick }) => {
  const { currentDocument, setCurrentPage } = usePDFEditorStore();
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());

  // Mock outline data - in a real implementation, this would come from PDF metadata
  const mockOutlines: OutlineItem[] = [
    {
      title: "Introduction",
      pageNumber: 1,
      level: 0,
      children: [
        { title: "Overview", pageNumber: 1, level: 1 },
        { title: "Getting Started", pageNumber: 2, level: 1 }
      ]
    },
    {
      title: "Main Content",
      pageNumber: 3,
      level: 0,
      children: [
        {
          title: "Chapter 1",
          pageNumber: 3,
          level: 1,
          children: [
            { title: "Section 1.1", pageNumber: 3, level: 2 },
            { title: "Section 1.2", pageNumber: 4, level: 2 }
          ]
        },
        { title: "Chapter 2", pageNumber: 5, level: 1 }
      ]
    },
    {
      title: "Conclusion",
      pageNumber: 6,
      level: 0
    }
  ];

  const toggleExpanded = (itemKey: string) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(itemKey)) {
      newExpanded.delete(itemKey);
    } else {
      newExpanded.add(itemKey);
    }
    setExpandedItems(newExpanded);
  };

  const handleOutlineClick = (pageNumber: number) => {
    setCurrentPage(pageNumber);
    onOutlineClick?.(pageNumber);
  };

  const renderOutlineItem = (item: OutlineItem, index: number, parentKey = '') => {
    const itemKey = `${parentKey}-${index}`;
    const hasChildren = item.children && item.children.length > 0;
    const isExpanded = expandedItems.has(itemKey);

    return (
      <div key={itemKey} className="select-none">
        <div
          className="flex items-center py-1 px-2 hover:bg-gray-100 rounded cursor-pointer group"
          style={{ paddingLeft: `${8 + item.level * 16}px` }}
        >
          {hasChildren ? (
            <button
              onClick={(e) => {
                e.stopPropagation();
                toggleExpanded(itemKey);
              }}
              className="mr-1 p-0.5 hover:bg-gray-200 rounded"
            >
              {isExpanded ? (
                <ChevronDownIcon className="h-3 w-3 text-gray-500" />
              ) : (
                <ChevronRightIcon className="h-3 w-3 text-gray-500" />
              )}
            </button>
          ) : (
            <div className="w-4 mr-1" />
          )}
          
          <DocumentTextIcon className="h-3 w-3 text-gray-400 mr-2 flex-shrink-0" />
          
          <div
            onClick={() => handleOutlineClick(item.pageNumber)}
            className="flex-1 flex items-center justify-between min-w-0"
          >
            <span className="text-xs text-gray-700 truncate group-hover:text-blue-600">
              {item.title}
            </span>
            <span className="text-xs text-gray-400 ml-2 flex-shrink-0">
              {item.pageNumber}
            </span>
          </div>
        </div>
        
        {hasChildren && isExpanded && (
          <div>
            {item.children!.map((child, childIndex) =>
              renderOutlineItem(child, childIndex, itemKey)
            )}
          </div>
        )}
      </div>
    );
  };

  if (!currentDocument) {
    return (
      <div className="flex items-center justify-center h-32 text-gray-500 text-sm">
        No document loaded
      </div>
    );
  }

  // Check if document has outlines (mock check)
  const hasOutlines = mockOutlines.length > 0;

  if (!hasOutlines) {
    return (
      <div className="space-y-4">
        <div className="text-xs font-medium text-gray-700 mb-3">
          Document Outline
        </div>
        <div className="flex flex-col items-center justify-center h-32 text-gray-500 text-xs text-center">
          <DocumentTextIcon className="h-8 w-8 mb-2 text-gray-300" />
          <div>No outline available</div>
          <div className="mt-1">This document doesn't contain bookmarks</div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-2">
      <div className="text-xs font-medium text-gray-700 mb-3">
        Document Outline
      </div>
      
      <div className="max-h-96 overflow-y-auto">
        {mockOutlines.map((item, index) => renderOutlineItem(item, index))}
      </div>
    </div>
  );
};

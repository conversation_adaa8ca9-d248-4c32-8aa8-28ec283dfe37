import React from 'react';
import { Tab } from '@headlessui/react';
import { 
  PencilIcon, 
  ChatBubbleLeftIcon, 
  DocumentIcon, 
  WrenchScrewdriverIcon,
  EyeIcon,
  ArrowsPointingInIcon,
  ArrowsPointingOutIcon 
} from '@heroicons/react/24/outline';
import { usePDFEditorStore, type EditorTab } from '../../stores/pdf-editor';
import { PDFViewer } from './PDFViewer';
import { TextEditor } from './TextEditor';
import { AnnotationTools } from './AnnotationTools';
import { PageManager } from './PageManager';
import { FormTools } from './FormTools';
import { OCRProcessor } from './OCRProcessor';
import { FormatConverter } from './FormatConverter';
import { CompressionTools } from './CompressionTools';

const tabs: Array<{ id: EditorTab; name: string; icon: React.ComponentType<any> }> = [
  { id: 'edit', name: 'Edit Text', icon: PencilIcon },
  { id: 'annotate', name: 'Annotate', icon: ChatBubbleLeftIcon },
  { id: 'pages', name: 'Pages', icon: DocumentIcon },
  { id: 'forms', name: 'Forms', icon: WrenchScrewdriverIcon },
  { id: 'ocr', name: 'OCR', icon: EyeIcon },
  { id: 'convert', name: 'Convert', icon: ArrowsPointingOutIcon },
  { id: 'compress', name: 'Compress', icon: ArrowsPointingInIcon }
];

export const PDFEditorLayout: React.FC = () => {
  const {
    activeTab,
    setActiveTab,
    sidebarOpen,
    setSidebarOpen,
    currentDocument,
    isDirty,
    zoomLevel,
    setZoomLevel,
    currentPage,
    documentPages
  } = usePDFEditorStore();

  if (!currentDocument) {
    return (
      <div className="flex items-center justify-center h-64 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
        <div className="text-center">
          <DocumentIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No document loaded</h3>
          <p className="mt-1 text-sm text-gray-500">
            Upload a PDF document to start editing
          </p>
        </div>
      </div>
    );
  }

  const renderTabContent = () => {
    switch (activeTab) {
      case 'edit':
        return <TextEditor />;
      case 'annotate':
        return <AnnotationTools />;
      case 'pages':
        return <PageManager />;
      case 'forms':
        return <FormTools />;
      case 'ocr':
        return <OCRProcessor />;
      case 'convert':
        return <FormatConverter />;
      case 'compress':
        return <CompressionTools />;
      default:
        return <TextEditor />;
    }
  };

  const handleZoomIn = () => {
    setZoomLevel(Math.min(zoomLevel * 1.2, 5.0));
  };

  const handleZoomOut = () => {
    setZoomLevel(Math.max(zoomLevel / 1.2, 0.1));
  };

  const formatZoom = () => {
    return Math.round(zoomLevel * 100) + '%';
  };

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Mobile Tab Bar - Only visible on mobile */}
      <div className="lg:hidden fixed top-16 left-0 right-0 z-50 bg-white border-b border-gray-200">
        <Tab.Group 
          selectedIndex={tabs.findIndex(tab => tab.id === activeTab)}
          onChange={(index) => setActiveTab(tabs[index].id)}
        >
          <Tab.List className="flex overflow-x-auto scrollbar-hide">
            {tabs.map((tab) => (
              <Tab
                key={tab.id}
                className={({ selected }) =>
                  `flex-shrink-0 flex items-center space-x-1 px-3 py-2 text-xs font-medium border-b-2 ${
                    selected
                      ? 'text-blue-600 border-blue-600'
                      : 'text-gray-600 border-transparent'
                  }`
                }
              >
                <tab.icon className="h-3 w-3" />
                <span className="whitespace-nowrap">{tab.name}</span>
              </Tab>
            ))}
          </Tab.List>
        </Tab.Group>
      </div>

      {/* Desktop Sidebar */}
      <div className={`hidden lg:flex ${sidebarOpen ? 'w-80' : 'w-0'} transition-all duration-300 overflow-hidden bg-white shadow-lg`}>
        <div className="flex flex-col h-full w-full">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200">
            <div className="flex items-center space-x-2">
              <DocumentIcon className="h-5 w-5 text-blue-600" />
              <div>
                <h2 className="text-sm font-medium text-gray-900 truncate">
                  {currentDocument.name}
                </h2>
                {isDirty && (
                  <p className="text-xs text-orange-600">Unsaved changes</p>
                )}
              </div>
            </div>
            <button
              onClick={() => setSidebarOpen(false)}
              className="p-1 text-gray-400 hover:text-gray-600 rounded"
            >
              <ArrowsPointingInIcon className="h-4 w-4" />
            </button>
          </div>

          {/* Desktop Tab Navigation */}
          <Tab.Group 
            selectedIndex={tabs.findIndex(tab => tab.id === activeTab)}
            onChange={(index) => setActiveTab(tabs[index].id)}
          >
            <Tab.List className="flex flex-col border-b border-gray-200">
              {tabs.map((tab) => (
                <Tab
                  key={tab.id}
                  className={({ selected }) =>
                    `flex items-center space-x-2 px-4 py-3 text-sm font-medium border-b border-transparent hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-inset ${
                      selected
                        ? 'text-blue-600 bg-blue-50 border-blue-600'
                        : 'text-gray-600 hover:text-gray-900'
                    }`
                  }
                >
                  <tab.icon className="h-4 w-4" />
                  <span>{tab.name}</span>
                </Tab>
              ))}
            </Tab.List>

            {/* Tab Panels */}
            <Tab.Panels className="flex-1 overflow-y-auto">
              <Tab.Panel className="p-4 focus:outline-none">
                <TextEditor />
              </Tab.Panel>
              <Tab.Panel className="p-4 focus:outline-none">
                <AnnotationTools />
              </Tab.Panel>
              <Tab.Panel className="p-4 focus:outline-none">
                <PageManager />
              </Tab.Panel>
              <Tab.Panel className="p-4 focus:outline-none">
                <FormTools />
              </Tab.Panel>
              <Tab.Panel className="p-4 focus:outline-none">
                <OCRProcessor />
              </Tab.Panel>
              <Tab.Panel className="p-4 focus:outline-none">
                <FormatConverter />
              </Tab.Panel>
              <Tab.Panel className="p-4 focus:outline-none">
                <CompressionTools />
              </Tab.Panel>
            </Tab.Panels>
          </Tab.Group>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col">
        {/* Desktop Toolbar */}
        <div className="hidden lg:flex bg-white border-b border-gray-200 px-4 py-2 items-center justify-between">
          {!sidebarOpen && (
            <button
              onClick={() => setSidebarOpen(true)}
              className="p-2 text-gray-400 hover:text-gray-600 rounded-md hover:bg-gray-100"
            >
              <ArrowsPointingOutIcon className="h-4 w-4" />
            </button>
          )}
          
          <div className="flex items-center space-x-4 ml-auto">
            <div className="text-sm text-gray-500">
              Page {currentPage} of {documentPages || 1}
            </div>
            <div className="flex items-center space-x-2">
              <button 
                onClick={handleZoomOut}
                className="px-3 py-1 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded"
              >
                Zoom Out
              </button>
              <span className="text-sm text-gray-600 min-w-[3rem] text-center">{formatZoom()}</span>
              <button 
                onClick={handleZoomIn}
                className="px-3 py-1 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded"
              >
                Zoom In
              </button>
            </div>
          </div>
        </div>

        {/* Mobile Toolbar */}
        <div className="lg:hidden bg-white border-b border-gray-200 px-2 py-1 flex items-center justify-between" style={{ marginTop: '48px' }}>
          <div className="text-xs text-gray-500">
            Page {currentPage} of {documentPages || 1}
          </div>
          <div className="flex items-center space-x-1">
            <button 
              onClick={handleZoomOut}
              className="px-2 py-1 text-xs text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded"
            >
              Zoom Out
            </button>
            <span className="text-xs text-gray-600 min-w-[2.5rem] text-center">{formatZoom()}</span>
            <button 
              onClick={handleZoomIn}
              className="px-2 py-1 text-xs text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded"
            >
              Zoom In
            </button>
          </div>
        </div>

        {/* PDF Viewer */}
        <div className="flex-1 relative">
          {/* Mobile Tool Panel */}
          <div className="lg:hidden absolute top-0 left-0 right-0 z-40 bg-white shadow-sm border-b border-gray-200">
            <div className="p-2 max-h-32 overflow-y-auto">
              {renderTabContent()}
            </div>
          </div>
          
          {/* PDF Content with proper spacing */}
          <div className="w-full h-full pt-32 lg:pt-0">
            <div className="w-full h-full overflow-hidden bg-gray-100">
              <PDFViewer />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
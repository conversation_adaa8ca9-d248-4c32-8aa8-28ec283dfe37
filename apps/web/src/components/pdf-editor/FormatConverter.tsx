import React, { useState } from 'react';
import { 
  ArrowsRightLeftIcon,
  DocumentTextIcon,
  PhotoIcon,
  DocumentIcon 
} from '@heroicons/react/24/outline';

export const FormatConverter: React.FC = () => {
  const [selectedFormat, setSelectedFormat] = useState('docx');
  const [conversionOptions, setConversionOptions] = useState({
    quality: 95,
    dpi: 300,
    pageRange: 'all',
    pageStart: 1,
    pageEnd: 1,
    preserveLayout: true,
    extractImages: false
  });
  const [isConverting, setIsConverting] = useState(false);

  const formatOptions = [
    { 
      value: 'docx', 
      name: 'Microsoft Word (.docx)', 
      icon: DocumentTextIcon,
      description: 'Editable Word document with layout preservation'
    },
    { 
      value: 'png', 
      name: 'PNG Images (.png)', 
      icon: PhotoIcon,
      description: 'High-quality images, one per page'
    },
    { 
      value: 'jpg', 
      name: 'JPEG Images (.jpg)', 
      icon: PhotoIcon,
      description: 'Compressed images with adjustable quality'
    },
    { 
      value: 'tiff', 
      name: 'TIFF Images (.tiff)', 
      icon: PhotoIcon,
      description: 'Multi-page TIFF document'
    }
  ];

  const handleConvert = async () => {
    setIsConverting(true);
    
    // Simulate conversion process
    console.log(`Converting to ${selectedFormat} with options:`, conversionOptions);
    
    setTimeout(() => {
      setIsConverting(false);
      console.log('Conversion complete!');
      // In real implementation, this would trigger download
    }, 3000);
  };

  const selectedFormatInfo = formatOptions.find(f => f.value === selectedFormat);

  return (
    <div className="space-y-6">
      {/* Format Selection */}
      <div>
        <h3 className="text-sm font-medium text-gray-900 mb-3">Export Format</h3>
        <div className="space-y-2">
          {formatOptions.map((format) => (
            <label
              key={format.value}
              className={`flex items-start space-x-3 p-3 rounded-md border cursor-pointer hover:bg-gray-50 ${
                selectedFormat === format.value
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200'
              }`}
            >
              <input
                type="radio"
                name="format"
                value={format.value}
                checked={selectedFormat === format.value}
                onChange={(e) => setSelectedFormat(e.target.value)}
                className="mt-1 text-blue-600 focus:ring-blue-500 border-gray-300"
              />
              <div className="flex-1">
                <div className="flex items-center space-x-2">
                  <format.icon className="h-4 w-4 text-gray-600" />
                  <span className="text-sm font-medium text-gray-900">
                    {format.name}
                  </span>
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  {format.description}
                </p>
              </div>
            </label>
          ))}
        </div>
      </div>

      {/* Conversion Options */}
      <div>
        <h3 className="text-sm font-medium text-gray-900 mb-3">Conversion Options</h3>
        <div className="space-y-4">
          
          {/* Page Range */}
          <div>
            <label className="block text-xs font-medium text-gray-700 mb-2">
              Page Range
            </label>
            <div className="space-y-2">
              <label className="flex items-center space-x-2">
                <input
                  type="radio"
                  name="pageRange"
                  value="all"
                  checked={conversionOptions.pageRange === 'all'}
                  onChange={(e) => setConversionOptions(prev => ({ ...prev, pageRange: e.target.value }))}
                  className="text-blue-600 focus:ring-blue-500 border-gray-300"
                />
                <span className="text-sm text-gray-700">All pages</span>
              </label>
              <label className="flex items-center space-x-2">
                <input
                  type="radio"
                  name="pageRange"
                  value="range"
                  checked={conversionOptions.pageRange === 'range'}
                  onChange={(e) => setConversionOptions(prev => ({ ...prev, pageRange: e.target.value }))}
                  className="text-blue-600 focus:ring-blue-500 border-gray-300"
                />
                <span className="text-sm text-gray-700">Page range:</span>
                <input
                  type="number"
                  min="1"
                  value={conversionOptions.pageStart}
                  onChange={(e) => setConversionOptions(prev => ({ ...prev, pageStart: parseInt(e.target.value) || 1 }))}
                  disabled={conversionOptions.pageRange !== 'range'}
                  className="w-16 text-xs border border-gray-300 rounded px-1 py-0.5 disabled:bg-gray-50"
                />
                <span className="text-sm text-gray-700">to</span>
                <input
                  type="number"
                  min="1"
                  value={conversionOptions.pageEnd}
                  onChange={(e) => setConversionOptions(prev => ({ ...prev, pageEnd: parseInt(e.target.value) || 1 }))}
                  disabled={conversionOptions.pageRange !== 'range'}
                  className="w-16 text-xs border border-gray-300 rounded px-1 py-0.5 disabled:bg-gray-50"
                />
              </label>
            </div>
          </div>

          {/* Quality Settings for Images */}
          {['png', 'jpg', 'tiff'].includes(selectedFormat) && (
            <>
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">
                  Image Quality: {conversionOptions.quality}%
                </label>
                <input
                  type="range"
                  min="10"
                  max="100"
                  step="5"
                  value={conversionOptions.quality}
                  onChange={(e) => setConversionOptions(prev => ({ ...prev, quality: parseInt(e.target.value) }))}
                  className="w-full"
                />
              </div>

              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">
                  Resolution (DPI)
                </label>
                <select
                  value={conversionOptions.dpi}
                  onChange={(e) => setConversionOptions(prev => ({ ...prev, dpi: parseInt(e.target.value) }))}
                  className="w-full text-sm border border-gray-300 rounded-md px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value={72}>72 DPI (Web)</option>
                  <option value={150}>150 DPI (Standard)</option>
                  <option value={300}>300 DPI (High Quality)</option>
                  <option value={600}>600 DPI (Print)</option>
                </select>
              </div>
            </>
          )}

          {/* DOCX Options */}
          {selectedFormat === 'docx' && (
            <div className="space-y-2">
              <label className="flex items-center justify-between">
                <span className="text-sm text-gray-700">Preserve layout</span>
                <input
                  type="checkbox"
                  checked={conversionOptions.preserveLayout}
                  onChange={(e) => setConversionOptions(prev => ({ ...prev, preserveLayout: e.target.checked }))}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
              </label>
              <label className="flex items-center justify-between">
                <span className="text-sm text-gray-700">Extract images separately</span>
                <input
                  type="checkbox"
                  checked={conversionOptions.extractImages}
                  onChange={(e) => setConversionOptions(prev => ({ ...prev, extractImages: e.target.checked }))}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
              </label>
            </div>
          )}
        </div>
      </div>

      {/* Conversion Preview */}
      {selectedFormatInfo && (
        <div className="bg-gray-50 rounded-md p-3">
          <h4 className="text-sm font-medium text-gray-900 mb-2">Conversion Preview</h4>
          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <DocumentIcon className="h-4 w-4" />
              <span>Current PDF</span>
            </div>
            <ArrowsRightLeftIcon className="h-4 w-4 text-gray-400" />
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <selectedFormatInfo.icon className="h-4 w-4" />
              <span>{selectedFormatInfo.name}</span>
            </div>
          </div>
          <div className="mt-2 text-xs text-gray-500">
            {conversionOptions.pageRange === 'all' 
              ? 'Converting all pages' 
              : `Converting pages ${conversionOptions.pageStart}-${conversionOptions.pageEnd}`
            }
            {['png', 'jpg', 'tiff'].includes(selectedFormat) && 
              ` at ${conversionOptions.dpi} DPI, ${conversionOptions.quality}% quality`
            }
          </div>
        </div>
      )}

      {/* Convert Button */}
      <div>
        <button
          onClick={handleConvert}
          disabled={isConverting}
          className="w-full bg-blue-600 text-white text-sm py-3 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
        >
          {isConverting ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              <span>Converting...</span>
            </>
          ) : (
            <>
              <ArrowsRightLeftIcon className="h-4 w-4" />
              <span>Convert to {selectedFormatInfo?.name.split(' ')[0]}</span>
            </>
          )}
        </button>
      </div>

      {/* Instructions */}
      <div className="text-xs text-gray-500 bg-gray-50 rounded-md p-3">
        <h4 className="font-medium mb-1">Format conversion tips:</h4>
        <ul className="space-y-1">
          <li>• <strong>DOCX:</strong> Best for text-heavy documents that need editing</li>
          <li>• <strong>PNG:</strong> Lossless images with transparency support</li>
          <li>• <strong>JPEG:</strong> Smaller file sizes, good for photos</li>
          <li>• <strong>TIFF:</strong> Multi-page format for archival purposes</li>
        </ul>
      </div>
    </div>
  );
};
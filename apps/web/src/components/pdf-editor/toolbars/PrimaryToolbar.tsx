import React from 'react';
import { Tab } from '@headlessui/react';
import {
  EyeIcon,
  PencilIcon,
  Squares2X2Icon,
  DocumentPlusIcon,
  ScaleIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline';
import { usePDFEditorStore } from '../../../stores/pdf-editor';

export type PrimaryTab = 'view' | 'annotate' | 'shapes' | 'insert' | 'measure' | 'forms';

interface PrimaryTabConfig {
  id: PrimaryTab;
  name: string;
  icon: React.ComponentType<any>;
  description: string;
}

const primaryTabs: PrimaryTabConfig[] = [
  {
    id: 'view',
    name: 'View',
    icon: EyeIcon,
    description: 'View and navigation tools'
  },
  {
    id: 'annotate', 
    name: 'Annotate',
    icon: PencilIcon,
    description: 'Text markup and annotation tools'
  },
  {
    id: 'shapes',
    name: 'Shapes',
    icon: Squares2X2Icon,
    description: 'Geometric shapes and drawing tools'
  },
  {
    id: 'insert',
    name: 'Insert',
    icon: DocumentPlusIcon,
    description: 'Insert text, images, and content'
  },
  {
    id: 'measure',
    name: 'Measure',
    icon: ScaleIcon,
    description: 'Measurement and dimension tools'
  },
  {
    id: 'forms',
    name: 'Fill & Sign',
    icon: DocumentTextIcon,
    description: 'Form filling and digital signatures'
  }
];

interface PrimaryToolbarProps {
  activeTab: PrimaryTab;
  onTabChange: (tab: PrimaryTab) => void;
  className?: string;
}

export const PrimaryToolbar: React.FC<PrimaryToolbarProps> = ({
  activeTab,
  onTabChange,
  className = ''
}) => {
  return (
    <div className={`bg-white border-b border-gray-200 ${className}`}>
      <Tab.Group
        selectedIndex={primaryTabs.findIndex(tab => tab.id === activeTab)}
        onChange={(index) => onTabChange(primaryTabs[index].id)}
      >
        {/* Desktop Tab List */}
        <Tab.List className="hidden md:flex border-b border-gray-100">
          {primaryTabs.map((tab) => (
            <Tab
              key={tab.id}
              className={({ selected }) =>
                `group flex items-center space-x-2 px-6 py-3 text-sm font-medium border-b-2 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-inset ${
                  selected
                    ? 'text-blue-600 border-blue-600 bg-blue-50'
                    : 'text-gray-600 border-transparent hover:text-gray-900 hover:bg-gray-50'
                }`
              }
            >
              <tab.icon className="h-4 w-4" />
              <span>{tab.name}</span>
            </Tab>
          ))}
        </Tab.List>

        {/* Mobile Horizontal Scrollable Tab List */}
        <Tab.List className="md:hidden flex overflow-x-auto scrollbar-hide border-b border-gray-100">
          {primaryTabs.map((tab) => (
            <Tab
              key={tab.id}
              className={({ selected }) =>
                `flex-shrink-0 flex flex-col items-center justify-center px-4 py-3 text-xs font-medium border-b-2 transition-colors min-w-[80px] focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-inset ${
                  selected
                    ? 'text-blue-600 border-blue-600 bg-blue-50'
                    : 'text-gray-600 border-transparent hover:text-gray-900 hover:bg-gray-50'
                }`
              }
            >
              <tab.icon className="h-5 w-5 mb-1" />
              <span className="whitespace-nowrap">{tab.name}</span>
            </Tab>
          ))}
        </Tab.List>
      </Tab.Group>
    </div>
  );
};
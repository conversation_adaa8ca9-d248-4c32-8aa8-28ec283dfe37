import React from 'react';
import { Menu, Transition } from '@headlessui/react';
import { Fragment } from 'react';
import {
  MagnifyingGlassMinusIcon,
  MagnifyingGlassPlusIcon,
  ChevronDownIcon,
} from '@heroicons/react/24/outline';
import { usePDFEditorStore } from '../../../stores/pdf-editor';

interface ZoomPreset {
  label: string;
  value: number | 'fitWidth' | 'fitPage';
}

const zoomPresets: ZoomPreset[] = [
  { label: 'Fit Page', value: 'fitPage' },
  { label: 'Fit Width', value: 'fitWidth' },
  { label: '50%', value: 0.5 },
  { label: '75%', value: 0.75 },
  { label: '100%', value: 1.0 },
  { label: '125%', value: 1.25 },
  { label: '150%', value: 1.5 },
  { label: '200%', value: 2.0 },
  { label: '300%', value: 3.0 },
  { label: '400%', value: 4.0 },
];

export const ZoomControls: React.FC = () => {
  const { zoomLevel, setZoomLevel } = usePDFEditorStore();

  const handleZoomIn = () => {
    const newZoom = Math.min(zoomLevel * 1.2, 5.0);
    setZoomLevel(newZoom);
  };

  const handleZoomOut = () => {
    const newZoom = Math.max(zoomLevel / 1.2, 0.1);
    setZoomLevel(newZoom);
  };

  const handleZoomPreset = (preset: ZoomPreset) => {
    if (typeof preset.value === 'number') {
      setZoomLevel(preset.value);
    } else if (preset.value === 'fitWidth') {
      // Calculate fit width - this will be enhanced when we have container dimensions
      // For now, use a reasonable fit width default
      setZoomLevel(1.2);
    } else if (preset.value === 'fitPage') {
      // Calculate fit page - this will be enhanced when we have container dimensions
      // For now, use a reasonable fit page default
      setZoomLevel(0.9);
    }
  };

  const formatZoom = (zoom: number): string => {
    return Math.round(zoom * 100) + '%';
  };

  const getCurrentZoomLabel = (): string => {
    const currentZoom = Math.round(zoomLevel * 100);
    const preset = zoomPresets.find(p => 
      typeof p.value === 'number' && Math.round(p.value * 100) === currentZoom
    );
    return preset ? preset.label : `${currentZoom}%`;
  };

  return (
    <div className="flex items-center space-x-1">
      {/* Zoom Out Button */}
      <button
        onClick={handleZoomOut}
        className="p-1.5 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded transition-colors"
        title="Zoom Out"
        disabled={zoomLevel <= 0.1}
      >
        <MagnifyingGlassMinusIcon className="h-4 w-4" />
      </button>

      {/* Zoom Dropdown */}
      <Menu as="div" className="relative">
        <Menu.Button className="flex items-center space-x-1 px-3 py-1.5 text-sm text-gray-700 hover:text-gray-900 hover:bg-gray-100 rounded border border-gray-300 transition-colors min-w-[80px] justify-center">
          <span>{getCurrentZoomLabel()}</span>
          <ChevronDownIcon className="h-3 w-3" />
        </Menu.Button>
        
        <Transition
          as={Fragment}
          enter="transition ease-out duration-100"
          enterFrom="transform opacity-0 scale-95"
          enterTo="transform opacity-100 scale-100"
          leave="transition ease-in duration-75"
          leaveFrom="transform opacity-100 scale-100"
          leaveTo="transform opacity-0 scale-95"
        >
          <Menu.Items className="absolute right-0 z-50 mt-1 w-40 bg-white border border-gray-200 rounded-md shadow-lg focus:outline-none">
            <div className="py-1">
              {zoomPresets.map((preset) => (
                <Menu.Item key={preset.label}>
                  {({ active }) => (
                    <button
                      onClick={() => handleZoomPreset(preset)}
                      className={`w-full text-left px-4 py-2 text-sm ${
                        active
                          ? 'bg-blue-50 text-blue-700'
                          : 'text-gray-700 hover:text-gray-900'
                      } ${
                        preset.label === getCurrentZoomLabel()
                          ? 'bg-blue-100 text-blue-700 font-medium'
                          : ''
                      }`}
                    >
                      {preset.label}
                    </button>
                  )}
                </Menu.Item>
              ))}
            </div>
          </Menu.Items>
        </Transition>
      </Menu>

      {/* Zoom In Button */}
      <button
        onClick={handleZoomIn}
        className="p-1.5 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded transition-colors"
        title="Zoom In"
        disabled={zoomLevel >= 5.0}
      >
        <MagnifyingGlassPlusIcon className="h-4 w-4" />
      </button>
    </div>
  );
};
import React from 'react';
import { ViewToolbar } from './ViewToolbar';
import type { PrimaryTab } from './PrimaryToolbar';

interface SecondaryToolbarProps {
  activeTab: PrimaryTab;
}

// Placeholder components for other tabs - these will be implemented in future stories
const AnnotateToolbar: React.FC = () => (
  <div className="bg-gray-50 border-b border-gray-200 px-4 py-2">
    <div className="flex items-center space-x-2 text-sm text-gray-600">
      <span>Annotate tools will be implemented in Story 1.2.2</span>
    </div>
  </div>
);

const ShapesToolbar: React.FC = () => (
  <div className="bg-gray-50 border-b border-gray-200 px-4 py-2">
    <div className="flex items-center space-x-2 text-sm text-gray-600">
      <span>Shapes tools - coming soon</span>
    </div>
  </div>
);

const InsertToolbar: React.FC = () => (
  <div className="bg-gray-50 border-b border-gray-200 px-4 py-2">
    <div className="flex items-center space-x-2 text-sm text-gray-600">
      <span>Insert tools - coming soon</span>
    </div>
  </div>
);

const MeasureToolbar: React.FC = () => (
  <div className="bg-gray-50 border-b border-gray-200 px-4 py-2">
    <div className="flex items-center space-x-2 text-sm text-gray-600">
      <span>Measure tools - coming soon</span>
    </div>
  </div>
);

const FormsToolbar: React.FC = () => (
  <div className="bg-gray-50 border-b border-gray-200 px-4 py-2">
    <div className="flex items-center space-x-2 text-sm text-gray-600">
      <span>Forms tools - coming soon</span>
    </div>
  </div>
);

export const SecondaryToolbar: React.FC<SecondaryToolbarProps> = ({ activeTab }) => {
  switch (activeTab) {
    case 'view':
      return <ViewToolbar />;
    case 'annotate':
      return <AnnotateToolbar />;
    case 'shapes':
      return <ShapesToolbar />;
    case 'insert':
      return <InsertToolbar />;
    case 'measure':
      return <MeasureToolbar />;
    case 'forms':
      return <FormsToolbar />;
    default:
      return <ViewToolbar />;
  }
};
import React from 'react';
import {
  ArrowPathIcon,
  ArrowsRightLeftIcon,
  DocumentIcon,
  Squares2X2Icon,
  ChevronLeftIcon,
  ChevronRightIcon
} from '@heroicons/react/24/outline';
import { usePDFEditorStore } from '../../../stores/pdf-editor';
import { ZoomControls } from './ZoomControls';

export const ViewToolbar: React.FC = () => {
  const {
    currentPage,
    documentPages,
    setCurrentPage,
  } = usePDFEditorStore();

  const handlePreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const handleNextPage = () => {
    if (currentPage < (documentPages || 1)) {
      setCurrentPage(currentPage + 1);
    }
  };

  const handlePageInputChange = (value: string) => {
    const pageNum = parseInt(value, 10);
    if (!isNaN(pageNum) && pageNum >= 1 && pageNum <= (documentPages || 1)) {
      setCurrentPage(pageNum);
    }
  };

  return (
    <div className="bg-gray-50 border-b border-gray-200 px-4 py-2">
      <div className="flex flex-wrap items-center gap-4">
        {/* Page Navigation Group */}
        <div className="flex items-center space-x-1 bg-white rounded border border-gray-300 p-1">
          <button
            onClick={handlePreviousPage}
            disabled={currentPage <= 1}
            className="p-1 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            title="Previous Page"
          >
            <ChevronLeftIcon className="h-4 w-4" />
          </button>

          <div className="flex items-center space-x-2 px-2">
            <span className="text-sm text-gray-600">Page</span>
            <input
              type="number"
              min={1}
              max={documentPages || 1}
              value={currentPage}
              onChange={(e) => handlePageInputChange(e.target.value)}
              className="w-12 px-1 py-0.5 text-sm text-center border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
            />
            <span className="text-sm text-gray-600">of {documentPages || 1}</span>
          </div>

          <button
            onClick={handleNextPage}
            disabled={currentPage >= (documentPages || 1)}
            className="p-1 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            title="Next Page"
          >
            <ChevronRightIcon className="h-4 w-4" />
          </button>
        </div>

        {/* Zoom Controls Group */}
        <div className="bg-white rounded border border-gray-300 p-1">
          <ZoomControls />
        </div>

        {/* Layout and View Options Group */}
        <div className="flex items-center space-x-1 bg-white rounded border border-gray-300 p-1">
          <button
            className="p-1.5 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded transition-colors"
            title="Single Page View"
          >
            <DocumentIcon className="h-4 w-4" />
          </button>
          
          <button
            className="p-1.5 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded transition-colors"
            title="Two Page View"
          >
            <Squares2X2Icon className="h-4 w-4" />
          </button>
        </div>

        {/* Rotation Controls Group */}
        <div className="flex items-center space-x-1 bg-white rounded border border-gray-300 p-1">
          <button
            className="p-1.5 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded transition-colors"
            title="Rotate Counterclockwise"
          >
            <ArrowPathIcon className="h-4 w-4 scale-x-[-1]" />
          </button>
          
          <button
            className="p-1.5 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded transition-colors"
            title="Rotate Clockwise"
          >
            <ArrowPathIcon className="h-4 w-4" />
          </button>
        </div>

        {/* Mobile: Stack groups vertically on small screens */}
        <style jsx>{`
          @media (max-width: 640px) {
            .flex.flex-wrap {
              flex-direction: column;
              align-items: stretch;
              gap: 8px;
            }
            
            .flex.items-center.space-x-1 {
              justify-content: center;
            }
          }
        `}</style>
      </div>
    </div>
  );
};
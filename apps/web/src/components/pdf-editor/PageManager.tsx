import React from 'react';
import { 
  PlusIcon,
  MinusIcon,
  ArrowPathIcon,
  ScissorsIcon,
  DocumentDuplicateIcon,
  ArrowsUpDownIcon 
} from '@heroicons/react/24/outline';
import { usePDFEditorStore } from '../../stores/pdf-editor';

export const PageManager: React.FC = () => {
  const { 
    documentPages, 
    currentPage, 
    setCurrentPage, 
    processedDocument,
    setProcessedDocument,
    markDirty 
  } = usePDFEditorStore();

  const [isProcessing, setIsProcessing] = React.useState(false);
  const [operationStatus, setOperationStatus] = React.useState<string>('');

  const pageOperations = [
    { id: 'add', name: 'Add Page', icon: PlusIcon },
    { id: 'delete', name: 'Delete Page', icon: MinusIcon },
    { id: 'rotate', name: 'Rotate Page', icon: ArrowPathIcon },
    { id: 'split', name: '<PERSON> Page', icon: ScissorsIcon },
    { id: 'duplicate', name: 'Duplicate', icon: DocumentDuplicateIcon },
    { id: 'reorder', name: 'Reorder Pages', icon: ArrowsUpDownIcon }
  ];

  // Mock page thumbnails (in real implementation, these would be generated from PDF)
  const pages = Array.from({ length: documentPages || 3 }, (_, i) => ({
    pageNum: i + 1,
    thumbnail: null // Would be actual thumbnail image
  }));

  const handlePageOperation = async (operation: string) => {
    setIsProcessing(true);
    setOperationStatus(`Processing ${operation}...`);
    
    console.log(`Page operation: ${operation} on page ${currentPage}`);
    
    try {
      // Simulate processing time for visual feedback
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      switch (operation) {
        case 'add':
          // Simulate adding a page
          setOperationStatus('Page added successfully');
          // In real implementation, this would add a new page to the PDF
          // For now, we'll just show visual feedback
          console.log(`Added new page after page ${currentPage}`);
          markDirty(); // Mark document as modified
          break;
          
        case 'delete':
          if (documentPages <= 1) {
            setOperationStatus('Cannot delete the only page');
            return;
          }
          // Simulate deleting current page
          setOperationStatus('Page deleted successfully');
          // In real implementation, this would remove the page from PDF
          console.log(`Deleted page ${currentPage}`);
          // Navigate to previous page if we deleted the last page
          if (currentPage > documentPages - 1) {
            setCurrentPage(currentPage - 1);
          }
          markDirty();
          break;
          
        case 'rotate':
          // Simulate rotating current page
          setOperationStatus('Page rotated successfully');
          // In real implementation, this would rotate the page 90 degrees
          console.log(`Rotated page ${currentPage} by 90 degrees`);
          markDirty();
          break;
          
        case 'split':
          // Simulate splitting current page
          setOperationStatus('Page split successfully');
          // In real implementation, this would split the page in half
          console.log(`Split page ${currentPage} into two pages`);
          markDirty();
          break;
          
        case 'duplicate':
          // Simulate duplicating current page
          setOperationStatus('Page duplicated successfully');
          // In real implementation, this would duplicate the current page
          console.log(`Duplicated page ${currentPage}`);
          markDirty();
          break;
          
        case 'reorder':
          setOperationStatus('Reorder mode activated - drag pages to reorder');
          // In real implementation, this would enable drag-and-drop reordering
          console.log('Reorder mode activated');
          break;
          
        default:
          setOperationStatus('Unknown operation');
      }
      
      // Clear status after 3 seconds
      setTimeout(() => {
        setOperationStatus('');
      }, 3000);
      
    } catch (error) {
      console.error('Page operation failed:', error);
      setOperationStatus('Operation failed');
      setTimeout(() => {
        setOperationStatus('');
      }, 3000);
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Operation Status - Mobile optimized */}
      {(isProcessing || operationStatus) && (
        <div className={`p-3 rounded-md text-sm ${
          operationStatus.includes('failed') || operationStatus.includes('Cannot') 
            ? 'bg-red-50 text-red-700 border border-red-200' 
            : operationStatus.includes('successfully') 
            ? 'bg-green-50 text-green-700 border border-green-200'
            : 'bg-blue-50 text-blue-700 border border-blue-200'
        }`}>
          <div className="flex items-center space-x-2">
            {isProcessing && (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
            )}
            <span>{operationStatus}</span>
          </div>
        </div>
      )}

      {/* Page Operations */}
      <div>
        <h3 className="text-sm font-medium text-gray-900 mb-3">Page Operations</h3>
        <div className="grid grid-cols-2 gap-2">
          {pageOperations.map((operation) => (
            <button
              key={operation.id}
              onClick={() => handlePageOperation(operation.id)}
              disabled={isProcessing}
              className="flex items-center space-x-2 p-3 md:p-2 rounded-md border border-gray-200 hover:border-gray-300 hover:bg-gray-50 text-sm disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <operation.icon className="h-5 w-5 md:h-4 md:w-4" />
              <span className="text-xs md:text-sm">{operation.name}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Page Thumbnails - Mobile optimized */}
      <div>
        <h3 className="text-sm font-medium text-gray-900 mb-3">
          Pages Overview ({pages.length} pages)
        </h3>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-3 max-h-96 overflow-y-auto">
          {pages.map((page) => (
            <div
              key={page.pageNum}
              onClick={() => setCurrentPage(page.pageNum)}
              className={`relative border-2 rounded-md cursor-pointer hover:border-blue-300 transition-colors ${
                currentPage === page.pageNum
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200'
              }`}
            >
              {/* Thumbnail placeholder */}
              <div className="aspect-[8.5/11] bg-white border rounded-md flex items-center justify-center">
                <div className="text-center text-gray-400">
                  <div className="text-2xl mb-1">📄</div>
                  <div className="text-xs">Page {page.pageNum}</div>
                </div>
              </div>
              
              {/* Page number badge - Larger for mobile */}
              <div className="absolute -top-2 -left-2 bg-blue-600 text-white text-xs rounded-full w-7 h-7 md:w-6 md:h-6 flex items-center justify-center font-medium">
                {page.pageNum}
              </div>

              {/* Current page indicator - Larger for mobile */}
              {currentPage === page.pageNum && (
                <div className="absolute -top-1 -right-1 bg-green-500 rounded-full w-4 h-4 md:w-3 md:h-3"></div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Page Navigation - Mobile optimized */}
      <div className="flex items-center justify-between p-4 md:p-3 bg-gray-50 rounded-md">
        <button
          onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
          disabled={currentPage <= 1}
          className="px-4 py-2 md:px-3 md:py-1 text-sm border border-gray-300 rounded hover:bg-white disabled:opacity-50 disabled:cursor-not-allowed font-medium"
        >
          Previous
        </button>
        
        <span className="text-sm md:text-xs text-gray-600 font-medium">
          Page {currentPage} of {pages.length}
        </span>
        
        <button
          onClick={() => setCurrentPage(Math.min(pages.length, currentPage + 1))}
          disabled={currentPage >= pages.length}
          className="px-4 py-2 md:px-3 md:py-1 text-sm border border-gray-300 rounded hover:bg-white disabled:opacity-50 disabled:cursor-not-allowed font-medium"
        >
          Next
        </button>
      </div>

      {/* Instructions - Mobile optimized */}
      <div className="text-xs text-gray-500 bg-gray-50 rounded-md p-4 md:p-3">
        <h4 className="font-medium mb-2">How to use:</h4>
        <ul className="space-y-1">
          <li>• Tap page thumbnails to navigate</li>
          <li>• Use page operations to modify structure</li>
          <li>• Operations show visual feedback when completed</li>
          <li>• Changes are automatically saved to document</li>
        </ul>
      </div>
    </div>
  );
};
import React from 'react';
import { 
  PaintBrushIcon,
  ChatBubbleLeftIcon,
  PencilIcon,
  PhotoIcon 
} from '@heroicons/react/24/outline';
import { usePDFEditorStore } from '../../stores/pdf-editor';

export const AnnotationTools: React.FC = () => {
  const { 
    activeTool, 
    setActiveTool, 
    annotations, 
    removeAnnotation,
    annotationOptions,
    setAnnotationOptions
  } = usePDFEditorStore();

  const tools = [
    { id: 'highlight', name: 'Highlight', icon: PaintBrushIcon },
    { id: 'comment', name: 'Comment', icon: ChatBubbleLeftIcon },
    { id: 'draw', name: 'Draw', icon: PencilIcon },
    { id: 'stamp', name: 'Stamp', icon: PhotoIcon }
  ];

  const highlightColors = [
    '#FFFF00', // Yellow
    '#00FF00', // Green
    '#FF69B4', // Pink
    '#00BFFF', // Blue
    '#FFD700', // Gold
    '#FF6347'  // Tomato
  ];

  return (
    <div className="space-y-6">
      {/* Tool Selection */}
      <div>
        <h3 className="text-sm font-medium text-gray-900 mb-3">Annotation Tools</h3>
        <div className="grid grid-cols-2 gap-2">
          {tools.map((tool) => (
            <button
              key={tool.id}
              onClick={() => setActiveTool(tool.id as any)}
              className={`flex items-center space-x-2 p-2 rounded-md border text-sm ${
                activeTool === tool.id
                  ? 'border-blue-500 bg-blue-50 text-blue-700'
                  : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
              }`}
            >
              <tool.icon className="h-4 w-4" />
              <span>{tool.name}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Highlight Options */}
      {activeTool === 'highlight' && (
        <div className="space-y-4">
          <h3 className="text-sm font-medium text-gray-900">Highlight Settings</h3>
          
          <div>
            <label className="block text-xs font-medium text-gray-700 mb-2">
              Color
            </label>
            <div className="flex flex-wrap gap-2">
              {highlightColors.map(color => (
                <button
                  key={color}
                  onClick={() => setAnnotationOptions(prev => ({ ...prev, highlightColor: color }))}
                  className={`w-8 h-8 rounded border-2 ${
                    annotationOptions.highlightColor === color ? 'border-gray-800' : 'border-gray-300'
                  }`}
                  style={{ backgroundColor: color }}
                />
              ))}
            </div>
          </div>

          <div>
            <label className="block text-xs font-medium text-gray-700 mb-1">
              Opacity: {Math.round(annotationOptions.highlightOpacity * 100)}%
            </label>
            <input
              type="range"
              min="0.1"
              max="1"
              step="0.1"
              value={annotationOptions.highlightOpacity}
              onChange={(e) => setAnnotationOptions(prev => ({ ...prev, highlightOpacity: parseFloat(e.target.value) }))}
              className="w-full"
            />
          </div>
        </div>
      )}

      {/* Drawing Options */}
      {activeTool === 'draw' && (
        <div className="space-y-4">
          <h3 className="text-sm font-medium text-gray-900">Drawing Settings</h3>
          
          <div>
            <label className="block text-xs font-medium text-gray-700 mb-1">
              Color
            </label>
            <input
              type="color"
              value={annotationOptions.drawColor}
              onChange={(e) => setAnnotationOptions(prev => ({ ...prev, drawColor: e.target.value }))}
              className="w-full h-8 rounded border border-gray-300"
            />
          </div>

          <div>
            <label className="block text-xs font-medium text-gray-700 mb-1">
              Line Width: {annotationOptions.drawWidth}px
            </label>
            <input
              type="range"
              min="1"
              max="10"
              value={annotationOptions.drawWidth}
              onChange={(e) => setAnnotationOptions(prev => ({ ...prev, drawWidth: parseInt(e.target.value) }))}
              className="w-full"
            />
          </div>
        </div>
      )}

      {/* Stamp Options - Only Approved stamp available */}
      {activeTool === 'stamp' && (
        <div className="space-y-4">
          <h3 className="text-sm font-medium text-gray-900">Stamp Settings</h3>
          
          <div>
            <label className="block text-xs font-medium text-gray-700 mb-2">
              Stamp Type
            </label>
            <div className="grid grid-cols-1 gap-2">
              {[
                { value: 'approved', label: '✅ Approved', color: 'green' },
                { value: 'rejected', label: '❌ Rejected', color: 'red' },
                { value: 'draft', label: '📝 Draft', color: 'blue' },
                { value: 'confidential', label: '🔒 Confidential', color: 'purple' },
                { value: 'urgent', label: '⚡ Urgent', color: 'orange' },
                { value: 'reviewed', label: '👁️ Reviewed', color: 'indigo' },
                { value: 'final', label: '🏁 Final', color: 'gray' }
              ].map(stamp => (
                <button
                  key={stamp.value}
                  onClick={() => setAnnotationOptions(prev => ({ ...prev, stampType: stamp.value }))}
                  className={`text-left px-3 py-2 rounded border text-sm ${
                    annotationOptions.stampType === stamp.value
                      ? `border-${stamp.color}-500 bg-${stamp.color}-50 text-${stamp.color}-700`
                      : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  {stamp.label}
                </button>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Comment Options */}
      {activeTool === 'comment' && (
        <div className="space-y-4">
          <h3 className="text-sm font-medium text-gray-900">Comment Settings</h3>
          
          <div>
            <label className="block text-xs font-medium text-gray-700 mb-2">
              Comment Style
            </label>
            <div className="space-y-2">
              <label className="flex items-center">
                <input
                  type="radio"
                  name="commentStyle"
                  value="sticky"
                  checked={annotationOptions.commentStyle === 'sticky'}
                  onChange={(e) => setAnnotationOptions(prev => ({ ...prev, commentStyle: e.target.value }))}
                  className="mr-2"
                />
                <span className="text-sm">Sticky note (hover to view)</span>
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  name="commentStyle"
                  value="inline"
                  checked={annotationOptions.commentStyle === 'inline'}
                  onChange={(e) => setAnnotationOptions(prev => ({ ...prev, commentStyle: e.target.value }))}
                  className="mr-2"
                />
                <span className="text-sm">Inline text</span>
              </label>
            </div>
          </div>
        </div>
      )}

      {/* Existing Annotations */}
      <div>
        <h3 className="text-sm font-medium text-gray-900 mb-3">
          Annotations ({annotations.length})
        </h3>
        {annotations.length === 0 ? (
          <p className="text-sm text-gray-500">No annotations yet</p>
        ) : (
          <div className="space-y-2 max-h-40 overflow-y-auto">
            {annotations.map((annotation) => (
              <div key={annotation.id} className="flex items-center justify-between p-2 bg-gray-50 rounded text-sm">
                <div>
                  <span className="capitalize font-medium">{annotation.type}</span>
                  <span className="text-gray-500 ml-2">Page {annotation.pageNum}</span>
                  {annotation.data.text && (
                    <div className="text-xs text-gray-600 truncate max-w-[100px]">
                      "{annotation.data.text}"
                    </div>
                  )}
                </div>
                <button 
                  onClick={() => removeAnnotation(annotation.id)}
                  className="text-red-600 hover:text-red-800 text-xs"
                >
                  Remove
                </button>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Instructions */}
      <div className="text-xs text-gray-500 bg-gray-50 rounded-md p-3">
        <h4 className="font-medium mb-1">How to use:</h4>
        <ul className="space-y-1">
          <li>• <strong>Highlight:</strong> Select text to highlight it</li>
          <li>• <strong>Comment:</strong> Click to add sticky notes</li>
          <li>• <strong>Draw:</strong> Click and drag to draw freehand</li>
          <li>• <strong>Stamp:</strong> Click to place stamps or signatures</li>
        </ul>
      </div>
    </div>
  );
};
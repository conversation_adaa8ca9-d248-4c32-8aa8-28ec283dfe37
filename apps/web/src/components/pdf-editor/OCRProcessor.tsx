import React, { useState } from 'react';
import { 
  EyeIcon,
  LanguageIcon,
  DocumentMagnifyingGlassIcon,
  DocumentTextIcon 
} from '@heroicons/react/24/outline';

export const OCRProcessor: React.FC = () => {
  const [selectedLanguages, setSelectedLanguages] = useState(['eng']);
  const [ocrResults, setOcrResults] = useState<{
    text: string;
    confidence: number;
    processed: boolean;
  } | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);

  const availableLanguages = [
    { code: 'eng', name: 'English' },
    { code: 'spa', name: 'Spanish' },
    { code: 'fra', name: 'French' },
    { code: 'deu', name: 'German' },
    { code: 'ita', name: 'Italian' },
    { code: 'por', name: 'Portuguese' },
    { code: 'rus', name: 'Russian' },
    { code: 'jpn', name: 'Japanese' },
    { code: 'chi_sim', name: 'Chinese (Simplified)' },
    { code: 'chi_tra', name: 'Chinese (Traditional)' },
    { code: 'kor', name: 'Korean' },
    { code: 'ara', name: 'Arabic' },
    { code: 'hin', name: 'Hindi' }
  ];

  const ocrOperations = [
    { id: 'scan', name: 'Scan Text', icon: EyeIcon },
    { id: 'languages', name: 'Languages', icon: LanguageIcon },
    { id: 'analyze', name: 'Analyze', icon: DocumentMagnifyingGlassIcon },
    { id: 'searchable', name: 'Make Searchable', icon: DocumentTextIcon }
  ];

  const handleLanguageToggle = (languageCode: string) => {
    setSelectedLanguages(prev => 
      prev.includes(languageCode)
        ? prev.filter(code => code !== languageCode)
        : [...prev, languageCode]
    );
  };

  const handleOCROperation = async (operation: string) => {
    console.log(`OCR operation: ${operation}`);
    
    if (operation === 'scan') {
      setIsProcessing(true);
      
      // Simulate OCR processing
      setTimeout(() => {
        setOcrResults({
          text: `This is sample extracted text from the PDF document using OCR processing with languages: ${selectedLanguages.map(code => availableLanguages.find(lang => lang.code === code)?.name).join(', ')}.\n\nThe OCR engine has detected and extracted this text with high confidence. In a real implementation, this would be the actual text content extracted from scanned documents or image-based PDFs.`,
          confidence: 87.5,
          processed: true
        });
        setIsProcessing(false);
      }, 2000);
    }
  };

  return (
    <div className="space-y-6">
      {/* OCR Operations */}
      <div>
        <h3 className="text-sm font-medium text-gray-900 mb-3">OCR Operations</h3>
        <div className="grid grid-cols-2 gap-2">
          {ocrOperations.map((operation) => (
            <button
              key={operation.id}
              onClick={() => handleOCROperation(operation.id)}
              disabled={isProcessing}
              className="flex items-center space-x-2 p-2 rounded-md border border-gray-200 hover:border-gray-300 hover:bg-gray-50 text-sm disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <operation.icon className="h-4 w-4" />
              <span>{operation.name}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Language Selection */}
      <div>
        <h3 className="text-sm font-medium text-gray-900 mb-3">
          OCR Languages ({selectedLanguages.length} selected)
        </h3>
        <div className="max-h-40 overflow-y-auto border border-gray-200 rounded-md">
          {availableLanguages.map((language) => (
            <label
              key={language.code}
              className="flex items-center space-x-2 p-2 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0"
            >
              <input
                type="checkbox"
                checked={selectedLanguages.includes(language.code)}
                onChange={() => handleLanguageToggle(language.code)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm text-gray-700">{language.name}</span>
              <span className="text-xs text-gray-400">({language.code})</span>
            </label>
          ))}
        </div>
      </div>

      {/* Processing Status */}
      {isProcessing && (
        <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
          <div className="flex items-center space-x-3">
            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
            <div>
              <h4 className="text-sm font-medium text-blue-900">Processing OCR...</h4>
              <p className="text-xs text-blue-700">
                Analyzing document with {selectedLanguages.length} language(s)
              </p>
            </div>
          </div>
        </div>
      )}

      {/* OCR Results */}
      {ocrResults && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-sm font-medium text-gray-900">OCR Results</h3>
            <div className="text-xs text-gray-500">
              Confidence: <span className="font-medium">{ocrResults.confidence}%</span>
            </div>
          </div>
          
          <div className="border border-gray-300 rounded-md">
            <div className="bg-gray-50 px-3 py-2 border-b border-gray-300">
              <div className="flex items-center justify-between">
                <span className="text-xs font-medium text-gray-700">Extracted Text</span>
                <button
                  onClick={() => navigator.clipboard.writeText(ocrResults.text)}
                  className="text-xs text-blue-600 hover:text-blue-800"
                >
                  Copy Text
                </button>
              </div>
            </div>
            <div className="p-3">
              <textarea
                value={ocrResults.text}
                onChange={(e) => setOcrResults(prev => prev ? { ...prev, text: e.target.value } : null)}
                className="w-full h-32 text-sm border-none resize-none focus:outline-none"
                placeholder="Extracted text will appear here..."
              />
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex space-x-2">
            <button
              onClick={() => handleOCROperation('searchable')}
              className="flex-1 bg-green-600 text-white text-sm py-2 px-3 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
            >
              Create Searchable PDF
            </button>
            <button
              onClick={() => console.log('Export text')}
              className="flex-1 bg-blue-600 text-white text-sm py-2 px-3 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              Export Text
            </button>
          </div>
        </div>
      )}

      {/* OCR Settings */}
      <div>
        <h3 className="text-sm font-medium text-gray-900 mb-3">OCR Settings</h3>
        <div className="space-y-3">
          <label className="flex items-center justify-between">
            <span className="text-sm text-gray-700">Preprocess images</span>
            <input
              type="checkbox"
              defaultChecked={true}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
          </label>
          <label className="flex items-center justify-between">
            <span className="text-sm text-gray-700">Auto-detect rotation</span>
            <input
              type="checkbox"
              defaultChecked={true}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
          </label>
          <label className="flex items-center justify-between">
            <span className="text-sm text-gray-700">Preserve formatting</span>
            <input
              type="checkbox"
              defaultChecked={false}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
          </label>
        </div>
      </div>

      {/* Instructions */}
      <div className="text-xs text-gray-500 bg-gray-50 rounded-md p-3">
        <h4 className="font-medium mb-1">How to use OCR:</h4>
        <ul className="space-y-1">
          <li>• Select languages for better recognition accuracy</li>
          <li>• Click "Scan Text" to extract text from images</li>
          <li>• Review and edit extracted text as needed</li>
          <li>• Create searchable PDF with invisible text overlay</li>
        </ul>
      </div>
    </div>
  );
};
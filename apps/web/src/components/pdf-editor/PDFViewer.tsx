import React, { useEffect, useRef, useState } from 'react';
import { usePDFEditorStore } from '../../stores/pdf-editor';

// PDF.js types
declare global {
  interface Window {
    pdfjsLib: any;
  }
}

export const PDFViewer: React.FC = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [pdfDocument, setPdfDocument] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pdfJsReady, setPdfJsReady] = useState(false);
  const [isSelecting, setIsSelecting] = useState(false);
  const [selectionStart, setSelectionStart] = useState<{ x: number; y: number } | null>(null);
  const [selectionEnd, setSelectionEnd] = useState<{ x: number; y: number } | null>(null);
  const [textInput, setTextInput] = useState<{
    x: number;
    y: number;
    visible: boolean;
    value: string;
  }>({ x: 0, y: 0, visible: false, value: '' });

  const {
    currentDocument,
    processedDocument,
    currentPage,
    zoomLevel,
    activeTool,
    annotations,
    addAnnotation,
    annotationOptions
  } = usePDFEditorStore();

  // Load PDF.js
  useEffect(() => {
    const loadPDFJS = async () => {
      try {
        // @ts-ignore
        if (typeof window !== 'undefined' && !window.pdfjsLib) {
          // Dynamically import PDF.js
          const pdfjsLib = await import('pdfjs-dist');
          
          // Use local worker from public directory
          pdfjsLib.GlobalWorkerOptions.workerSrc = '/pdf.worker.mjs';
          
          // @ts-ignore
          window.pdfjsLib = pdfjsLib;
          console.log('PDF.js imported successfully with local worker:', '/pdf.worker.mjs');
        }
        setPdfJsReady(true);
      } catch (error) {
        console.error('Failed to load PDF.js:', error);
        setError('Failed to load PDF viewer');
      }
    };

    loadPDFJS();
  }, []);

  // Load PDF document
  useEffect(() => {
    const loadPDF = async () => {
      if (!currentDocument || !pdfJsReady) {
        console.log('Waiting for document or PDF.js...', { 
          currentDocument: !!currentDocument, 
          pdfJsReady,
          // @ts-ignore
          pdfjsLib: !!window.pdfjsLib 
        });
        return;
      }

      try {
        setIsLoading(true);
        setError(null);
        console.log('Loading PDF document...');

        // @ts-ignore
        const pdfjsLib = window.pdfjsLib;
        
        let arrayBuffer: ArrayBuffer;
        if (processedDocument && processedDocument.data) {
          console.log('Using processed buffer:', processedDocument.data.byteLength, 'bytes');
          arrayBuffer = processedDocument.data.buffer.slice(
            processedDocument.data.byteOffset, 
            processedDocument.data.byteOffset + processedDocument.data.byteLength
          );
        } else if (currentDocument && typeof currentDocument.arrayBuffer === 'function') {
          console.log('Using original file buffer');
          arrayBuffer = await currentDocument.arrayBuffer();
        } else {
          throw new Error('No valid PDF data available. Please reload the document.');
        }

        console.log('ArrayBuffer size:', arrayBuffer.byteLength);

        const loadingTask = pdfjsLib.getDocument({ 
          data: arrayBuffer,
          useSystemFonts: false,
          standardFontDataUrl: null
        });
        
        console.log('PDF loading task created, waiting for completion...');
        const pdf = await loadingTask.promise;
        
        setPdfDocument(pdf);
        console.log('PDF loaded successfully:', pdf.numPages, 'pages');
      } catch (error) {
        console.error('Error loading PDF:', error);
        setError(`Failed to load PDF: ${error instanceof Error ? error.message : 'Unknown error'}`);
      } finally {
        setIsLoading(false);
      }
    };

    loadPDF();
  }, [currentDocument, pdfJsReady]);

  // Render PDF page
  useEffect(() => {
    const renderPage = async () => {
      if (!pdfDocument || !canvasRef.current || !currentPage) {
        console.log('Cannot render page:', {
          pdfDocument: !!pdfDocument,
          canvas: !!canvasRef.current,
          currentPage
        });
        return;
      }

      try {
        console.log(`Rendering page ${currentPage}...`);
        const page = await pdfDocument.getPage(currentPage);
        const canvas = canvasRef.current;
        const context = canvas.getContext('2d');

        if (!context) {
          throw new Error('Could not get canvas context');
        }

        // Get the original viewport
        const originalViewport = page.getViewport({ scale: 1.0 });
        console.log('Original viewport:', originalViewport.width, 'x', originalViewport.height);

        // Calculate base scale to fit the document in the container initially
        const containerWidth = containerRef.current?.clientWidth || window.innerWidth;
        const containerHeight = containerRef.current?.clientHeight || window.innerHeight;

        // Calculate base scale to fit document width with some padding
        const baseScale = Math.min(
          (containerWidth - 40) / originalViewport.width, // 40px total padding
          (containerHeight - 40) / originalViewport.height
        );

        // Apply zoom level to base scale (minimum 0.25x, maximum 5x)
        const finalScale = Math.max(Math.min(baseScale * zoomLevel, 5.0), 0.25);

        console.log('Internal viewport zoom calculation:', {
          originalWidth: originalViewport.width,
          originalHeight: originalViewport.height,
          containerWidth,
          containerHeight,
          baseScale,
          zoomLevel,
          finalScale
        });

        const scaledViewport = page.getViewport({ scale: finalScale });
        console.log('Scaled viewport:', scaledViewport.width, 'x', scaledViewport.height, 'at scale:', finalScale);

        // Set canvas dimensions for crisp rendering
        const dpr = window.devicePixelRatio || 1;
        canvas.width = scaledViewport.width * dpr;
        canvas.height = scaledViewport.height * dpr;
        canvas.style.width = scaledViewport.width + 'px';
        canvas.style.height = scaledViewport.height + 'px';

        console.log('Canvas setup for internal viewport scrolling:', {
          canvasWidth: canvas.width,
          canvasHeight: canvas.height,
          cssWidth: canvas.style.width,
          cssHeight: canvas.style.height,
          dpr,
          willScroll: scaledViewport.width > containerWidth || scaledViewport.height > containerHeight
        });

        // Scale context for high DPI displays
        context.scale(dpr, dpr);

        // Clear the canvas
        context.clearRect(0, 0, canvas.width, canvas.height);

        // Render the page
        console.log('Starting page render...');
        const renderContext = {
          canvasContext: context,
          viewport: scaledViewport
        };

        await page.render(renderContext).promise;
        console.log('Page rendered successfully');

      } catch (error) {
        console.error('Error rendering page:', error);
        setError(`Failed to render page: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    };

    renderPage();
  }, [pdfDocument, currentPage, zoomLevel]);

  const handleCanvasClick = (event: React.MouseEvent<HTMLCanvasElement>) => {
    if (!canvasRef.current || !activeTool) return;

    const canvas = canvasRef.current;
    const rect = canvas.getBoundingClientRect();
    
    // Get click position relative to canvas
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    
    // Convert to PDF coordinates (0-100% for position storage)
    const pdfX = (x / canvas.offsetWidth) * 100;
    const pdfY = (y / canvas.offsetHeight) * 100;

    console.log('Canvas clicked at:', { x: pdfX, y: pdfY, tool: activeTool });

    // Handle different tools
    switch (activeTool) {
      case 'text':
        // Show text input at clicked position
        setTextInput({
          x: event.clientX,
          y: event.clientY,
          visible: true,
          value: ''
        });
        break;
      
      case 'comment':
        // Add comment annotation
        const commentText = prompt('Enter your comment:');
        if (commentText && commentText.trim()) {
          addAnnotation({
            type: 'comment',
            pageNum: currentPage,
            position: { x: pdfX, y: pdfY },
            data: {
              text: commentText.trim(),
              style: annotationOptions.commentStyle
            }
          });
        }
        break;

      case 'stamp':
        // Add stamp annotation
        addAnnotation({
          type: 'stamp',
          pageNum: currentPage,
          position: { x: pdfX, y: pdfY },
          data: {
            stampType: annotationOptions.stampType,
            size: 'medium'
          }
        });
        break;
      
      case 'select':
        // Handle text selection
        console.log('Select tool clicked at:', { x: pdfX, y: pdfY });
        break;
      
      default:
        console.log('No active tool or unknown tool:', activeTool);
    }
  };

  const handleMouseDown = (event: React.MouseEvent<HTMLCanvasElement>) => {
    if (!canvasRef.current) return;

    if (activeTool === 'highlight' || activeTool === 'select') {
      setIsSelecting(true);
      const canvas = canvasRef.current;
      const rect = canvas.getBoundingClientRect();
      const x = event.clientX - rect.left;
      const y = event.clientY - rect.top;
      
      setSelectionStart({ x, y });
      setSelectionEnd({ x, y });
    }
  };

  const handleMouseMove = (event: React.MouseEvent<HTMLCanvasElement>) => {
    if (!canvasRef.current || !isSelecting) return;

    const canvas = canvasRef.current;
    const rect = canvas.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    
    setSelectionEnd({ x, y });
  };

  const handleMouseUp = () => {
    if (!isSelecting || !selectionStart || !selectionEnd || !canvasRef.current) {
      setIsSelecting(false);
      return;
    }

    const canvas = canvasRef.current;
    
    // Calculate selection bounds
    const startX = Math.min(selectionStart.x, selectionEnd.x);
    const startY = Math.min(selectionStart.y, selectionEnd.y);
    const width = Math.abs(selectionEnd.x - selectionStart.x);
    const height = Math.abs(selectionEnd.y - selectionStart.y);

    // Only create annotation if selection is large enough
    if (width > 10 && height > 10) {
      if (activeTool === 'highlight') {
        // Add highlight annotation
        addAnnotation({
          type: 'highlight',
          pageNum: currentPage,
          position: { 
            x: (startX / canvas.offsetWidth) * 100, 
            y: (startY / canvas.offsetHeight) * 100 
          },
          data: {
            width: (width / canvas.offsetWidth) * 100,
            height: (height / canvas.offsetHeight) * 100,
            color: annotationOptions.highlightColor,
            opacity: annotationOptions.highlightOpacity
          }
        });
      }
    }

    setIsSelecting(false);
    setSelectionStart(null);
    setSelectionEnd(null);
  };

  const handleTextSubmit = (text: string) => {
    if (!text.trim() || !canvasRef.current) return;

    const canvas = canvasRef.current;
    
    // Convert screen coordinates back to PDF coordinates
    const rect = canvas.getBoundingClientRect();
    const pdfX = ((textInput.x - rect.left) / canvas.offsetWidth) * 100;
    const pdfY = ((textInput.y - rect.top) / canvas.offsetHeight) * 100;

    addAnnotation({
      type: 'comment',
      pageNum: currentPage,
      position: { x: pdfX, y: pdfY },
      data: {
        text: text.trim(),
        style: 'text',
        fontFamily: annotationOptions.fontFamily,
        fontSize: annotationOptions.fontSize,
        color: annotationOptions.textColor,
        bold: annotationOptions.bold,
        italic: annotationOptions.italic,
        underline: annotationOptions.underline
      }
    });

    setTextInput({ x: 0, y: 0, visible: false, value: '' });
  };

  // Get stamp display text based on type
  const getStampText = (stampType: string): string => {
    switch (stampType) {
      case 'approved': return '✅ APPROVED';
      case 'rejected': return '❌ REJECTED';
      case 'draft': return '📝 DRAFT';
      case 'confidential': return '🔒 CONFIDENTIAL';
      case 'urgent': return '⚡ URGENT';
      case 'reviewed': return '👁️ REVIEWED';
      case 'final': return '🏁 FINAL';
      default: return '✅ APPROVED';
    }
  };

  // Get stamp color based on type
  const getStampColor = (stampType: string): string => {
    switch (stampType) {
      case 'approved': return '#059669'; // green-600
      case 'rejected': return '#dc2626'; // red-600
      case 'draft': return '#2563eb'; // blue-600
      case 'confidential': return '#7c3aed'; // purple-600
      case 'urgent': return '#ea580c'; // orange-600
      case 'reviewed': return '#4f46e5'; // indigo-600
      case 'final': return '#6b7280'; // gray-500
      default: return '#059669';
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading PDF...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center max-w-md">
          <div className="text-red-500 mb-2 text-2xl">⚠️</div>
          <p className="text-red-600 mb-4">{error}</p>
          {error.includes('No valid PDF data available') && (
            <p className="text-gray-600 mb-4 text-sm">
              This usually happens during development. Please go back and re-upload your PDF file.
            </p>
          )}
          <div className="space-x-2">
            <button
              onClick={() => {
                setError(null);
                setPdfDocument(null);
                setPdfJsReady(false);
              }}
              className="px-4 py-2 text-sm bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              Retry
            </button>
            <button
              onClick={() => window.history.back()}
              className="px-4 py-2 text-sm bg-gray-300 text-gray-700 rounded hover:bg-gray-400"
            >
              Go Back
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!currentDocument) {
    return (
      <div className="flex items-center justify-center h-full text-gray-500">
        <div className="text-center">
          <div className="text-4xl mb-4">📄</div>
          <p>No document to display</p>
        </div>
      </div>
    );
  }

  if (!pdfJsReady) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading PDF viewer...</p>
        </div>
      </div>
    );
  }

  return (
    <div
      className="w-full h-full bg-gray-100 relative"
      style={{
        position: 'relative',
        height: '100%',
        width: '100%'
      }}
    >
      {/* Fixed viewport container for internal scrolling */}
      <div
        ref={containerRef}
        className="absolute inset-0"
        style={{
          overflow: 'auto',
          WebkitOverflowScrolling: 'touch',
          overscrollBehavior: 'contain',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          // Internal viewport scrolling styles
          scrollbarWidth: 'thin',
          scrollbarColor: '#cbd5e1 #f1f5f9'
        }}
      >
        <div
          style={{
            padding: '20px',
            margin: '0',
            minWidth: 'fit-content',
            minHeight: 'fit-content',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'flex-start',
            // Ensure content can extend beyond container for scrolling
            width: 'max-content',
            height: 'max-content'
          }}
        >
          <div
            className="relative bg-white shadow-lg"
            style={{
              margin: '0',
              display: 'inline-block',
              // Add subtle border and shadow for professional appearance
              border: '1px solid #e2e8f0',
              borderRadius: '4px'
            }}
          >
        <canvas
          ref={canvasRef}
          onClick={handleCanvasClick}
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          // Add touch event handlers for mobile
          onTouchStart={(e) => {
            const touch = e.touches[0];
            const mouseEvent = new MouseEvent('mousedown', {
              clientX: touch.clientX,
              clientY: touch.clientY
            });
            handleMouseDown(mouseEvent as any);
          }}
          onTouchMove={(e) => {
            e.preventDefault(); // Prevent scrolling while drawing
            const touch = e.touches[0];
            const mouseEvent = new MouseEvent('mousemove', {
              clientX: touch.clientX,
              clientY: touch.clientY
            });
            handleMouseMove(mouseEvent as any);
          }}
          onTouchEnd={() => {
            handleMouseUp();
          }}
          className={`block ${
            activeTool === 'text' ? 'cursor-text' :
            activeTool === 'select' ? 'cursor-pointer' :
            activeTool === 'draw' ? 'cursor-crosshair' :
            activeTool === 'highlight' ? 'cursor-text' :
            'cursor-crosshair'
          }`}
          style={{
            display: 'block',
            border: 'none',
            margin: '0',
            padding: '0',
            touchAction: activeTool === 'draw' ? 'none' : 'auto', // Disable touch scrolling when drawing
            // Ensure canvas doesn't interfere with scrolling
            maxWidth: 'none',
            maxHeight: 'none'
          }}
        />

        {/* Annotations Overlay */}
        {canvasRef.current && annotations
          .filter(annotation => annotation.pageNum === currentPage)
          .map(annotation => {
            const canvas = canvasRef.current!;
            const x = (annotation.position.x / 100) * canvas.offsetWidth;
            const y = (annotation.position.y / 100) * canvas.offsetHeight;
            
            // Text annotations (from text tool and comment tool)
            if (annotation.type === 'comment' && annotation.data.text) {
              return (
                <div
                  key={annotation.id}
                  className="absolute pointer-events-none select-none"
                  style={{
                    left: x,
                    top: y,
                    transform: 'translate(0, -50%)',
                    fontFamily: annotation.data.fontFamily || 'Arial',
                    fontSize: `${Math.max((annotation.data.fontSize || 12) * (canvas.offsetHeight / 600), 10)}px`, // Ensure minimum font size for mobile
                    color: annotation.data.color || '#000000',
                    fontWeight: annotation.data.bold ? 'bold' : 'normal',
                    fontStyle: annotation.data.italic ? 'italic' : 'normal',
                    textDecoration: annotation.data.underline ? 'underline' : 'none',
                    whiteSpace: 'nowrap',
                    textShadow: '1px 1px 2px rgba(255,255,255,0.8)', // Better visibility
                    zIndex: 10
                  }}
                >
                  {annotation.data.text}
                </div>
              );
            }

            // Highlight annotations
            if (annotation.type === 'highlight') {
              const width = (annotation.data.width / 100) * canvas.offsetWidth;
              const height = (annotation.data.height / 100) * canvas.offsetHeight;
              
              return (
                <div
                  key={annotation.id}
                  className="absolute pointer-events-none"
                  style={{
                    left: x,
                    top: y,
                    width: width,
                    height: height,
                    backgroundColor: annotation.data.color || '#FFFF00',
                    opacity: annotation.data.opacity || 0.5,
                    zIndex: 5
                  }}
                />
              );
            }

            // Comment annotations (sticky notes) - Mobile optimized
            if (annotation.type === 'comment' && annotation.data.style === 'sticky') {
              return (
                <div
                  key={annotation.id}
                  className="absolute bg-yellow-200 border border-yellow-400 rounded p-1 shadow-sm max-w-[200px] lg:max-w-[300px]"
                  style={{
                    left: x,
                    top: y,
                    transform: 'translate(-50%, -100%)',
                    fontSize: `${Math.max(canvas.offsetHeight / 60, 10)}px`, // Responsive font size with minimum
                    lineHeight: '1.2',
                    zIndex: 15
                  }}
                >
                  <div className="text-xs break-words">
                    {annotation.data.text}
                  </div>
                </div>
              );
            }

            // Stamp annotations - Mobile optimized  
            if (annotation.type === 'stamp') {
              const stampText = getStampText(annotation.data.stampType);
              const stampColor = getStampColor(annotation.data.stampType);
              
              return (
                <div
                  key={annotation.id}
                  className="absolute pointer-events-none select-none"
                  style={{
                    left: x,
                    top: y,
                    transform: 'translate(-50%, -50%)',
                    backgroundColor: stampColor,
                    color: 'white',
                    padding: `${Math.max(canvas.offsetHeight / 200, 4)}px ${Math.max(canvas.offsetHeight / 100, 8)}px`, // Responsive padding
                    borderRadius: '4px',
                    fontWeight: 'bold',
                    fontSize: `${Math.max(canvas.offsetHeight / 50, 10)}px`, // Responsive font size with minimum
                    textAlign: 'center',
                    whiteSpace: 'nowrap',
                    border: `2px solid ${stampColor}`,
                    boxShadow: '0 2px 4px rgba(0,0,0,0.3)',
                    zIndex: 20,
                    // Ensure visibility on mobile
                    minWidth: '60px',
                    minHeight: '20px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}
                >
                  {stampText}
                </div>
              );
            }

            return null;
          })}

        {/* Selection Rectangle */}
        {isSelecting && selectionStart && selectionEnd && (
          <div
            className="absolute border-2 border-blue-500 bg-blue-200 bg-opacity-30 pointer-events-none"
            style={{
              left: Math.min(selectionStart.x, selectionEnd.x),
              top: Math.min(selectionStart.y, selectionEnd.y),
              width: Math.abs(selectionEnd.x - selectionStart.x),
              height: Math.abs(selectionEnd.y - selectionStart.y),
              zIndex: 25
            }}
          />
        )}
          </div>
        </div>
      </div>

      {/* Text Input Modal */}
      {textInput.visible && (
        <div
          className="fixed bg-white border border-gray-300 rounded shadow-lg p-2 z-50"
          style={{
            left: textInput.x,
            top: textInput.y,
            transform: 'translate(-50%, -100%)'
          }}
        >
          <input
            type="text"
            value={textInput.value}
            onChange={(e) => setTextInput(prev => ({ ...prev, value: e.target.value }))}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                handleTextSubmit(textInput.value);
              } else if (e.key === 'Escape') {
                setTextInput({ x: 0, y: 0, visible: false, value: '' });
              }
            }}
            placeholder="Enter text..."
            className="border border-gray-300 rounded px-2 py-1 text-sm w-48"
            autoFocus
          />
          <div className="flex mt-1 space-x-1">
            <button
              onClick={() => handleTextSubmit(textInput.value)}
              className="px-2 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700"
            >
              Add
            </button>
            <button
              onClick={() => setTextInput({ x: 0, y: 0, visible: false, value: '' })}
              className="px-2 py-1 bg-gray-300 text-gray-700 text-xs rounded hover:bg-gray-400"
            >
              Cancel
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

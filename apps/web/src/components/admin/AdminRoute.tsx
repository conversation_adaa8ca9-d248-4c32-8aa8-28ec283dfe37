import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuthStore } from '../../stores/auth';

interface AdminRouteProps {
  children: React.ReactNode;
  fallbackPath?: string;
}

const LoadingSpinner: React.FC = () => (
  <div className="flex items-center justify-center min-h-screen">
    <div className="flex flex-col items-center space-y-4">
      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      <p className="text-gray-600">Loading...</p>
    </div>
  </div>
);

const AccessDenied: React.FC = () => (
  <div className="flex items-center justify-center min-h-screen bg-gray-50">
    <div className="max-w-md w-full bg-white shadow-lg rounded-lg p-8 text-center">
      <div className="mb-6">
        <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
          <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.314 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        </div>
      </div>
      
      <h2 className="text-2xl font-bold text-gray-900 mb-4">
        Admin Access Required
      </h2>
      
      <p className="text-gray-600 mb-6">
        You don't have permission to access this administrative area. 
        Please contact your system administrator if you believe this is an error.
      </p>

      <div className="space-y-3">
        <button
          onClick={() => window.history.back()}
          className="w-full border border-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md hover:bg-gray-50 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        >
          Go Back
        </button>
        
        <button
          onClick={() => window.location.href = '/'}
          className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        >
          Return to Dashboard
        </button>
      </div>
    </div>
  </div>
);

export const AdminRoute: React.FC<AdminRouteProps> = ({
  children,
  fallbackPath = '/auth',
}) => {
  const { user, isLoading } = useAuthStore();
  const location = useLocation();

  // Show loading spinner while checking authentication
  if (isLoading) {
    return <LoadingSpinner />;
  }

  // Redirect to login if not authenticated
  if (!user) {
    return (
      <Navigate 
        to={fallbackPath} 
        state={{ from: location.pathname }} 
        replace 
      />
    );
  }

  // Check if user has admin role
  if (user.role !== 'admin') {
    return <AccessDenied />;
  }

  // All checks passed, render the admin content
  return <>{children}</>;
};

// Hook for checking admin permissions within components
export const useRequireAdmin = () => {
  const { user, isLoading } = useAuthStore();

  const isAuthenticated = Boolean(user);
  const isAdmin = user?.role === 'admin';

  return {
    user,
    isLoading,
    isAuthenticated,
    isAdmin,
    canAccessAdmin: isAuthenticated && isAdmin
  };
};

// Higher-order component for protecting admin components
export const withAdminProtection = <P extends object>(
  Component: React.ComponentType<P>
) => {
  const AdminProtectedComponent: React.FC<P> = (props) => (
    <AdminRoute>
      <Component {...props} />
    </AdminRoute>
  );

  AdminProtectedComponent.displayName = `withAdminProtection(${Component.displayName || Component.name})`;
  
  return AdminProtectedComponent;
};
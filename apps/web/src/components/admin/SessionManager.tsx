import React, { useState, useEffect } from 'react';
import { UserSession, SessionStats } from '../../../../../packages/shared/src/types';
import { trpc } from '../../utils/trpc';

interface SessionManagerProps {
  className?: string;
}

export function SessionManager({ className = '' }: SessionManagerProps) {
  const [sessions, setSessions] = useState<UserSession[]>([]);
  const [stats, setStats] = useState<SessionStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Query session data with real-time polling
  const { data: sessionData, isLoading, error: queryError } = trpc.admin.getSessions.useQuery(
    undefined,
    {
      refetchInterval: 30000, // Refresh every 30 seconds
      refetchIntervalInBackground: false, // Don't refresh when tab is not active
    }
  );
  const { data: sessionStats } = trpc.admin.getSessionStats.useQuery(
    undefined,
    {
      refetchInterval: 30000, // Refresh every 30 seconds
      refetchIntervalInBackground: false,
    }
  );
  
  // Mutations for session management
  const revokeSessionMutation = trpc.admin.revokeSession.useMutation({
    onSuccess: () => {
      // Refresh session data
      trpc.admin.getSessions.invalidate();
      trpc.admin.getSessionStats.invalidate();
    },
  });

  const revokeAllUserSessionsMutation = trpc.admin.revokeAllUserSessions.useMutation({
    onSuccess: () => {
      trpc.admin.getSessions.invalidate();
      trpc.admin.getSessionStats.invalidate();
    },
  });

  useEffect(() => {
    if (sessionData) {
      setSessions(sessionData);
      setLoading(false);
    }
    if (sessionStats) {
      setStats(sessionStats);
    }
    if (queryError) {
      setError(queryError.message);
      setLoading(false);
    }
  }, [sessionData, sessionStats, queryError]);

  const handleRevokeSession = async (sessionId: string) => {
    try {
      await revokeSessionMutation.mutateAsync({ sessionId });
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to revoke session');
    }
  };

  const handleRevokeAllUserSessions = async (userId: string) => {
    try {
      await revokeAllUserSessionsMutation.mutateAsync({ userId });
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to revoke user sessions');
    }
  };

  const formatDuration = (start: string, end?: string) => {
    const startTime = new Date(start);
    const endTime = end ? new Date(end) : new Date();
    const duration = endTime.getTime() - startTime.getTime();
    const hours = Math.floor(duration / (1000 * 60 * 60));
    const minutes = Math.floor((duration % (1000 * 60 * 60)) / (1000 * 60));
    return `${hours}h ${minutes}m`;
  };

  const getStatusColor = (status: UserSession['status']) => {
    switch (status) {
      case 'active': return 'text-green-600 bg-green-100';
      case 'expired': return 'text-yellow-600 bg-yellow-100';
      case 'revoked': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  if (loading || isLoading) {
    return (
      <div className={`p-6 ${className}`}>
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-300 rounded w-1/4"></div>
          <div className="space-y-2">
            <div className="h-4 bg-gray-300 rounded"></div>
            <div className="h-4 bg-gray-300 rounded w-3/4"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`p-6 ${className}`}>
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          <h3 className="font-semibold">Error Loading Sessions</h3>
          <p>{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`p-6 ${className}`}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Session Management</h2>
            <p className="text-gray-600 mt-1">Monitor and manage user sessions across the platform</p>
          </div>
          <div className="flex items-center space-x-2">
            <div className="flex items-center space-x-1">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-sm text-gray-500">Live updates every 30s</span>
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="bg-white p-4 rounded-lg border border-gray-200">
              <h3 className="text-sm font-medium text-gray-500">Active Sessions</h3>
              <p className="text-2xl font-bold text-gray-900">{stats.total_active_sessions}</p>
            </div>
            <div className="bg-white p-4 rounded-lg border border-gray-200">
              <h3 className="text-sm font-medium text-gray-500">Users Online</h3>
              <p className="text-2xl font-bold text-gray-900">{stats.total_users_online}</p>
            </div>
            <div className="bg-white p-4 rounded-lg border border-gray-200">
              <h3 className="text-sm font-medium text-gray-500">Avg Session Duration</h3>
              <p className="text-2xl font-bold text-gray-900">{Math.round(stats.average_session_duration / 60)}m</p>
            </div>
            <div className="bg-white p-4 rounded-lg border border-gray-200">
              <h3 className="text-sm font-medium text-gray-500">Sessions Today</h3>
              <p className="text-2xl font-bold text-gray-900">{stats.sessions_today}</p>
            </div>
          </div>
        )}

        {/* Sessions Table */}
        <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Active Sessions</h3>
          </div>
          
          {sessions.length === 0 ? (
            <div className="px-6 py-8 text-center text-gray-500">
              No active sessions found
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      User
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Duration
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Last Activity
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      IP Address
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {sessions.map((session) => (
                    <tr key={session.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {session.user_name}
                          </div>
                          <div className="text-sm text-gray-500">
                            {session.user_email}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(session.status)}`}>
                          {session.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatDuration(session.created_at, session.status === 'active' ? undefined : session.last_activity)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(session.last_activity).toLocaleString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {session.ip_address || 'N/A'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                        {session.status === 'active' && (
                          <>
                            <button
                              onClick={() => handleRevokeSession(session.id)}
                              disabled={revokeSessionMutation.isLoading}
                              className="text-red-600 hover:text-red-900 disabled:opacity-50"
                            >
                              Revoke
                            </button>
                            <button
                              onClick={() => handleRevokeAllUserSessions(session.user_id)}
                              disabled={revokeAllUserSessionsMutation.isLoading}
                              className="text-orange-600 hover:text-orange-900 disabled:opacity-50"
                            >
                              Revoke All
                            </button>
                          </>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
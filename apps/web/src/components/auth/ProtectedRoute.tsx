import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuthStore } from '../../stores/auth';
import type { User } from 'shared';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredTier?: 'free' | 'premium';
  fallbackPath?: string;
  upgradeComponent?: React.ComponentType;
}

const LoadingSpinner: React.FC = () => (
  <div className="flex items-center justify-center min-h-screen">
    <div className="flex flex-col items-center space-y-4">
      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      <p className="text-gray-600">Loading...</p>
    </div>
  </div>
);

const UpgradeRequired: React.FC<{
  requiredTier: string;
  currentTier: string;
  onUpgrade?: () => void;
}> = ({ requiredTier, currentTier, onUpgrade }) => (
  <div className="flex items-center justify-center min-h-screen bg-gray-50">
    <div className="max-w-md w-full bg-white shadow-lg rounded-lg p-8 text-center">
      <div className="mb-6">
        <div className="mx-auto w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center">
          <svg className="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m0 0v2m0-2h2m-2 0H9m3-9V6m0 0V4m0 2h2m-2 0H9M7 7l10 10M7 17L17 7" />
          </svg>
        </div>
      </div>
      
      <h2 className="text-2xl font-bold text-gray-900 mb-4">
        Premium Access Required
      </h2>
      
      <p className="text-gray-600 mb-6">
        This feature requires a {requiredTier} subscription. 
        Your current plan is {currentTier}.
      </p>

      <div className="space-y-3">
        <button
          onClick={onUpgrade}
          className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        >
          Upgrade to Premium
        </button>
        
        <button
          onClick={() => window.history.back()}
          className="w-full border border-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md hover:bg-gray-50 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        >
          Go Back
        </button>
      </div>
    </div>
  </div>
);

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredTier = 'free',
  fallbackPath = '/auth',
  upgradeComponent: UpgradeComponent
}) => {
  const { user, isLoading } = useAuthStore();
  const location = useLocation();

  // Show loading spinner while checking authentication
  if (isLoading) {
    return <LoadingSpinner />;
  }

  // Redirect to login if not authenticated
  if (!user) {
    return (
      <Navigate 
        to={fallbackPath} 
        state={{ from: location.pathname }} 
        replace 
      />
    );
  }

  // Check subscription tier requirements
  if (requiredTier === 'premium' && user.subscription_tier !== 'premium') {
    const handleUpgrade = () => {
      // Navigate to upgrade page or trigger upgrade flow
      window.location.href = '/upgrade';
    };

    if (UpgradeComponent) {
      return <UpgradeComponent />;
    }

    return (
      <UpgradeRequired 
        requiredTier={requiredTier}
        currentTier={user.subscription_tier}
        onUpgrade={handleUpgrade}
      />
    );
  }

  // All checks passed, render the protected content
  return <>{children}</>;
};

// Hook for checking user permissions within components
export const useRequireAuth = (requiredTier: 'free' | 'premium' = 'free') => {
  const { user, isLoading } = useAuthStore();

  const isAuthenticated = Boolean(user);
  const hasRequiredTier = user ? (
    requiredTier === 'free' || user.subscription_tier === 'premium'
  ) : false;

  return {
    user,
    isLoading,
    isAuthenticated,
    hasRequiredTier,
    canAccess: isAuthenticated && hasRequiredTier
  };
};

// Higher-order component for protecting class components
export const withProtection = <P extends object>(
  Component: React.ComponentType<P>,
  requiredTier: 'free' | 'premium' = 'free'
) => {
  const ProtectedComponent: React.FC<P> = (props) => (
    <ProtectedRoute requiredTier={requiredTier}>
      <Component {...props} />
    </ProtectedRoute>
  );

  ProtectedComponent.displayName = `withProtection(${Component.displayName || Component.name})`;
  
  return ProtectedComponent;
};
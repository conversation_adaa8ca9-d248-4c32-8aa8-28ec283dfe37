import React, { useState, useRef } from 'react';
import { clsx } from 'clsx';
import { useAuthStore } from '../../stores/auth';
import type { User } from 'shared';

interface UserProfileProps {
  onSuccess?: () => void;
  onDeleteAccount?: () => void;
  className?: string;
}

export const UserProfile: React.FC<UserProfileProps> = ({
  onSuccess,
  onDeleteAccount,
  className
}) => {
  const { user, updateProfile, logout } = useAuthStore();
  const [isEditing, setIsEditing] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [isDeletingAccount, setIsDeletingAccount] = useState(false);
  const [errors, setErrors] = useState<{ name?: string; avatar_url?: string }>({});
  const [successMessage, setSuccessMessage] = useState('');
  
  const [profileData, setProfileData] = useState({
    name: user?.name || '',
    avatar_url: user?.avatar_url || ''
  });

  const fileInputRef = useRef<HTMLInputElement>(null);

  if (!user) {
    return null;
  }

  const validateForm = (): boolean => {
    const newErrors: { name?: string; avatar_url?: string } = {};

    if (!profileData.name.trim()) {
      newErrors.name = 'Name is required';
    } else if (profileData.name.trim().length < 2) {
      newErrors.name = 'Name must be at least 2 characters';
    }

    if (profileData.avatar_url && !/^https?:\/\/.+\.(jpg|jpeg|png|gif|webp)$/i.test(profileData.avatar_url)) {
      newErrors.avatar_url = 'Please enter a valid image URL';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSaveProfile = async () => {
    setSuccessMessage('');
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      await updateProfile({
        name: profileData.name.trim(),
        avatar_url: profileData.avatar_url.trim() || null
      });
      
      setIsEditing(false);
      setSuccessMessage('Profile updated successfully!');
      onSuccess?.();
    } catch (error) {
      setErrors({ name: error instanceof Error ? error.message : 'Failed to update profile' });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancelEdit = () => {
    setProfileData({
      name: user.name,
      avatar_url: user.avatar_url || ''
    });
    setIsEditing(false);
    setErrors({});
    setSuccessMessage('');
  };

  const handleDeleteAccount = async () => {
    setIsDeletingAccount(true);
    
    try {
      // This would call the auth service deleteAccount method
      // For now, we'll just trigger the callback
      onDeleteAccount?.();
    } catch (error) {
      console.error('Failed to delete account:', error);
      // Handle error appropriately
    } finally {
      setIsDeletingAccount(false);
      setShowDeleteConfirm(false);
    }
  };

  const handleAvatarUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // For now, just show the file name (in a real app, you'd upload to a service)
    // This is a placeholder for avatar upload functionality
    setProfileData(prev => ({
      ...prev,
      avatar_url: `https://example.com/avatars/${file.name}` // Placeholder URL
    }));
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className={clsx('w-full max-w-2xl mx-auto', className)}>
      <div className="bg-white shadow-lg rounded-lg p-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">
          Account Settings
        </h2>

        {successMessage && (
          <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-md">
            <p className="text-sm text-green-600">{successMessage}</p>
          </div>
        )}

        <div className="space-y-6">
          {/* Profile Picture Section */}
          <div className="flex items-center space-x-6">
            <div className="relative">
              <img
                src={user.avatar_url || `https://ui-avatars.com/api/?name=${encodeURIComponent(user.name)}&background=3b82f6&color=ffffff`}
                alt="Profile"
                className="w-20 h-20 rounded-full object-cover border-4 border-gray-200"
              />
              {isEditing && (
                <button
                  type="button"
                  onClick={() => fileInputRef.current?.click()}
                  className="absolute bottom-0 right-0 bg-blue-600 text-white rounded-full p-1 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                </button>
              )}
            </div>
            
            <div className="flex-1">
              <h3 className="text-lg font-medium text-gray-900">{user.name}</h3>
              <p className="text-sm text-gray-600">{user.email}</p>
              <p className="text-sm text-gray-500">
                <span className={clsx(
                  'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
                  {
                    'bg-green-100 text-green-800': user.subscription_tier === 'premium',
                    'bg-gray-100 text-gray-800': user.subscription_tier === 'free'
                  }
                )}>
                  {user.subscription_tier === 'premium' ? '⭐ Premium' : '🆓 Free'}
                </span>
              </p>
            </div>

            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={handleAvatarUpload}
              className="hidden"
            />
          </div>

          {/* Profile Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Full Name
              </label>
              {isEditing ? (
                <input
                  type="text"
                  value={profileData.name}
                  onChange={(e) => setProfileData(prev => ({ ...prev, name: e.target.value }))}
                  className={clsx(
                    'w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500',
                    {
                      'border-gray-300': !errors.name,
                      'border-red-300 focus:border-red-500 focus:ring-red-500': errors.name
                    }
                  )}
                  disabled={isSubmitting}
                />
              ) : (
                <p className="py-2 px-3 bg-gray-50 rounded-md text-gray-900">{user.name}</p>
              )}
              {errors.name && (
                <p className="mt-1 text-sm text-red-600">{errors.name}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Email Address
              </label>
              <p className="py-2 px-3 bg-gray-50 rounded-md text-gray-900">{user.email}</p>
              <p className="mt-1 text-xs text-gray-500">Email cannot be changed</p>
            </div>

            {isEditing && (
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Avatar URL (optional)
                </label>
                <input
                  type="url"
                  value={profileData.avatar_url}
                  onChange={(e) => setProfileData(prev => ({ ...prev, avatar_url: e.target.value }))}
                  className={clsx(
                    'w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500',
                    {
                      'border-gray-300': !errors.avatar_url,
                      'border-red-300 focus:border-red-500 focus:ring-red-500': errors.avatar_url
                    }
                  )}
                  placeholder="https://example.com/your-avatar.jpg"
                  disabled={isSubmitting}
                />
                {errors.avatar_url && (
                  <p className="mt-1 text-sm text-red-600">{errors.avatar_url}</p>
                )}
              </div>
            )}
          </div>

          {/* Account Information */}
          <div className="border-t pt-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Account Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Member since:</span>
                <span className="ml-2 text-gray-900">{formatDate(user.created_at)}</span>
              </div>
              <div>
                <span className="text-gray-600">Last updated:</span>
                <span className="ml-2 text-gray-900">{formatDate(user.updated_at)}</span>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3 pt-6 border-t">
            {!isEditing ? (
              <>
                <button
                  onClick={() => setIsEditing(true)}
                  className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
                >
                  Edit Profile
                </button>
                
                <button
                  onClick={() => setShowDeleteConfirm(true)}
                  className="flex-1 bg-red-600 text-white py-2 px-4 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 transition-colors"
                >
                  Delete Account
                </button>
              </>
            ) : (
              <>
                <button
                  onClick={handleSaveProfile}
                  disabled={isSubmitting}
                  className={clsx(
                    'flex-1 py-2 px-4 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500',
                    {
                      'bg-blue-600 text-white hover:bg-blue-700': !isSubmitting,
                      'bg-gray-400 text-white cursor-not-allowed': isSubmitting
                    }
                  )}
                >
                  {isSubmitting ? 'Saving...' : 'Save Changes'}
                </button>
                
                <button
                  onClick={handleCancelEdit}
                  disabled={isSubmitting}
                  className="flex-1 border border-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors disabled:opacity-50"
                >
                  Cancel
                </button>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Delete Account Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-sm w-full mx-4">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Delete Account</h3>
            <p className="text-gray-600 mb-6">
              Are you sure you want to delete your account? This action cannot be undone and all your data will be permanently removed.
            </p>
            
            <div className="flex space-x-3">
              <button
                onClick={handleDeleteAccount}
                disabled={isDeletingAccount}
                className={clsx(
                  'flex-1 py-2 px-4 rounded-md text-white transition-colors focus:outline-none focus:ring-2 focus:ring-red-500',
                  {
                    'bg-red-600 hover:bg-red-700': !isDeletingAccount,
                    'bg-gray-400 cursor-not-allowed': isDeletingAccount
                  }
                )}
              >
                {isDeletingAccount ? 'Deleting...' : 'Delete Account'}
              </button>
              
              <button
                onClick={() => setShowDeleteConfirm(false)}
                disabled={isDeletingAccount}
                className="flex-1 border border-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors disabled:opacity-50"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
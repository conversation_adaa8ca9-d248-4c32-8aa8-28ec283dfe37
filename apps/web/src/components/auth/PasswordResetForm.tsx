import React, { useState } from 'react';
import { clsx } from 'clsx';
import { authService } from '../../services/auth.service';

interface PasswordResetFormProps {
  onSuccess?: () => void;
  onBackToLogin?: () => void;
  className?: string;
}

export const PasswordResetForm: React.FC<PasswordResetFormProps> = ({
  onSuccess,
  onBackToLogin,
  className
}) => {
  const [email, setEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [error, setError] = useState('');

  const validateEmail = (email: string): string | null => {
    if (!email) {
      return 'Email is required';
    }
    if (!/\S+@\S+\.\S+/.test(email)) {
      return 'Please enter a valid email address';
    }
    return null;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    const emailError = validateEmail(email);
    if (emailError) {
      setError(emailError);
      return;
    }

    setIsSubmitting(true);

    try {
      await authService.resetPassword(email);
      setIsSubmitted(true);
      onSuccess?.();
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to send reset email. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEmailChange = (value: string) => {
    setEmail(value);
    if (error) {
      setError('');
    }
  };

  const handleResendEmail = async () => {
    setError('');
    setIsSubmitting(true);

    try {
      await authService.resetPassword(email);
      setError(''); // Clear any previous errors on successful resend
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to resend email. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isSubmitted) {
    return (
      <div className={clsx('w-full max-w-md mx-auto', className)}>
        <div className="bg-white shadow-lg rounded-lg p-8 text-center">
          <div className="mb-4">
            <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
              <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
          </div>

          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            Check Your Email
          </h2>

          <p className="text-gray-600 mb-6">
            We've sent a password reset link to <strong>{email}</strong>. 
            Please check your email and follow the instructions to reset your password.
          </p>

          <div className="space-y-3">
            <button
              onClick={handleResendEmail}
              disabled={isSubmitting}
              className={clsx(
                'w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white',
                'focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500',
                {
                  'hover:bg-gray-50': !isSubmitting,
                  'cursor-not-allowed opacity-50': isSubmitting
                }
              )}
            >
              {isSubmitting ? 'Sending...' : 'Resend Email'}
            </button>

            <button
              onClick={onBackToLogin}
              className="w-full py-2 px-4 text-sm font-medium text-blue-600 hover:text-blue-500 focus:outline-none focus:underline"
            >
              Back to Sign In
            </button>
          </div>

          {error && (
            <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-sm text-red-600">{error}</p>
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className={clsx('w-full max-w-md mx-auto', className)}>
      <div className="bg-white shadow-lg rounded-lg p-8">
        <h2 className="text-2xl font-bold text-gray-900 text-center mb-2">
          Reset Password
        </h2>
        
        <p className="text-gray-600 text-center mb-6">
          Enter your email address and we'll send you a link to reset your password.
        </p>

        {error && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="reset-email" className="block text-sm font-medium text-gray-700 mb-1">
              Email Address
            </label>
            <input
              id="reset-email"
              type="email"
              value={email}
              onChange={(e) => handleEmailChange(e.target.value)}
              className={clsx(
                'w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500',
                {
                  'border-gray-300': !error,
                  'border-red-300 focus:border-red-500 focus:ring-red-500': error
                }
              )}
              placeholder="Enter your email address"
              disabled={isSubmitting}
              autoComplete="email"
              required
            />
          </div>

          <button
            type="submit"
            disabled={isSubmitting}
            className={clsx(
              'w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white',
              'focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500',
              {
                'bg-blue-600 hover:bg-blue-700': !isSubmitting,
                'bg-gray-400 cursor-not-allowed': isSubmitting
              }
            )}
          >
            {isSubmitting ? (
              <>
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="m100 50l-5.4-8.1c-.7-1.1-2.2-1.1-2.9 0L86.3 50H80c-1.1 0-2 .9-2 2s.9 2 2 2h8.5c.8 0 1.5-.5 1.8-1.2L94 46.7l3.7 6.1c.3.7 1 1.2 1.8 1.2H108c1.1 0 2-.9 2-2s-.9-2-2-2h-6.3z"></path>
                </svg>
                Sending...
              </>
            ) : (
              'Send Reset Link'
            )}
          </button>
        </form>

        <div className="mt-6 text-center">
          <button
            onClick={onBackToLogin}
            className="text-sm font-medium text-blue-600 hover:text-blue-500 focus:outline-none focus:underline"
            disabled={isSubmitting}
          >
            Back to Sign In
          </button>
        </div>
      </div>
    </div>
  );
};
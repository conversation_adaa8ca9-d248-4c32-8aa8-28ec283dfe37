/**
 * Memory management utilities for large PDF processing
 */

export interface MemoryInfo {
  usedJSHeapSize: number;
  totalJSHeapSize: number;
  jsHeapSizeLimit: number;
  isSupported: boolean;
}

export interface ProcessingChunk {
  data: Uint8Array;
  offset: number;
  size: number;
  isLast: boolean;
}

export class MemoryManager {
  private static instance: MemoryManager;
  
  // Memory thresholds
  private readonly HIGH_MEMORY_THRESHOLD = 0.8; // 80% of heap limit
  private readonly CRITICAL_MEMORY_THRESHOLD = 0.9; // 90% of heap limit
  private readonly DEFAULT_CHUNK_SIZE = 8 * 1024 * 1024; // 8MB chunks

  private constructor() {}

  public static getInstance(): MemoryManager {
    if (!MemoryManager.instance) {
      MemoryManager.instance = new MemoryManager();
    }
    return MemoryManager.instance;
  }

  /**
   * Check if SharedArrayBuffer is supported
   */
  public isSharedArrayBufferSupported(): boolean {
    return typeof SharedArrayBuffer !== 'undefined';
  }

  /**
   * Get current memory information
   */
  public getMemoryInfo(): MemoryInfo {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      return {
        usedJSHeapSize: memory.usedJSHeapSize,
        totalJSHeapSize: memory.totalJSHeapSize,
        jsHeapSizeLimit: memory.jsHeapSizeLimit,
        isSupported: true
      };
    }
    
    return {
      usedJSHeapSize: 0,
      totalJSHeapSize: 0,
      jsHeapSizeLimit: 0,
      isSupported: false
    };
  }

  /**
   * Check if memory pressure is high
   */
  public isMemoryPressureHigh(): boolean {
    const memory = this.getMemoryInfo();
    if (!memory.isSupported || memory.jsHeapSizeLimit === 0) {
      return false;
    }
    
    return memory.usedJSHeapSize / memory.jsHeapSizeLimit > this.HIGH_MEMORY_THRESHOLD;
  }

  /**
   * Check if memory pressure is critical
   */
  public isMemoryPressureCritical(): boolean {
    const memory = this.getMemoryInfo();
    if (!memory.isSupported || memory.jsHeapSizeLimit === 0) {
      return false;
    }
    
    return memory.usedJSHeapSize / memory.jsHeapSizeLimit > this.CRITICAL_MEMORY_THRESHOLD;
  }

  /**
   * Calculate optimal chunk size based on available memory and file size
   */
  public calculateOptimalChunkSize(fileSize: number): number {
    const memory = this.getMemoryInfo();
    
    if (!memory.isSupported) {
      return this.DEFAULT_CHUNK_SIZE;
    }

    const availableMemory = memory.jsHeapSizeLimit - memory.usedJSHeapSize;
    const safeMemory = availableMemory * 0.5; // Use only 50% of available memory
    
    // Calculate chunk size (should not exceed 25% of available memory)
    const maxChunkSize = Math.max(safeMemory * 0.25, 1024 * 1024); // Min 1MB
    const optimalChunkSize = Math.min(this.DEFAULT_CHUNK_SIZE, maxChunkSize);
    
    // For very large files, use smaller chunks
    if (fileSize > 100 * 1024 * 1024) { // Files > 100MB
      return Math.min(optimalChunkSize, 4 * 1024 * 1024); // Max 4MB chunks
    }
    
    return optimalChunkSize;
  }

  /**
   * Create a chunked processor for large files
   */
  public async *createChunkedProcessor(
    arrayBuffer: ArrayBuffer,
    chunkSize?: number
  ): AsyncGenerator<ProcessingChunk, void, unknown> {
    const size = chunkSize || this.calculateOptimalChunkSize(arrayBuffer.byteLength);
    const totalSize = arrayBuffer.byteLength;
    let offset = 0;

    while (offset < totalSize) {
      // Check memory pressure before creating each chunk
      if (this.isMemoryPressureCritical()) {
        // Wait for garbage collection and memory pressure to reduce
        await this.waitForMemoryRelief();
      }

      const remainingSize = totalSize - offset;
      const currentChunkSize = Math.min(size, remainingSize);
      const isLast = offset + currentChunkSize >= totalSize;
      
      // Create chunk data
      const chunkBuffer = arrayBuffer.slice(offset, offset + currentChunkSize);
      const chunkData = new Uint8Array(chunkBuffer);
      
      yield {
        data: chunkData,
        offset,
        size: currentChunkSize,
        isLast
      };
      
      offset += currentChunkSize;
    }
  }

  /**
   * Create SharedArrayBuffer if supported, fallback to regular ArrayBuffer
   */
  public createBuffer(size: number): ArrayBuffer | SharedArrayBuffer {
    if (this.isSharedArrayBufferSupported()) {
      try {
        return new SharedArrayBuffer(size);
      } catch (error) {
        console.warn('SharedArrayBuffer creation failed, falling back to ArrayBuffer:', error);
      }
    }
    
    return new ArrayBuffer(size);
  }

  /**
   * Force garbage collection if available
   */
  public forceGarbageCollection(): void {
    // Trigger garbage collection if available (mainly for development)
    if ('gc' in window && typeof (window as any).gc === 'function') {
      (window as any).gc();
    }
  }

  /**
   * Wait for memory pressure to reduce
   */
  public async waitForMemoryRelief(maxWaitMs: number = 5000): Promise<void> {
    const startTime = Date.now();
    
    while (this.isMemoryPressureCritical() && (Date.now() - startTime) < maxWaitMs) {
      // Force garbage collection
      this.forceGarbageCollection();
      
      // Wait a bit for GC to complete
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    if (this.isMemoryPressureCritical()) {
      throw new Error('Unable to reduce memory pressure within timeout');
    }
  }

  /**
   * Monitor memory usage during processing
   */
  public createMemoryMonitor(onMemoryPressure: (info: MemoryInfo) => void) {
    let monitoring = true;
    
    const monitor = async () => {
      while (monitoring) {
        const memoryInfo = this.getMemoryInfo();
        
        if (this.isMemoryPressureHigh()) {
          onMemoryPressure(memoryInfo);
        }
        
        // Check every 500ms
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    };
    
    monitor();
    
    return {
      stop: () => {
        monitoring = false;
      }
    };
  }

  /**
   * Get memory usage as a percentage
   */
  public getMemoryUsagePercentage(): number {
    const memory = this.getMemoryInfo();
    
    if (!memory.isSupported || memory.jsHeapSizeLimit === 0) {
      return 0;
    }
    
    return (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100;
  }

  /**
   * Get recommended maximum file size based on current memory
   */
  public getRecommendedMaxFileSize(): number {
    const memory = this.getMemoryInfo();
    
    if (!memory.isSupported) {
      return 100 * 1024 * 1024; // 100MB default
    }
    
    const availableMemory = memory.jsHeapSizeLimit - memory.usedJSHeapSize;
    
    // Recommend max file size as 25% of available memory
    // This allows room for processing overhead
    return Math.max(availableMemory * 0.25, 10 * 1024 * 1024); // Min 10MB
  }

  /**
   * Check if a file size is safe to process
   */
  public isFileSafeToProcess(fileSize: number): {
    safe: boolean;
    reason?: string;
    recommendedMaxSize?: number;
  } {
    const memory = this.getMemoryInfo();
    
    if (!memory.isSupported) {
      // Without memory info, use conservative limits
      if (fileSize > 100 * 1024 * 1024) { // 100MB
        return {
          safe: false,
          reason: 'File size exceeds safe processing limit (100MB) for this browser',
          recommendedMaxSize: 100 * 1024 * 1024
        };
      }
      return { safe: true };
    }

    const recommendedMax = this.getRecommendedMaxFileSize();
    
    if (fileSize > recommendedMax) {
      return {
        safe: false,
        reason: 'File size exceeds recommended limit based on available memory',
        recommendedMaxSize: recommendedMax
      };
    }

    if (this.isMemoryPressureCritical()) {
      return {
        safe: false,
        reason: 'Current memory usage is too high to safely process additional files',
        recommendedMaxSize: recommendedMax
      };
    }

    return { safe: true };
  }
}

// Export singleton instance
export const memoryManager = MemoryManager.getInstance();
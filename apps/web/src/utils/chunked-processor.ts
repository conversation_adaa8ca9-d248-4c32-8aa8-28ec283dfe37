import { memoryManager, type ProcessingChunk } from './memory-manager';

export interface ChunkedProcessingOptions {
  chunkSize?: number;
  maxConcurrentChunks?: number;
  onProgress?: (processed: number, total: number) => void;
  onChunkProcessed?: (chunk: ProcessingChunk, result: Uint8Array) => void;
}

export class ChunkedFileProcessor {
  private static instance: ChunkedFileProcessor;

  private constructor() {}

  public static getInstance(): ChunkedFileProcessor {
    if (!ChunkedFileProcessor.instance) {
      ChunkedFileProcessor.instance = new ChunkedFileProcessor();
    }
    return ChunkedFileProcessor.instance;
  }

  /**
   * Process a large file in chunks to manage memory usage
   */
  public async processFileInChunks(
    file: File,
    processor: (chunk: Uint8Array, isLast: boolean) => Promise<Uint8Array>,
    options: ChunkedProcessingOptions = {}
  ): Promise<Uint8Array> {
    const {
      chunkSize,
      maxConcurrentChunks = 2,
      onProgress,
      onChunkProcessed
    } = options;

    // Check if chunked processing is needed
    const safetyCheck = memoryManager.isFileSafeToProcess(file.size);
    if (safetyCheck.safe && file.size < 50 * 1024 * 1024) { // Less than 50MB
      // Process normally without chunking
      const arrayBuffer = await file.arrayBuffer();
      const data = new Uint8Array(arrayBuffer);
      return processor(data, true);
    }

    const arrayBuffer = await file.arrayBuffer();
    const actualChunkSize = chunkSize || memoryManager.calculateOptimalChunkSize(file.size);
    const chunks: ProcessingChunk[] = [];
    const processedChunks: Uint8Array[] = [];

    // Create chunks
    for await (const chunk of memoryManager.createChunkedProcessor(arrayBuffer, actualChunkSize)) {
      chunks.push(chunk);
    }

    let processedCount = 0;
    const totalChunks = chunks.length;

    // Process chunks in batches to manage memory
    for (let i = 0; i < chunks.length; i += maxConcurrentChunks) {
      const batch = chunks.slice(i, i + maxConcurrentChunks);
      
      // Wait for memory pressure to reduce if needed
      if (memoryManager.isMemoryPressureCritical()) {
        await memoryManager.waitForMemoryRelief();
      }

      // Process batch concurrently
      const batchPromises = batch.map(async (chunk, batchIndex) => {
        const globalIndex = i + batchIndex;
        const result = await processor(chunk.data, chunk.isLast);
        
        processedCount++;
        onProgress?.(processedCount, totalChunks);
        onChunkProcessed?.(chunk, result);
        
        return { index: globalIndex, result };
      });

      const batchResults = await Promise.all(batchPromises);
      
      // Store results in correct order
      for (const { index, result } of batchResults) {
        processedChunks[index] = result;
      }

      // Force garbage collection between batches
      if (i + maxConcurrentChunks < chunks.length) {
        memoryManager.forceGarbageCollection();
      }
    }

    // Combine all processed chunks
    return this.combineChunks(processedChunks);
  }

  /**
   * Process file with automatic chunking decision
   */
  public async processFileWithAutoChunking(
    file: File,
    processor: (data: Uint8Array, metadata: { isChunked: boolean; totalSize: number }) => Promise<Uint8Array>
  ): Promise<Uint8Array> {
    const shouldChunk = !memoryManager.isFileSafeToProcess(file.size).safe 
                      || file.size > 50 * 1024 * 1024; // 50MB threshold

    if (!shouldChunk) {
      // Process normally
      const arrayBuffer = await file.arrayBuffer();
      const data = new Uint8Array(arrayBuffer);
      return processor(data, { isChunked: false, totalSize: file.size });
    }

    // Use chunked processing
    return this.processFileInChunks(
      file,
      (chunk, _isLast) => processor(chunk, { isChunked: true, totalSize: file.size })
    );
  }

  /**
   * Combine processed chunks into a single Uint8Array
   */
  private combineChunks(chunks: Uint8Array[]): Uint8Array {
    // Calculate total size
    const totalSize = chunks.reduce((sum, chunk) => sum + chunk.length, 0);
    
    // Create combined buffer (use SharedArrayBuffer if supported)
    const buffer = memoryManager.createBuffer(totalSize);
    const combined = new Uint8Array(buffer);
    
    // Copy chunks into combined buffer
    let offset = 0;
    for (const chunk of chunks) {
      combined.set(chunk, offset);
      offset += chunk.length;
    }
    
    return combined;
  }

  /**
   * Estimate memory requirements for processing
   */
  public estimateMemoryRequirements(fileSize: number): {
    estimated: number;
    safe: boolean;
    recommendChunking: boolean;
  } {
    // Rough estimate: file size * 3 (original + processing overhead + result)
    const estimated = fileSize * 3;
    const memoryInfo = memoryManager.getMemoryInfo();
    
    if (!memoryInfo.isSupported) {
      return {
        estimated,
        safe: fileSize < 50 * 1024 * 1024, // Conservative 50MB
        recommendChunking: fileSize > 50 * 1024 * 1024
      };
    }

    const availableMemory = memoryInfo.jsHeapSizeLimit - memoryInfo.usedJSHeapSize;
    const safe = estimated < availableMemory * 0.5; // Use max 50% of available memory
    
    return {
      estimated,
      safe,
      recommendChunking: !safe || fileSize > 100 * 1024 * 1024 // 100MB threshold
    };
  }
}

// Export singleton instance
export const chunkedProcessor = ChunkedFileProcessor.getInstance();
# MuPDF WASM Files

This directory contains the MuPDF WebAssembly files used by the PDF editor.

## Current Status

The files in this directory are **placeholders** for development purposes. 
They contain valid WASM headers but no actual MuPDF functionality.

## Production Setup

To get real MuPDF WASM files for production:

### Option 1: Build from Source (Recommended)

1. Install Emscripten SDK:
   ```bash
   git clone https://github.com/emscripten-core/emsdk.git
   cd emsdk
   ./emsdk install latest
   ./emsdk activate latest
   source ./emsdk_env.sh
   ```

2. Clone and build MuPDF:
   ```bash
   git clone https://github.com/ArtifexSoftware/mupdf.git
   cd mupdf
   make HAVE_GLUT=no HAVE_X11=no wasm
   ```

3. Copy the generated files:
   ```bash
   cp build/wasm/mupdf.wasm /path/to/project/apps/web/public/assets/wasm/mupdf/mupdf-basic.wasm
   ```

### Option 2: Use Pre-built Binaries

Check the MuPDF releases page for pre-built WASM binaries:
https://github.com/ArtifexSoftware/mupdf/releases

## File Descriptions

- **mupdf-basic.wasm**: Standard build, compatible with all browsers
- **mupdf-simd.wasm**: SIMD optimized, requires SIMD support
- **mupdf-threads.wasm**: Multi-threaded, requires SharedArrayBuffer
- **mupdf-threads-simd.wasm**: Fully optimized, requires SIMD + threads

## Development Notes

The placeholder files allow the application to start without errors.
The WASM loader will detect that these are placeholders and fall back
to alternative PDF processing methods (pdf-lib) automatically.

## Security Note

Only use WASM files from trusted sources. Building from the official
MuPDF repository is the most secure approach.

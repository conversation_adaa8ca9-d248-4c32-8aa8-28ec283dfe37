#!/usr/bin/env node

/**
 * WASM File Verification Script
 * Checks if WASM files are real or placeholders
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

const wasmFiles = [
  'mupdf-basic.wasm',
  'mupdf-simd.wasm', 
  'mupdf-threads.wasm',
  'mupdf-threads-simd.wasm'
];

console.log('🔍 Verifying WASM files...\n');

wasmFiles.forEach(filename => {
  const filePath = path.join(__dirname, filename);
  
  if (!fs.existsSync(filePath)) {
    console.log(`❌ Missing: ${filename}`);
    return;
  }
  
  const fileBuffer = fs.readFileSync(filePath);
  const isPlaceholder = fileBuffer.slice(8).every(byte => byte === 0);
  
  if (isPlaceholder) {
    console.log(`🔶 Placeholder: ${filename} (${(fileBuffer.length / 1024 / 1024).toFixed(2)}MB)`);
  } else {
    console.log(`✅ Real WASM: ${filename} (${(fileBuffer.length / 1024 / 1024).toFixed(2)}MB)`);
  }
});

console.log('\n💡 Run `node setup-mupdf-wasm.js` to create placeholders');
console.log('💡 See README.md for production setup instructions');

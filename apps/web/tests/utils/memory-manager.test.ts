import { describe, it, expect, beforeEach, vi } from 'vitest';
import { memoryManager } from '../../src/utils/memory-manager';

describe('MemoryManager', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('getInstance', () => {
    it('should return the same instance (singleton)', () => {
      const manager1 = memoryManager;
      const manager2 = memoryManager;
      
      expect(manager1).toBe(manager2);
    });
  });

  describe('isSharedArrayBufferSupported', () => {
    it('should detect SharedArrayBuffer support', () => {
      const isSupported = memoryManager.isSharedArrayBufferSupported();
      expect(typeof isSupported).toBe('boolean');
    });
  });

  describe('getMemoryInfo', () => {
    it('should return memory information when supported', () => {
      // Mock performance.memory
      Object.defineProperty(performance, 'memory', {
        value: {
          usedJSHeapSize: 10000000,
          totalJSHeapSize: 20000000,
          jsHeapSizeLimit: 100000000
        },
        configurable: true
      });

      const memoryInfo = memoryManager.getMemoryInfo();
      
      expect(memoryInfo.isSupported).toBe(true);
      expect(memoryInfo.usedJSHeapSize).toBe(10000000);
      expect(memoryInfo.totalJSHeapSize).toBe(20000000);
      expect(memoryInfo.jsHeapSizeLimit).toBe(100000000);
    });

    it('should return default values when not supported', () => {
      // Remove memory property
      delete (performance as any).memory;

      const memoryInfo = memoryManager.getMemoryInfo();
      
      expect(memoryInfo.isSupported).toBe(false);
      expect(memoryInfo.usedJSHeapSize).toBe(0);
      expect(memoryInfo.totalJSHeapSize).toBe(0);
      expect(memoryInfo.jsHeapSizeLimit).toBe(0);
    });
  });

  describe('isMemoryPressureHigh', () => {
    it('should detect high memory pressure', () => {
      // Mock high memory usage (90% of limit)
      Object.defineProperty(performance, 'memory', {
        value: {
          usedJSHeapSize: 90000000,
          totalJSHeapSize: 95000000,
          jsHeapSizeLimit: 100000000
        },
        configurable: true
      });

      expect(memoryManager.isMemoryPressureHigh()).toBe(true);
    });

    it('should return false for low memory pressure', () => {
      // Mock low memory usage (50% of limit)
      Object.defineProperty(performance, 'memory', {
        value: {
          usedJSHeapSize: 50000000,
          totalJSHeapSize: 60000000,
          jsHeapSizeLimit: 100000000
        },
        configurable: true
      });

      expect(memoryManager.isMemoryPressureHigh()).toBe(false);
    });
  });

  describe('calculateOptimalChunkSize', () => {
    it('should calculate appropriate chunk size for small files', () => {
      // Mock available memory
      Object.defineProperty(performance, 'memory', {
        value: {
          usedJSHeapSize: 10000000,
          totalJSHeapSize: 20000000,
          jsHeapSizeLimit: 100000000
        },
        configurable: true
      });

      const fileSize = 10 * 1024 * 1024; // 10MB
      const chunkSize = memoryManager.calculateOptimalChunkSize(fileSize);
      
      expect(chunkSize).toBeGreaterThan(0);
      expect(chunkSize).toBeLessThanOrEqual(8 * 1024 * 1024); // Default max chunk size
    });

    it('should use smaller chunks for very large files', () => {
      const fileSize = 200 * 1024 * 1024; // 200MB
      const chunkSize = memoryManager.calculateOptimalChunkSize(fileSize);
      
      expect(chunkSize).toBeLessThanOrEqual(4 * 1024 * 1024); // 4MB max for large files
    });

    it('should fallback to default when memory info not available', () => {
      delete (performance as any).memory;

      const fileSize = 50 * 1024 * 1024; // 50MB
      const chunkSize = memoryManager.calculateOptimalChunkSize(fileSize);
      
      expect(chunkSize).toBe(8 * 1024 * 1024); // Default chunk size
    });
  });

  describe('createChunkedProcessor', () => {
    it('should create chunks for processing', async () => {
      const data = new Uint8Array(1000);
      data.fill(1); // Fill with test data
      
      const arrayBuffer = data.buffer;
      const chunkSize = 300;
      
      const chunks = [];
      for await (const chunk of memoryManager.createChunkedProcessor(arrayBuffer, chunkSize)) {
        chunks.push(chunk);
      }
      
      expect(chunks.length).toBeGreaterThan(1);
      expect(chunks[chunks.length - 1].isLast).toBe(true);
      
      // Verify total size
      const totalProcessedSize = chunks.reduce((sum, chunk) => sum + chunk.size, 0);
      expect(totalProcessedSize).toBe(arrayBuffer.byteLength);
    });
  });

  describe('createBuffer', () => {
    it('should create buffer successfully', () => {
      const buffer = memoryManager.createBuffer(1000);
      expect(buffer.byteLength).toBe(1000);
      // Buffer should be either ArrayBuffer or SharedArrayBuffer
      expect(buffer instanceof ArrayBuffer || buffer instanceof SharedArrayBuffer).toBe(true);
    });
  });

  describe('SharedArrayBuffer fallback testing', () => {
    it('should use SharedArrayBuffer when available', () => {
      // Ensure SharedArrayBuffer is available
      if (typeof SharedArrayBuffer !== 'undefined') {
        const buffer = memoryManager.createBuffer(1000);
        // Should prefer SharedArrayBuffer when available
        expect(buffer instanceof SharedArrayBuffer || buffer instanceof ArrayBuffer).toBe(true);
        expect(buffer.byteLength).toBe(1000);
      }
    });

    it('should fallback to ArrayBuffer when SharedArrayBuffer is not available', () => {
      // Temporarily hide SharedArrayBuffer
      const originalSharedArrayBuffer = (globalThis as any).SharedArrayBuffer;
      delete (globalThis as any).SharedArrayBuffer;

      try {
        const buffer = memoryManager.createBuffer(1000);
        expect(buffer instanceof ArrayBuffer).toBe(true);
        expect(buffer.byteLength).toBe(1000);
        expect(memoryManager.isSharedArrayBufferSupported()).toBe(false);
      } finally {
        // Restore SharedArrayBuffer
        if (originalSharedArrayBuffer) {
          (globalThis as any).SharedArrayBuffer = originalSharedArrayBuffer;
        }
      }
    });

    it('should fallback to ArrayBuffer when SharedArrayBuffer creation fails', () => {
      // Mock SharedArrayBuffer to throw an error
      const originalSharedArrayBuffer = (globalThis as any).SharedArrayBuffer;
      const mockSharedArrayBuffer = vi.fn(() => {
        throw new Error('SharedArrayBuffer creation failed');
      });
      
      (globalThis as any).SharedArrayBuffer = mockSharedArrayBuffer;

      try {
        const buffer = memoryManager.createBuffer(1000);
        expect(buffer instanceof ArrayBuffer).toBe(true);
        expect(buffer.byteLength).toBe(1000);
        expect(mockSharedArrayBuffer).toHaveBeenCalled();
      } finally {
        // Restore SharedArrayBuffer
        (globalThis as any).SharedArrayBuffer = originalSharedArrayBuffer;
      }
    });

    it('should handle chunked processing without SharedArrayBuffer', async () => {
      // Temporarily hide SharedArrayBuffer
      const originalSharedArrayBuffer = (globalThis as any).SharedArrayBuffer;
      delete (globalThis as any).SharedArrayBuffer;

      try {
        const data = new Uint8Array(2000);
        data.fill(1);
        
        const arrayBuffer = data.buffer;
        const chunkSize = 500;
        
        const chunks = [];
        for await (const chunk of memoryManager.createChunkedProcessor(arrayBuffer, chunkSize)) {
          chunks.push(chunk);
        }
        
        expect(chunks.length).toBe(4); // 2000 / 500 = 4 chunks
        expect(chunks[chunks.length - 1].isLast).toBe(true);
        
        // Verify all chunks are processed correctly without SharedArrayBuffer
        const totalProcessedSize = chunks.reduce((sum, chunk) => sum + chunk.size, 0);
        expect(totalProcessedSize).toBe(arrayBuffer.byteLength);
        
        // Verify that SharedArrayBuffer was not available during processing
        expect(memoryManager.isSharedArrayBufferSupported()).toBe(false);
      } finally {
        // Restore SharedArrayBuffer
        if (originalSharedArrayBuffer) {
          (globalThis as any).SharedArrayBuffer = originalSharedArrayBuffer;
        }
      }
    });

    it('should handle memory pressure with fallback processing', async () => {
      // Mock high memory pressure
      Object.defineProperty(performance, 'memory', {
        value: {
          usedJSHeapSize: 95000000,
          totalJSHeapSize: 98000000,
          jsHeapSizeLimit: 100000000
        },
        configurable: true
      });

      // Temporarily hide SharedArrayBuffer to test fallback under pressure
      const originalSharedArrayBuffer = (globalThis as any).SharedArrayBuffer;
      delete (globalThis as any).SharedArrayBuffer;

      try {
        expect(memoryManager.isMemoryPressureCritical()).toBe(true);
        expect(memoryManager.isSharedArrayBufferSupported()).toBe(false);
        
        const fileSize = 10 * 1024 * 1024; // 10MB
        const result = memoryManager.isFileSafeToProcess(fileSize);
        
        // Should reject due to high memory pressure, regardless of SharedArrayBuffer availability
        expect(result.safe).toBe(false);
        expect(result.reason).toContain('memory usage is too high');
      } finally {
        // Restore SharedArrayBuffer
        if (originalSharedArrayBuffer) {
          (globalThis as any).SharedArrayBuffer = originalSharedArrayBuffer;
        }
      }
    });
  });

  describe('isFileSafeToProcess', () => {
    it('should approve small files', () => {
      // Mock plenty of available memory
      Object.defineProperty(performance, 'memory', {
        value: {
          usedJSHeapSize: 10000000,
          totalJSHeapSize: 20000000,
          jsHeapSizeLimit: 1000000000 // 1GB limit
        },
        configurable: true
      });

      const fileSize = 10 * 1024 * 1024; // 10MB
      const result = memoryManager.isFileSafeToProcess(fileSize);
      
      expect(result.safe).toBe(true);
    });

    it('should reject files that exceed memory limits', () => {
      // Mock limited memory
      Object.defineProperty(performance, 'memory', {
        value: {
          usedJSHeapSize: 80000000,
          totalJSHeapSize: 90000000,
          jsHeapSizeLimit: 100000000 // 100MB limit
        },
        configurable: true
      });

      const fileSize = 50 * 1024 * 1024; // 50MB file with limited memory
      const result = memoryManager.isFileSafeToProcess(fileSize);
      
      expect(result.safe).toBe(false);
      expect(result.reason).toBeDefined();
    });

    it('should use conservative limits when memory info not available', () => {
      delete (performance as any).memory;

      const fileSize = 150 * 1024 * 1024; // 150MB
      const result = memoryManager.isFileSafeToProcess(fileSize);
      
      expect(result.safe).toBe(false);
      expect(result.recommendedMaxSize).toBe(100 * 1024 * 1024);
    });
  });

  describe('getMemoryUsagePercentage', () => {
    it('should calculate memory usage percentage correctly', () => {
      Object.defineProperty(performance, 'memory', {
        value: {
          usedJSHeapSize: 75000000,
          totalJSHeapSize: 80000000,
          jsHeapSizeLimit: 100000000
        },
        configurable: true
      });

      const percentage = memoryManager.getMemoryUsagePercentage();
      expect(percentage).toBe(75); // 75MB used out of 100MB limit
    });

    it('should return 0 when memory info not available', () => {
      delete (performance as any).memory;

      const percentage = memoryManager.getMemoryUsagePercentage();
      expect(percentage).toBe(0);
    });
  });
});
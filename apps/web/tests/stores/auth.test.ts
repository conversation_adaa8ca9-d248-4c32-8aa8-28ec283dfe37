import { describe, it, expect, vi, beforeEach } from 'vitest'
import { useAuthStore } from '../../src/stores/auth'
import type { LoginCredentials, RegisterCredentials } from 'shared'

// Mock auth service
const mockLogin = vi.fn()
const mockRegister = vi.fn()
const mockLogout = vi.fn()
const mockGetCurrentUser = vi.fn()
const mockUpdateProfile = vi.fn()

vi.mock('../../src/services/auth.service', () => ({
  authService: {
    login: mockLogin,
    register: mockRegister,
    logout: mockLogout,
    getCurrentUser: mockGetCurrentUser,
    onAuthStateChange: vi.fn(() => ({ unsubscribe: vi.fn() })),
  }
}))

describe('Auth Store', () => {
  beforeEach(() => {
    useAuthStore.setState({ user: null, session: null, isLoading: false })
    vi.clearAllMocks()
  })

  describe('login', () => {
    it('should set loading state during login', async () => {
      const credentials: LoginCredentials = {
        email: '<EMAIL>',
        password: 'password123'
      }

      const mockAuthResult = {
        user: {
          id: '123',
          email: '<EMAIL>',
          name: 'Test User',
          avatar_url: null,
          subscription_tier: 'free' as const,
          created_at: '2023-01-01T00:00:00Z',
          updated_at: '2023-01-01T00:00:00Z'
        },
        session: {
          access_token: 'mock-token',
          refresh_token: 'mock-refresh',
          expires_at: 1234567890,
          user: {} as any
        }
      }

      mockLogin.mockResolvedValue(mockAuthResult)

      const store = useAuthStore.getState()
      const loginPromise = store.login(credentials)

      // Check loading state is set
      expect(useAuthStore.getState().isLoading).toBe(true)

      await loginPromise

      // Check final state
      const finalState = useAuthStore.getState()
      expect(finalState.isLoading).toBe(false)
      expect(finalState.user).toEqual(mockAuthResult.user)
      expect(finalState.session).toEqual(mockAuthResult.session)
    })

    it('should handle login errors', async () => {
      const credentials: LoginCredentials = {
        email: '<EMAIL>',
        password: 'wrongpassword'
      }

      mockLogin.mockRejectedValue(new Error('Invalid credentials'))

      const store = useAuthStore.getState()

      await expect(store.login(credentials)).rejects.toThrow('Invalid credentials')
      
      // Should reset loading state on error
      expect(useAuthStore.getState().isLoading).toBe(false)
    })
  })

  describe('register', () => {
    it('should register successfully', async () => {
      const credentials: RegisterCredentials = {
        email: '<EMAIL>',
        password: 'password123',
        name: 'New User'
      }

      const mockAuthResult = {
        user: {
          id: '456',
          email: '<EMAIL>',
          name: 'New User',
          avatar_url: null,
          subscription_tier: 'free' as const,
          created_at: '2023-01-01T00:00:00Z',
          updated_at: '2023-01-01T00:00:00Z'
        },
        session: {
          access_token: 'mock-token',
          refresh_token: 'mock-refresh',
          expires_at: 1234567890,
          user: {} as any
        }
      }

      mockRegister.mockResolvedValue(mockAuthResult)

      const store = useAuthStore.getState()
      await store.register(credentials)

      const finalState = useAuthStore.getState()
      expect(finalState.user).toEqual(mockAuthResult.user)
      expect(finalState.session).toEqual(mockAuthResult.session)
      expect(finalState.isLoading).toBe(false)
    })
  })

  describe('logout', () => {
    it('should logout and clear state', async () => {
      // Set initial authenticated state
      const initialUser = {
        id: '123',
        email: '<EMAIL>',
        name: 'Test User',
        avatar_url: null,
        subscription_tier: 'free' as const,
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z'
      }

      useAuthStore.setState({ 
        user: initialUser, 
        session: {} as any, 
        isLoading: false 
      })

      mockLogout.mockResolvedValue(undefined)

      const store = useAuthStore.getState()
      await store.logout()

      const finalState = useAuthStore.getState()
      expect(finalState.user).toBeNull()
      expect(finalState.session).toBeNull()
      expect(finalState.isLoading).toBe(false)
    })
  })

  describe('checkAuth', () => {
    it('should check current auth status', async () => {
      const mockUser = {
        id: '123',
        email: '<EMAIL>',
        name: 'Test User',
        avatar_url: null,
        subscription_tier: 'free' as const,
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z'
      }

      mockGetCurrentUser.mockResolvedValue(mockUser)

      const store = useAuthStore.getState()
      await store.checkAuth()

      const finalState = useAuthStore.getState()
      expect(finalState.user).toEqual(mockUser)
      expect(finalState.isLoading).toBe(false)
    })

    it('should handle no current user', async () => {
      mockGetCurrentUser.mockResolvedValue(null)

      const store = useAuthStore.getState()
      await store.checkAuth()

      const finalState = useAuthStore.getState()
      expect(finalState.user).toBeNull()
      expect(finalState.isLoading).toBe(false)
    })
  })

  describe('updateProfile', () => {
    it('should update user profile', async () => {
      const initialUser = {
        id: '123',
        email: '<EMAIL>',
        name: 'Old Name',
        avatar_url: null,
        subscription_tier: 'free' as const,
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z'
      }

      useAuthStore.setState({ user: initialUser, session: {} as any, isLoading: false })

      const store = useAuthStore.getState()
      await store.updateProfile({ name: 'New Name' })

      const finalState = useAuthStore.getState()
      expect(finalState.user?.name).toBe('New Name')
      expect(finalState.user?.updated_at).not.toBe(initialUser.updated_at)
    })

    it('should throw error if no user logged in', async () => {
      useAuthStore.setState({ user: null, session: null, isLoading: false })

      const store = useAuthStore.getState()
      await expect(store.updateProfile({ name: 'New Name' })).rejects.toThrow('No user logged in')
    })
  })
})
import { describe, it, expect, beforeEach, vi } from 'vitest';

// Mock the worker import
vi.mock('../../src/workers/pdf-processor.worker?worker', () => {
  return {
    default: class MockWorker {
      private messageHandlers: ((event: MessageEvent) => void)[] = [];
      private errorHandlers: ((event: ErrorEvent) => void)[] = [];

      addEventListener(type: string, handler: any) {
        if (type === 'message') {
          this.messageHandlers.push(handler);
        } else if (type === 'error') {
          this.errorHandlers.push(handler);
        }
      }

      postMessage(message: any) {
        // Simulate worker processing
        setTimeout(() => {
          if (message.type === 'validate') {
            this.messageHandlers.forEach(handler => {
              handler({
                data: {
                  id: message.id,
                  type: 'success',
                  payload: { valid: true }
                }
              } as MessageEvent);
            });
          } else if (message.type === 'getInfo') {
            this.messageHandlers.forEach(handler => {
              handler({
                data: {
                  id: message.id,
                  type: 'success',
                  payload: {
                    pages: 5,
                    size: message.payload.file.byteLength,
                    title: 'Test Document',
                    author: 'Test Author'
                  }
                }
              } as MessageEvent);
            });
          } else if (message.type === 'process') {
            // Simulate progress updates
            this.messageHandlers.forEach(handler => {
              handler({
                data: {
                  id: message.id,
                  type: 'progress',
                  payload: {
                    stage: 'loading',
                    percentage: 25,
                    currentStep: 'Loading PDF...'
                  }
                }
              } as MessageEvent);
            });

            setTimeout(() => {
              this.messageHandlers.forEach(handler => {
                handler({
                  data: {
                    id: message.id,
                    type: 'progress',
                    payload: {
                      stage: 'processing',
                      percentage: 50,
                      currentStep: 'Processing...'
                    }
                  }
                } as MessageEvent);
              });
            }, 10);

            setTimeout(() => {
              this.messageHandlers.forEach(handler => {
                handler({
                  data: {
                    id: message.id,
                    type: 'success',
                    payload: {
                      data: new Uint8Array(1000),
                      originalSize: message.payload.file.byteLength,
                      processedSize: 1000,
                      compressionRatio: message.payload.file.byteLength / 1000,
                      processingTimeMs: 100,
                      metadata: {
                        pages: 5,
                        title: 'Test Document',
                        author: 'Test Author'
                      }
                    }
                  }
                } as MessageEvent);
              });
            }, 20);
          }
        }, 10);
      }

      terminate() {
        // Mock cleanup
      }
    }
  };
});

// Mock memory manager
vi.mock('../../src/utils/memory-manager', () => ({
  memoryManager: {
    isFileSafeToProcess: vi.fn((fileSize: number) => {
      // Allow files up to 20MB in tests (to accommodate the 10MB test file)
      return fileSize <= 20 * 1024 * 1024 ? { safe: true } : { 
        safe: false, 
        reason: 'File too large for available memory' 
      };
    }),
    createMemoryMonitor: vi.fn(() => ({ stop: vi.fn() })),
    getMemoryInfo: vi.fn(() => ({
      usedJSHeapSize: 10000000,
      totalJSHeapSize: 20000000,
      jsHeapSizeLimit: 100000000,
      isSupported: true
    })),
    isMemoryPressureHigh: vi.fn(() => false),
    forceGarbageCollection: vi.fn(),
    getMemoryUsagePercentage: vi.fn(() => 50),
    isSharedArrayBufferSupported: vi.fn(() => false)
  }
}));

import { pdfProcessor } from '../../src/services/pdf-processor';
import type { ProcessingOptions } from '../../src/services/pdf-processor';

describe('PDF Processing Integration', () => {
  let mockFile: File;

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Create a mock PDF file with all necessary methods
    const mockPDFData = new Uint8Array(2000);
    mockPDFData[0] = 0x25; // %
    mockPDFData[1] = 0x50; // P
    mockPDFData[2] = 0x44; // D
    mockPDFData[3] = 0x46; // F
    
    // Create a proper mock File object with all needed methods
    mockFile = {
      name: 'test.pdf',
      size: 2000,
      type: 'application/pdf',
      lastModified: Date.now(),
      arrayBuffer: async () => mockPDFData.buffer.slice(0, 2000),
      slice: (start = 0, end = mockPDFData.length) => {
        const slicedData = mockPDFData.slice(start, end);
        return {
          arrayBuffer: async () => slicedData.buffer.slice(0, slicedData.length),
          size: slicedData.length,
          type: 'application/pdf'
        } as Blob;
      },
      text: async () => '',
      stream: () => new ReadableStream()
    } as File;
  });

  describe('PDF Validation', () => {
    it('should validate a valid PDF file', async () => {
      const isValid = await pdfProcessor.validatePDF(mockFile);
      expect(isValid).toBe(true);
    });

    it('should reject files without PDF header', async () => {
      const invalidData = new Uint8Array([0x00, 0x00, 0x00, 0x00]);
      const invalidFile = {
        name: 'invalid.pdf',
        size: 4,
        type: 'application/pdf',
        lastModified: Date.now(),
        arrayBuffer: async () => invalidData.buffer.slice(0, 4),
        slice: (start = 0, end = invalidData.length) => {
          const slicedData = invalidData.slice(start, end);
          return {
            arrayBuffer: async () => slicedData.buffer.slice(0, slicedData.length),
            size: slicedData.length,
            type: 'application/pdf'
          } as Blob;
        },
        text: async () => '',
        stream: () => new ReadableStream()
      } as File;
      
      const isValid = await pdfProcessor.validatePDF(invalidFile);
      expect(isValid).toBe(false);
    });
  });

  describe('PDF Information Extraction', () => {
    it('should extract PDF information correctly', async () => {
      const info = await pdfProcessor.getPDFInfo(mockFile);
      
      expect(info.pages).toBe(5);
      expect(info.size).toBe(mockFile.size);
      expect(info.title).toBe('Test Document');
      expect(info.author).toBe('Test Author');
    });
  });

  describe('PDF Processing', () => {
    it('should process a PDF with progress callbacks', async () => {
      const progressCallbacks: any[] = [];
      const progressCallback = vi.fn((progress) => {
        progressCallbacks.push(progress);
      });

      const options: ProcessingOptions = {
        targetSize: 1000000,
        qualityLevel: 80
      };

      const result = await pdfProcessor.processDocument(mockFile, options, progressCallback);
      
      expect(result.data).toBeInstanceOf(Uint8Array);
      expect(result.originalSize).toBe(mockFile.size);
      expect(result.processedSize).toBeGreaterThan(0);
      expect(result.compressionRatio).toBeGreaterThan(0);
      expect(result.metadata?.pages).toBe(5);
      
      // Verify progress callbacks were called
      expect(progressCallback).toHaveBeenCalled();
      expect(progressCallbacks.length).toBeGreaterThan(0);
      
      // Check progress stages
      const stages = progressCallbacks.map(p => p.stage);
      expect(stages).toContain('loading');
      expect(stages).toContain('processing');
    });

    it('should handle memory safety checks', async () => {
      const { memoryManager } = await import('../../src/utils/memory-manager');
      
      // Mock unsafe file
      (memoryManager.isFileSafeToProcess as any).mockReturnValue({
        safe: false,
        reason: 'File too large for available memory'
      });

      await expect(
        pdfProcessor.processDocument(mockFile)
      ).rejects.toThrow('File too large for available memory');
    });

    it('should provide memory usage information', () => {
      const memoryInfo = pdfProcessor.getMemoryUsage();
      
      expect(memoryInfo.isSupported).toBe(true);
      expect(memoryInfo.usedJSHeapSize).toBe(10000000);
      expect(memoryInfo.totalJSHeapSize).toBe(20000000);
      expect(memoryInfo.jsHeapSizeLimit).toBe(100000000);
    });

    it('should check memory pressure', () => {
      const isHighPressure = pdfProcessor.checkMemoryPressure();
      expect(isHighPressure).toBe(false);
    });

    it('should get memory usage percentage', () => {
      const percentage = pdfProcessor.getMemoryUsagePercentage();
      expect(percentage).toBe(50);
    });

    it('should check SharedArrayBuffer support', () => {
      const isSupported = pdfProcessor.isSharedArrayBufferSupported();
      expect(typeof isSupported).toBe('boolean');
    });
  });

  describe('Error Handling', () => {
    it('should handle worker initialization errors gracefully', async () => {
      // This test verifies that the service handles worker failures
      // In a real scenario, the worker might fail to load
      
      const result = await pdfProcessor.validatePDF(mockFile);
      // Should still work with the mock
      expect(typeof result).toBe('boolean');
    });

    it('should clean up resources when disposed', () => {
      expect(() => {
        pdfProcessor.dispose();
      }).not.toThrow();
    });
  });

  describe('Large File Handling', () => {
    it('should handle large files with memory monitoring', async () => {
      // Explicitly mock the memory manager for this test to allow the large file
      const { memoryManager } = await import('../../src/utils/memory-manager');
      (memoryManager.isFileSafeToProcess as any).mockReturnValue({
        safe: true
      });
      
      // Create a larger mock file
      const largeData = new Uint8Array(10 * 1024 * 1024); // 10MB
      largeData[0] = 0x25; // %
      largeData[1] = 0x50; // P
      largeData[2] = 0x44; // D
      largeData[3] = 0x46; // F
      
      const largeFile = {
        name: 'large.pdf',
        size: 10 * 1024 * 1024,
        type: 'application/pdf',
        lastModified: Date.now(),
        arrayBuffer: async () => largeData.buffer.slice(0, 10 * 1024 * 1024),
        slice: (start = 0, end = largeData.length) => {
          const slicedData = largeData.slice(start, end);
          return {
            arrayBuffer: async () => slicedData.buffer.slice(0, slicedData.length),
            size: slicedData.length,
            type: 'application/pdf'
          } as Blob;
        },
        text: async () => '',
        stream: () => new ReadableStream()
      } as File;
      
      const result = await pdfProcessor.processDocument(largeFile);
      
      expect(result.data).toBeInstanceOf(Uint8Array);
      expect(result.originalSize).toBe(largeFile.size);
    });
  });
});
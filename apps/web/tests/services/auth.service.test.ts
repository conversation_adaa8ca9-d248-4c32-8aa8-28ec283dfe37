import { describe, it, expect, vi, beforeEach } from 'vitest'
import { AuthService } from '../../src/services/auth.service'
import type { LoginCredentials, RegisterCredentials } from 'shared'

// Mock Supabase
const mockSignInWithPassword = vi.fn()
const mockSignUp = vi.fn()
const mockSignOut = vi.fn()
const mockGetUser = vi.fn()
const mockRefreshSession = vi.fn()
const mockResetPasswordForEmail = vi.fn()
const mockUpdateUser = vi.fn()

vi.mock('../../src/services/supabase', () => ({
  supabase: {
    auth: {
      signInWithPassword: mockSignInWithPassword,
      signUp: mockSignUp,
      signOut: mockSignOut,
      getUser: mockGetUser,
      refreshSession: mockRefreshSession,
      resetPasswordForEmail: mockResetPasswordForEmail,
      updateUser: mockUpdateUser,
      onAuthStateChange: vi.fn(() => ({ unsubscribe: vi.fn() })),
    },
  },
}))

describe('AuthService', () => {
  let authService: AuthService
  
  beforeEach(() => {
    authService = new AuthService()
    vi.clearAllMocks()
  })

  describe('login', () => {
    it('should login successfully with valid credentials', async () => {
      const credentials: LoginCredentials = {
        email: '<EMAIL>',
        password: 'password123'
      }

      const mockResponse = {
        data: {
          user: {
            id: '123',
            email: '<EMAIL>',
            user_metadata: { name: 'Test User' },
            created_at: '2023-01-01T00:00:00Z',
            updated_at: '2023-01-01T00:00:00Z'
          },
          session: {
            access_token: 'mock-access-token',
            refresh_token: 'mock-refresh-token',
            expires_at: 1234567890
          }
        },
        error: null
      }

      mockSignInWithPassword.mockResolvedValue(mockResponse)

      const result = await authService.login(credentials)

      expect(mockSignInWithPassword).toHaveBeenCalledWith({
        email: credentials.email,
        password: credentials.password
      })
      
      expect(result.user.email).toBe('<EMAIL>')
      expect(result.session.access_token).toBe('mock-access-token')
    })

    it('should throw error on login failure', async () => {
      const credentials: LoginCredentials = {
        email: '<EMAIL>',
        password: 'wrongpassword'
      }

      mockSignInWithPassword.mockResolvedValue({
        data: { user: null, session: null },
        error: { message: 'Invalid credentials' }
      })

      await expect(authService.login(credentials)).rejects.toThrow('Invalid credentials')
    })
  })

  describe('register', () => {
    it('should register successfully with valid credentials', async () => {
      const credentials: RegisterCredentials = {
        email: '<EMAIL>',
        password: 'password123',
        name: 'New User'
      }

      const mockResponse = {
        data: {
          user: {
            id: '456',
            email: '<EMAIL>',
            user_metadata: { name: 'New User' },
            created_at: '2023-01-01T00:00:00Z',
            updated_at: '2023-01-01T00:00:00Z'
          },
          session: {
            access_token: 'mock-access-token',
            refresh_token: 'mock-refresh-token',
            expires_at: 1234567890
          }
        },
        error: null
      }

      mockSignUp.mockResolvedValue(mockResponse)

      const result = await authService.register(credentials)

      expect(mockSignUp).toHaveBeenCalledWith({
        email: credentials.email,
        password: credentials.password,
        options: {
          data: {
            name: credentials.name
          }
        }
      })
      
      expect(result.user.name).toBe('New User')
      expect(result.user.subscription_tier).toBe('free')
    })

    it('should throw error on registration failure', async () => {
      const credentials: RegisterCredentials = {
        email: 'invalid@email',
        password: 'short',
        name: 'Test'
      }

      mockSignUp.mockResolvedValue({
        data: { user: null, session: null },
        error: { message: 'Invalid email format' }
      })

      await expect(authService.register(credentials)).rejects.toThrow('Invalid email format')
    })
  })

  describe('logout', () => {
    it('should logout successfully', async () => {
      mockSignOut.mockResolvedValue({ error: null })

      await expect(authService.logout()).resolves.not.toThrow()
      expect(mockSignOut).toHaveBeenCalled()
    })

    it('should throw error on logout failure', async () => {
      mockSignOut.mockResolvedValue({ error: { message: 'Logout failed' } })

      await expect(authService.logout()).rejects.toThrow('Logout failed')
    })
  })

  describe('getCurrentUser', () => {
    it('should return current user', async () => {
      const mockUser = {
        id: '123',
        email: '<EMAIL>',
        user_metadata: { name: 'Test User' },
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z'
      }

      mockGetUser.mockResolvedValue({
        data: { user: mockUser },
        error: null
      })

      const result = await authService.getCurrentUser()

      expect(result).toBeTruthy()
      expect(result?.email).toBe('<EMAIL>')
    })

    it('should return null if no user', async () => {
      mockGetUser.mockResolvedValue({
        data: { user: null },
        error: { message: 'No user found' }
      })

      const result = await authService.getCurrentUser()
      expect(result).toBeNull()
    })
  })

  describe('refreshToken', () => {
    it('should refresh token successfully', async () => {
      mockRefreshSession.mockResolvedValue({
        data: {
          session: {
            access_token: 'new-access-token'
          }
        },
        error: null
      })

      const result = await authService.refreshToken()
      expect(result).toBe('new-access-token')
    })

    it('should throw error on refresh failure', async () => {
      mockRefreshSession.mockResolvedValue({
        data: { session: null },
        error: { message: 'Refresh failed' }
      })

      await expect(authService.refreshToken()).rejects.toThrow('Refresh failed')
    })
  })

  describe('resetPassword', () => {
    it('should send reset password email', async () => {
      mockResetPasswordForEmail.mockResolvedValue({ error: null })

      await expect(authService.resetPassword('<EMAIL>')).resolves.not.toThrow()
      expect(mockResetPasswordForEmail).toHaveBeenCalledWith(
        '<EMAIL>',
        { redirectTo: `${window.location.origin}/reset-password` }
      )
    })

    it('should throw error on reset failure', async () => {
      mockResetPasswordForEmail.mockResolvedValue({
        error: { message: 'Reset failed' }
      })

      await expect(authService.resetPassword('<EMAIL>')).rejects.toThrow('Reset failed')
    })
  })
})
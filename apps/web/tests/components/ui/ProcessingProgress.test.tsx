import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { ProcessingProgress } from '../../../src/components/ui/ProcessingProgress';

describe('ProcessingProgress', () => {
  const defaultProps = {
    stage: 'processing' as const,
    percentage: 50,
    currentStep: 'Processing document...'
  };

  it('should render with basic props', () => {
    render(<ProcessingProgress {...defaultProps} />);

    expect(screen.getByText('Processing PDF')).toBeInTheDocument();
    expect(screen.getByText('50% Complete')).toBeInTheDocument();
    expect(screen.getByText('Processing document...')).toBeInTheDocument();
  });

  it('should show progress bar with correct width', () => {
    render(<ProcessingProgress {...defaultProps} percentage={75} />);

    const progressBar = document.querySelector('.bg-blue-600');
    expect(progressBar).toHaveStyle({ width: '75%' });
  });

  it('should display estimated time remaining', () => {
    render(
      <ProcessingProgress 
        {...defaultProps}
        estimatedTimeRemaining={120}
      />
    );

    expect(screen.getByText('~2m 0s remaining')).toBeInTheDocument();
  });

  it('should format time correctly for seconds only', () => {
    render(
      <ProcessingProgress 
        {...defaultProps}
        estimatedTimeRemaining={45}
      />
    );

    expect(screen.getByText('~45s remaining')).toBeInTheDocument();
  });

  it('should show cancel button when enabled', () => {
    const mockOnCancel = vi.fn();
    
    render(
      <ProcessingProgress 
        {...defaultProps}
        showCancel={true}
        onCancel={mockOnCancel}
      />
    );

    const cancelButton = screen.getByLabelText('Cancel processing');
    expect(cancelButton).toBeInTheDocument();

    fireEvent.click(cancelButton);
    expect(mockOnCancel).toHaveBeenCalled();
  });

  it('should not show cancel button by default', () => {
    render(<ProcessingProgress {...defaultProps} />);

    expect(screen.queryByLabelText('Cancel processing')).not.toBeInTheDocument();
  });

  it('should highlight current stage correctly', () => {
    render(
      <ProcessingProgress 
        {...defaultProps}
        stage="compressing"
      />
    );

    // Current stage should have blue styling
    const stageIndicators = document.querySelectorAll('.text-blue-600');
    expect(stageIndicators.length).toBeGreaterThan(0);
  });

  it('should show all four stages', () => {
    render(<ProcessingProgress {...defaultProps} />);

    expect(screen.getByText('Loading')).toBeInTheDocument();
    expect(screen.getByText('Processing')).toBeInTheDocument();
    expect(screen.getByText('Compressing')).toBeInTheDocument();
    expect(screen.getByText('Finalizing')).toBeInTheDocument();
  });

  it('should handle 100% completion', () => {
    render(
      <ProcessingProgress 
        {...defaultProps}
        percentage={100}
        stage="finalizing"
        currentStep="Complete!"
      />
    );

    expect(screen.getByText('100% Complete')).toBeInTheDocument();
    expect(screen.getByText('Complete!')).toBeInTheDocument();
  });

  it('should handle 0% progress', () => {
    render(
      <ProcessingProgress 
        {...defaultProps}
        percentage={0}
        stage="loading"
        currentStep="Initializing..."
      />
    );

    expect(screen.getByText('0% Complete')).toBeInTheDocument();
    const progressBar = document.querySelector('.bg-blue-600');
    expect(progressBar).toHaveStyle({ width: '0%' });
  });

  it('should apply custom className', () => {
    render(
      <ProcessingProgress 
        {...defaultProps}
        className="custom-class"
      />
    );

    const container = document.querySelector('.custom-class');
    expect(container).toBeInTheDocument();
  });

  it('should show mobile step indicator', () => {
    render(<ProcessingProgress {...defaultProps} />);

    // Check for mobile step indicator (hidden by default but present in DOM)
    expect(screen.getByText(/Step \d+ of 4/)).toBeInTheDocument();
  });

  it('should handle edge cases in percentage', () => {
    // Test percentage over 100
    const { rerender } = render(
      <ProcessingProgress 
        {...defaultProps}
        percentage={150}
      />
    );

    const progressBar = document.querySelector('.bg-blue-600');
    expect(progressBar).toHaveStyle({ width: '100%' }); // Should be clamped to 100%

    // Test negative percentage - rerender with negative value
    rerender(
      <ProcessingProgress 
        {...defaultProps}
        percentage={-10}
      />
    );

    const progressBarNeg = document.querySelector('.bg-blue-600');
    expect(progressBarNeg).toHaveStyle({ width: '0%' }); // Should be clamped to 0%
  });
});
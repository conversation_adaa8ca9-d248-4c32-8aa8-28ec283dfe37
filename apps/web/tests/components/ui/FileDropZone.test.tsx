import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { FileDropZone } from '../../../src/components/ui/FileDropZone';

// Mock memory manager
vi.mock('../../../src/utils/memory-manager', () => ({
  memoryManager: {
    isFileSafeToProcess: vi.fn(() => ({ safe: true }))
  }
}));

describe('FileDropZone', () => {
  const mockOnFileSelect = vi.fn();
  const mockOnValidationError = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render with default props', () => {
    render(
      <FileDropZone 
        onFileSelect={mockOnFileSelect}
        onValidationError={mockOnValidationError}
      />
    );

    expect(screen.getByText('Select or drop a PDF file')).toBeInTheDocument();
    expect(screen.getByText('Up to 4096MB')).toBeInTheDocument();
  });

  it('should render custom max size', () => {
    const maxSize = 100 * 1024 * 1024; // 100MB
    
    render(
      <FileDropZone 
        onFileSelect={mockOnFileSelect}
        onValidationError={mockOnValidationError}
        maxSize={maxSize}
      />
    );

    expect(screen.getByText('Up to 100MB')).toBeInTheDocument();
  });

  it('should show disabled state', () => {
    render(
      <FileDropZone 
        onFileSelect={mockOnFileSelect}
        onValidationError={mockOnValidationError}
        disabled={true}
      />
    );

    const dropZone = screen.getByLabelText('File drop zone');
    expect(dropZone).toHaveClass('cursor-not-allowed');
  });

  it('should handle file selection via input', async () => {
    // Create a mock PDF file with arrayBuffer method for validation
    const mockPDFData = new Uint8Array([0x25, 0x50, 0x44, 0x46]); // %PDF header
    const file = {
      name: 'test.pdf',
      size: 4,
      type: 'application/pdf',
      arrayBuffer: async () => mockPDFData.buffer,
      slice: (start = 0, end = 4) => ({
        arrayBuffer: async () => mockPDFData.slice(start, end).buffer
      })
    } as File;

    render(
      <FileDropZone 
        onFileSelect={mockOnFileSelect}
        onValidationError={mockOnValidationError}
      />
    );

    const fileInput = screen.getByLabelText('File drop zone').querySelector('input[type="file"]') as HTMLInputElement;
    
    // Mock the file input change
    Object.defineProperty(fileInput, 'files', {
      value: [file],
      writable: false,
    });

    fireEvent.change(fileInput);

    await waitFor(() => {
      expect(mockOnFileSelect).toHaveBeenCalledWith(file);
    }, { timeout: 3000 });
  });

  it('should reject files that are too large', async () => {
    // Create a mock large file without actually creating a 5GB string
    const largeFile = {
      name: 'large.pdf',
      size: 5000000000, // 5GB
      type: 'application/pdf',
      arrayBuffer: async () => new ArrayBuffer(0),
      slice: () => ({ arrayBuffer: async () => new ArrayBuffer(0) })
    } as File;

    render(
      <FileDropZone 
        onFileSelect={mockOnFileSelect}
        onValidationError={mockOnValidationError}
        maxSize={1024 * 1024} // 1MB limit
      />
    );

    const fileInput = screen.getByLabelText('File drop zone').querySelector('input[type="file"]') as HTMLInputElement;
    
    Object.defineProperty(fileInput, 'files', {
      value: [largeFile],
      writable: false,
    });

    fireEvent.change(fileInput);

    await waitFor(() => {
      expect(mockOnValidationError).toHaveBeenCalledWith('File size exceeds 1MB limit');
      expect(mockOnFileSelect).not.toHaveBeenCalled();
    });
  });

  it('should reject non-PDF files', async () => {
    const textFile = new File(['hello world'], 'test.txt', { type: 'text/plain' });

    render(
      <FileDropZone 
        onFileSelect={mockOnFileSelect}
        onValidationError={mockOnValidationError}
      />
    );

    const fileInput = screen.getByLabelText('File drop zone').querySelector('input[type="file"]') as HTMLInputElement;
    
    Object.defineProperty(fileInput, 'files', {
      value: [textFile],
      writable: false,
    });

    fireEvent.change(fileInput);

    await waitFor(() => {
      expect(mockOnValidationError).toHaveBeenCalledWith('Please select a valid PDF file');
      expect(mockOnFileSelect).not.toHaveBeenCalled();
    });
  });

  it('should reject empty files', async () => {
    const emptyFile = new File([], 'empty.pdf', { type: 'application/pdf' });

    render(
      <FileDropZone 
        onFileSelect={mockOnFileSelect}
        onValidationError={mockOnValidationError}
      />
    );

    const fileInput = screen.getByLabelText('File drop zone').querySelector('input[type="file"]') as HTMLInputElement;
    
    Object.defineProperty(fileInput, 'files', {
      value: [emptyFile],
      writable: false,
    });

    fireEvent.change(fileInput);

    await waitFor(() => {
      expect(mockOnValidationError).toHaveBeenCalledWith('File appears to be empty');
      expect(mockOnFileSelect).not.toHaveBeenCalled();
    });
  });

  it('should handle drag and drop', async () => {
    const mockPDFData = new Uint8Array([0x25, 0x50, 0x44, 0x46]); // %PDF header
    const file = {
      name: 'test.pdf',
      size: 4,
      type: 'application/pdf',
      arrayBuffer: async () => mockPDFData.buffer,
      slice: (start = 0, end = 4) => ({
        arrayBuffer: async () => mockPDFData.slice(start, end).buffer
      })
    } as File;

    render(
      <FileDropZone 
        onFileSelect={mockOnFileSelect}
        onValidationError={mockOnValidationError}
      />
    );

    const dropZone = screen.getByLabelText('File drop zone');

    // Simulate drag over
    fireEvent.dragOver(dropZone, {
      dataTransfer: {
        files: [file]
      }
    });

    expect(dropZone).toHaveClass('border-blue-500');

    // Simulate drop
    fireEvent.drop(dropZone, {
      dataTransfer: {
        files: [file]
      }
    });

    await waitFor(() => {
      expect(mockOnFileSelect).toHaveBeenCalledWith(file);
    }, { timeout: 3000 });
  });

  it('should show processing state', () => {
    render(
      <FileDropZone 
        onFileSelect={mockOnFileSelect}
        onValidationError={mockOnValidationError}
      />
    );

    // The processing state would be triggered by file selection
    // This test verifies the UI elements exist for the processing state
    expect(screen.getByText('Choose File')).toBeInTheDocument();
  });

  it('should handle memory safety validation', async () => {
    const { memoryManager } = await import('../../../src/utils/memory-manager');
    
    // Mock unsafe file
    (memoryManager.isFileSafeToProcess as any).mockReturnValue({
      safe: false,
      reason: 'File too large for available memory',
      recommendedMaxSize: 50 * 1024 * 1024
    });

    const mockPDFData = new Uint8Array([0x25, 0x50, 0x44, 0x46]);
    const file = new File([mockPDFData], 'large.pdf', { type: 'application/pdf' });

    render(
      <FileDropZone 
        onFileSelect={mockOnFileSelect}
        onValidationError={mockOnValidationError}
      />
    );

    const fileInput = screen.getByLabelText('File drop zone').querySelector('input[type="file"]') as HTMLInputElement;
    
    Object.defineProperty(fileInput, 'files', {
      value: [file],
      writable: false,
    });

    fireEvent.change(fileInput);

    await waitFor(() => {
      expect(mockOnValidationError).toHaveBeenCalledWith(
        'File too large for available memory Recommended max size: 50MB'
      );
      expect(mockOnFileSelect).not.toHaveBeenCalled();
    });
  });
});
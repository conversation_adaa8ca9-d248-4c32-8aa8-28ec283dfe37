import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { Header } from '@/components/layout/Header';

describe('Header', () => {
  it('renders app title and menu button', () => {
    const mockToggle = vi.fn();
    
    render(
      <Header 
        onMenuToggle={mockToggle} 
        isMenuOpen={false} 
      />
    );

    expect(screen.getByText('MobilePDF Pro')).toBeInTheDocument();
    expect(screen.getByLabelText('Open menu')).toBeInTheDocument();
    expect(screen.getByLabelText('User menu')).toBeInTheDocument();
  });

  it('calls onMenuToggle when menu button is clicked', () => {
    const mockToggle = vi.fn();
    
    render(
      <Header 
        onMenuToggle={mockToggle} 
        isMenuOpen={false} 
      />
    );

    fireEvent.click(screen.getByLabelText('Open menu'));
    expect(mockToggle).toHaveBeenCalledOnce();
  });

  it('shows close icon when menu is open', () => {
    const mockToggle = vi.fn();
    
    render(
      <Header 
        onMenuToggle={mockToggle} 
        isMenuOpen={true} 
      />
    );

    const button = screen.getByLabelText('Open menu');
    expect(button).toBeInTheDocument();
  });
});
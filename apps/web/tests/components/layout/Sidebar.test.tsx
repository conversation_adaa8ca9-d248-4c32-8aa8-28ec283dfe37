import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import { Sidebar } from '@/components/layout/Sidebar';

const renderWithRouter = (component: React.ReactElement, initialRoute = '/') => {
  return render(
    <MemoryRouter initialEntries={[initialRoute]}>
      {component}
    </MemoryRouter>
  );
};

describe('Sidebar', () => {
  it('renders navigation items', () => {
    const mockClose = vi.fn();
    
    renderWithRouter(
      <Sidebar isOpen={true} onClose={mockClose} />
    );

    expect(screen.getByText('Dashboard')).toBeInTheDocument();
    expect(screen.getByText('Process PDFs')).toBeInTheDocument();
    expect(screen.getByText('History')).toBeInTheDocument();
    expect(screen.getByText('Settings')).toBeInTheDocument();
  });

  it('highlights active navigation item', () => {
    const mockClose = vi.fn();
    
    renderWithRouter(
      <Sidebar isOpen={true} onClose={mockClose} />,
      '/dashboard'
    );

    const dashboardLink = screen.getByText('Dashboard').closest('a');
    expect(dashboardLink).toHaveClass('bg-blue-50', 'text-blue-700');
  });

  it('calls onClose when close button is clicked on mobile', () => {
    const mockClose = vi.fn();
    
    renderWithRouter(
      <Sidebar isOpen={true} onClose={mockClose} />
    );

    const closeButton = screen.getByLabelText('Close menu');
    fireEvent.click(closeButton);
    expect(mockClose).toHaveBeenCalledOnce();
  });

  it('applies correct transform classes based on isOpen prop', () => {
    const mockClose = vi.fn();
    
    const { rerender } = renderWithRouter(
      <Sidebar isOpen={false} onClose={mockClose} />
    );

    let sidebar = screen.getByText('Dashboard').closest('div[class*="transform"]');
    expect(sidebar).toHaveClass('-translate-x-full');

    rerender(
      <MemoryRouter>
        <Sidebar isOpen={true} onClose={mockClose} />
      </MemoryRouter>
    );

    sidebar = screen.getByText('Dashboard').closest('div[class*="transform"]');
    expect(sidebar).toHaveClass('translate-x-0');
  });
});
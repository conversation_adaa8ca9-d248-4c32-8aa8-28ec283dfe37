import { describe, it, expect, beforeEach, vi } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import '@testing-library/jest-dom'
import { SessionManager } from '../../../src/components/admin/SessionManager'
import type { UserSession, SessionStats } from '../../../../../packages/shared/src/types'

// Mock tRPC
const mockTrpc = {
  admin: {
    getSessions: {
      useQuery: vi.fn(),
      invalidate: vi.fn(),
    },
    getSessionStats: {
      useQuery: vi.fn(),
      invalidate: vi.fn(),
    },
    revokeSession: {
      useMutation: vi.fn(),
    },
    revokeAllUserSessions: {
      useMutation: vi.fn(),
    },
  },
}

vi.mock('../../utils/trpc', () => ({
  trpc: mockTrpc,
}))

// Mock data
const mockSessions: UserSession[] = [
  {
    id: 'session-1',
    user_id: 'user-1',
    user_email: '<EMAIL>',
    user_name: '<PERSON>',
    created_at: new Date(Date.now() - 1000 * 60 * 30).toISOString(), // 30 minutes ago
    last_activity: new Date(Date.now() - 1000 * 60 * 5).toISOString(), // 5 minutes ago
    expires_at: new Date(Date.now() + 1000 * 60 * 60 * 23).toISOString(), // 23 hours from now
    ip_address: '***********',
    status: 'active',
  },
  {
    id: 'session-2',
    user_id: 'user-2',
    user_email: '<EMAIL>',
    user_name: 'Jane Smith',
    created_at: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(), // 2 hours ago
    last_activity: new Date(Date.now() - 1000 * 60 * 10).toISOString(), // 10 minutes ago
    expires_at: new Date(Date.now() + 1000 * 60 * 60 * 22).toISOString(), // 22 hours from now
    status: 'active',
  },
]

const mockStats: SessionStats = {
  total_active_sessions: 2,
  total_users_online: 2,
  average_session_duration: 3600, // 1 hour in seconds
  sessions_today: 5,
}

describe('SessionManager', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should render loading state initially', () => {
    mockTrpc.admin.getSessions.useQuery.mockReturnValue({
      data: undefined,
      isLoading: true,
      error: null,
    })
    mockTrpc.admin.getSessionStats.useQuery.mockReturnValue({
      data: undefined,
    })
    
    mockTrpc.admin.revokeSession.useMutation.mockReturnValue({
      mutateAsync: vi.fn(),
      isLoading: false,
    })
    
    mockTrpc.admin.revokeAllUserSessions.useMutation.mockReturnValue({
      mutateAsync: vi.fn(),
      isLoading: false,
    })

    render(<SessionManager />)
    
    expect(screen.getByText('Loading...')).toBeInTheDocument()
  })

  it('should render error state when query fails', () => {
    mockTrpc.admin.getSessions.useQuery.mockReturnValue({
      data: undefined,
      isLoading: false,
      error: { message: 'Failed to fetch sessions' },
    })
    mockTrpc.admin.getSessionStats.useQuery.mockReturnValue({
      data: undefined,
    })
    
    mockTrpc.admin.revokeSession.useMutation.mockReturnValue({
      mutateAsync: vi.fn(),
      isLoading: false,
    })
    
    mockTrpc.admin.revokeAllUserSessions.useMutation.mockReturnValue({
      mutateAsync: vi.fn(),
      isLoading: false,
    })

    render(<SessionManager />)
    
    expect(screen.getByText('Error Loading Sessions')).toBeInTheDocument()
    expect(screen.getByText('Failed to fetch sessions')).toBeInTheDocument()
  })

  it('should render session data correctly', () => {
    mockTrpc.admin.getSessions.useQuery.mockReturnValue({
      data: mockSessions,
      isLoading: false,
      error: null,
    })
    mockTrpc.admin.getSessionStats.useQuery.mockReturnValue({
      data: mockStats,
    })
    
    mockTrpc.admin.revokeSession.useMutation.mockReturnValue({
      mutateAsync: vi.fn(),
      isLoading: false,
    })
    
    mockTrpc.admin.revokeAllUserSessions.useMutation.mockReturnValue({
      mutateAsync: vi.fn(),
      isLoading: false,
    })

    render(<SessionManager />)
    
    expect(screen.getByText('Session Management')).toBeInTheDocument()
    expect(screen.getByText('Live updates every 30s')).toBeInTheDocument()
    
    // Check stats cards
    expect(screen.getByText('2')).toBeInTheDocument() // Active sessions
    expect(screen.getByText('60m')).toBeInTheDocument() // Average duration
    expect(screen.getByText('5')).toBeInTheDocument() // Sessions today
    
    // Check session table
    expect(screen.getByText('John Doe')).toBeInTheDocument()
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
    expect(screen.getByText('Jane Smith')).toBeInTheDocument()
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
    
    // Check IP addresses
    expect(screen.getByText('***********')).toBeInTheDocument()
    expect(screen.getByText('N/A')).toBeInTheDocument() // For session without IP
  })

  it('should handle session revocation', async () => {
    const mockRevoke = vi.fn().mockResolvedValue({})
    
    mockTrpc.admin.getSessions.useQuery.mockReturnValue({
      data: mockSessions,
      isLoading: false,
      error: null,
    })
    mockTrpc.admin.getSessionStats.useQuery.mockReturnValue({
      data: mockStats,
    })
    
    mockTrpc.admin.revokeSession.useMutation.mockReturnValue({
      mutateAsync: mockRevoke,
      isLoading: false,
    })
    
    mockTrpc.admin.revokeAllUserSessions.useMutation.mockReturnValue({
      mutateAsync: vi.fn(),
      isLoading: false,
    })

    render(<SessionManager />)
    
    const revokeButtons = screen.getAllByText('Revoke')
    fireEvent.click(revokeButtons[0])
    
    await waitFor(() => {
      expect(mockRevoke).toHaveBeenCalledWith({ sessionId: 'session-1' })
    })
  })

  it('should handle revoke all user sessions', async () => {
    const mockRevokeAll = vi.fn().mockResolvedValue({})
    
    mockTrpc.admin.getSessions.useQuery.mockReturnValue({
      data: mockSessions,
      isLoading: false,
      error: null,
    })
    mockTrpc.admin.getSessionStats.useQuery.mockReturnValue({
      data: mockStats,
    })
    
    mockTrpc.admin.revokeSession.useMutation.mockReturnValue({
      mutateAsync: vi.fn(),
      isLoading: false,
    })
    
    mockTrpc.admin.revokeAllUserSessions.useMutation.mockReturnValue({
      mutateAsync: mockRevokeAll,
      isLoading: false,
    })

    render(<SessionManager />)
    
    const revokeAllButtons = screen.getAllByText('Revoke All')
    fireEvent.click(revokeAllButtons[0])
    
    await waitFor(() => {
      expect(mockRevokeAll).toHaveBeenCalledWith({ userId: 'user-1' })
    })
  })

  it('should format session duration correctly', () => {
    const sessionWithLongDuration: UserSession[] = [
      {
        ...mockSessions[0],
        created_at: new Date(Date.now() - 1000 * 60 * 60 * 3 - 1000 * 60 * 45).toISOString(), // 3h 45m ago
      },
    ]
    
    mockTrpc.admin.getSessions.useQuery.mockReturnValue({
      data: sessionWithLongDuration,
      isLoading: false,
      error: null,
    })
    mockTrpc.admin.getSessionStats.useQuery.mockReturnValue({
      data: mockStats,
    })
    
    mockTrpc.admin.revokeSession.useMutation.mockReturnValue({
      mutateAsync: vi.fn(),
      isLoading: false,
    })
    
    mockTrpc.admin.revokeAllUserSessions.useMutation.mockReturnValue({
      mutateAsync: vi.fn(),
      isLoading: false,
    })

    render(<SessionManager />)
    
    expect(screen.getByText('3h 45m')).toBeInTheDocument()
  })

  it('should show correct status colors', () => {
    const sessionsWithDifferentStatuses: UserSession[] = [
      { ...mockSessions[0], status: 'active' },
      { ...mockSessions[1], status: 'expired' },
    ]
    
    mockTrpc.admin.getSessions.useQuery.mockReturnValue({
      data: sessionsWithDifferentStatuses,
      isLoading: false,
      error: null,
    })
    mockTrpc.admin.getSessionStats.useQuery.mockReturnValue({
      data: mockStats,
    })
    
    mockTrpc.admin.revokeSession.useMutation.mockReturnValue({
      mutateAsync: vi.fn(),
      isLoading: false,
    })
    
    mockTrpc.admin.revokeAllUserSessions.useMutation.mockReturnValue({
      mutateAsync: vi.fn(),
      isLoading: false,
    })

    render(<SessionManager />)
    
    const activeStatus = screen.getByText('active')
    const expiredStatus = screen.getByText('expired')
    
    expect(activeStatus).toHaveClass('text-green-600', 'bg-green-100')
    expect(expiredStatus).toHaveClass('text-yellow-600', 'bg-yellow-100')
  })

  it('should handle empty sessions gracefully', () => {
    mockTrpc.admin.getSessions.useQuery.mockReturnValue({
      data: [],
      isLoading: false,
      error: null,
    })
    mockTrpc.admin.getSessionStats.useQuery.mockReturnValue({
      data: mockStats,
    })
    
    mockTrpc.admin.revokeSession.useMutation.mockReturnValue({
      mutateAsync: vi.fn(),
      isLoading: false,
    })
    
    mockTrpc.admin.revokeAllUserSessions.useMutation.mockReturnValue({
      mutateAsync: vi.fn(),
      isLoading: false,
    })

    render(<SessionManager />)
    
    expect(screen.getByText('No active sessions found')).toBeInTheDocument()
  })

  it('should disable buttons during mutation loading', () => {
    mockTrpc.admin.getSessions.useQuery.mockReturnValue({
      data: mockSessions,
      isLoading: false,
      error: null,
    })
    mockTrpc.admin.getSessionStats.useQuery.mockReturnValue({
      data: mockStats,
    })
    
    mockTrpc.admin.revokeSession.useMutation.mockReturnValue({
      mutateAsync: vi.fn(),
      isLoading: true,
    })
    
    mockTrpc.admin.revokeAllUserSessions.useMutation.mockReturnValue({
      mutateAsync: vi.fn(),
      isLoading: false,
    })

    render(<SessionManager />)
    
    const revokeButtons = screen.getAllByText('Revoke')
    expect(revokeButtons[0]).toBeDisabled()
  })
})
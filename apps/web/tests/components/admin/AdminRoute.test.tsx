import { describe, it, expect, beforeEach, vi } from 'vitest'
import { render, screen } from '@testing-library/react'
import { <PERSON><PERSON>er<PERSON>outer, MemoryRouter } from 'react-router-dom'
import '@testing-library/jest-dom'
import { AdminRoute, useRequireAdmin } from '../../../src/components/admin/AdminRoute'
import type { User } from '../../../../../packages/shared/src/types'

// Mock auth store
const mockAuthStore = {
  user: null as User | null,
  isLoading: false,
}

vi.mock('../../stores/auth', () => ({
  useAuthStore: () => mockAuthStore,
}))

// Mock navigate
const mockNavigate = vi.fn()
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom')
  return {
    ...actual,
    Navigate: ({ to, state }: any) => {
      mockNavigate(to, state)
      return <div data-testid="navigate">Navigating to {to}</div>
    },
  }
})

const TestComponent = () => <div data-testid="protected-content">Admin Content</div>

describe('AdminRoute', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockAuthStore.user = null
    mockAuthStore.isLoading = false
  })

  it('should show loading spinner when loading', () => {
    mockAuthStore.isLoading = true
    
    render(
      <BrowserRouter>
        <AdminRoute>
          <TestComponent />
        </AdminRoute>
      </BrowserRouter>
    )
    
    expect(screen.getByText('Loading...')).toBeInTheDocument()
    expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument()
  })

  it('should redirect to auth when not authenticated', () => {
    mockAuthStore.user = null
    mockAuthStore.isLoading = false
    
    render(
      <MemoryRouter initialEntries={['/admin/sessions']}>
        <AdminRoute>
          <TestComponent />
        </AdminRoute>
      </MemoryRouter>
    )
    
    expect(screen.getByTestId('navigate')).toBeInTheDocument()
    expect(screen.getByText('Navigating to /auth')).toBeInTheDocument()
  })

  it('should show access denied for non-admin users', () => {
    mockAuthStore.user = {
      id: 'user-1',
      email: '<EMAIL>',
      name: 'Regular User',
      avatar_url: null,
      subscription_tier: 'free',
      role: 'user',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    }
    mockAuthStore.isLoading = false
    
    render(
      <BrowserRouter>
        <AdminRoute>
          <TestComponent />
        </AdminRoute>
      </BrowserRouter>
    )
    
    expect(screen.getByText('Admin Access Required')).toBeInTheDocument()
    expect(screen.getByText(/You don't have permission to access this administrative area/)).toBeInTheDocument()
    expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument()
  })

  it('should render protected content for admin users', () => {
    mockAuthStore.user = {
      id: 'admin-1',
      email: '<EMAIL>',
      name: 'Admin User',
      avatar_url: null,
      subscription_tier: 'premium',
      role: 'admin',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    }
    mockAuthStore.isLoading = false
    
    render(
      <BrowserRouter>
        <AdminRoute>
          <TestComponent />
        </AdminRoute>
      </BrowserRouter>
    )
    
    expect(screen.getByTestId('protected-content')).toBeInTheDocument()
    expect(screen.getByText('Admin Content')).toBeInTheDocument()
  })

  it('should use custom fallback path', () => {
    mockAuthStore.user = null
    mockAuthStore.isLoading = false
    
    render(
      <MemoryRouter initialEntries={['/admin/sessions']}>
        <AdminRoute fallbackPath="/login">
          <TestComponent />
        </AdminRoute>
      </MemoryRouter>
    )
    
    expect(screen.getByText('Navigating to /login')).toBeInTheDocument()
  })

  it('should preserve current location in navigation state', () => {
    mockAuthStore.user = null
    mockAuthStore.isLoading = false
    
    render(
      <MemoryRouter initialEntries={['/admin/sessions']}>
        <AdminRoute>
          <TestComponent />
        </AdminRoute>
      </MemoryRouter>
    )
    
    expect(mockNavigate).toHaveBeenCalledWith('/auth', { from: '/admin/sessions' })
  })
})

// Test the useRequireAdmin hook
describe('useRequireAdmin', () => {
  const TestHookComponent = () => {
    const { user, isLoading, isAuthenticated, isAdmin, canAccessAdmin } = useRequireAdmin()
    
    return (
      <div>
        <div data-testid="is-loading">{isLoading.toString()}</div>
        <div data-testid="is-authenticated">{isAuthenticated.toString()}</div>
        <div data-testid="is-admin">{isAdmin.toString()}</div>
        <div data-testid="can-access-admin">{canAccessAdmin.toString()}</div>
        <div data-testid="user-email">{user?.email || 'none'}</div>
      </div>
    )
  }

  beforeEach(() => {
    mockAuthStore.user = null
    mockAuthStore.isLoading = false
  })

  it('should return correct values when loading', () => {
    mockAuthStore.isLoading = true
    
    render(<TestHookComponent />)
    
    expect(screen.getByTestId('is-loading')).toHaveTextContent('true')
    expect(screen.getByTestId('is-authenticated')).toHaveTextContent('false')
    expect(screen.getByTestId('is-admin')).toHaveTextContent('false')
    expect(screen.getByTestId('can-access-admin')).toHaveTextContent('false')
  })

  it('should return correct values when not authenticated', () => {
    mockAuthStore.user = null
    
    render(<TestHookComponent />)
    
    expect(screen.getByTestId('is-loading')).toHaveTextContent('false')
    expect(screen.getByTestId('is-authenticated')).toHaveTextContent('false')
    expect(screen.getByTestId('is-admin')).toHaveTextContent('false')
    expect(screen.getByTestId('can-access-admin')).toHaveTextContent('false')
    expect(screen.getByTestId('user-email')).toHaveTextContent('none')
  })

  it('should return correct values for regular user', () => {
    mockAuthStore.user = {
      id: 'user-1',
      email: '<EMAIL>',
      name: 'Regular User',
      avatar_url: null,
      subscription_tier: 'free',
      role: 'user',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    }
    
    render(<TestHookComponent />)
    
    expect(screen.getByTestId('is-loading')).toHaveTextContent('false')
    expect(screen.getByTestId('is-authenticated')).toHaveTextContent('true')
    expect(screen.getByTestId('is-admin')).toHaveTextContent('false')
    expect(screen.getByTestId('can-access-admin')).toHaveTextContent('false')
    expect(screen.getByTestId('user-email')).toHaveTextContent('<EMAIL>')
  })

  it('should return correct values for admin user', () => {
    mockAuthStore.user = {
      id: 'admin-1',
      email: '<EMAIL>',
      name: 'Admin User',
      avatar_url: null,
      subscription_tier: 'premium',
      role: 'admin',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    }
    
    render(<TestHookComponent />)
    
    expect(screen.getByTestId('is-loading')).toHaveTextContent('false')
    expect(screen.getByTestId('is-authenticated')).toHaveTextContent('true')
    expect(screen.getByTestId('is-admin')).toHaveTextContent('true')
    expect(screen.getByTestId('can-access-admin')).toHaveTextContent('true')
    expect(screen.getByTestId('user-email')).toHaveTextContent('<EMAIL>')
  })
})
import { test, expect } from '@playwright/test';

test.describe('Responsive Design', () => {
  test('should adapt to mobile viewport', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 }); // iPhone SE size
    await page.goto('/');
    
    // Check that hamburger menu is visible on mobile
    const menuButton = page.locator('[aria-label="Open menu"]');
    await expect(menuButton).toBeVisible();
    
    // Check that sidebar is hidden by default on mobile
    const sidebar = page.locator('nav').first();
    await expect(sidebar).toHaveClass(/-translate-x-full/);
  });

  test('should show desktop layout on large screens', async ({ page }) => {
    await page.setViewportSize({ width: 1024, height: 768 }); // Desktop size
    await page.goto('/');
    
    // Check that sidebar is visible on desktop
    const sidebar = page.locator('nav').first();
    await expect(sidebar).toHaveClass(/lg:translate-x-0/);
  });

  test('should have touch-friendly elements', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto('/');
    
    // Check that interactive elements have minimum touch target size
    const menuButton = page.locator('[aria-label="Open menu"]');
    const buttonBox = await menuButton.boundingBox();
    
    expect(buttonBox?.width).toBeGreaterThanOrEqual(44);
    expect(buttonBox?.height).toBeGreaterThanOrEqual(44);
  });

  test('should handle mobile navigation properly', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto('/');
    
    // Open mobile menu
    await page.click('[aria-label="Open menu"]');
    
    // Check that sidebar becomes visible
    const sidebar = page.locator('nav').first();
    await expect(sidebar).toHaveClass(/translate-x-0/);
    
    // Click on a navigation item
    await page.click('text=Process PDFs');
    
    // Check that we navigated to the process page
    await expect(page.locator('h1')).toContainText('Process PDFs');
    
    // Check that sidebar is closed after navigation
    await expect(sidebar).toHaveClass(/-translate-x-full/);
  });

  test('should maintain usability across different screen sizes', async ({ page }) => {
    const viewports = [
      { width: 320, height: 568 },  // iPhone 5
      { width: 375, height: 667 },  // iPhone SE
      { width: 768, height: 1024 }, // iPad
      { width: 1024, height: 768 }, // Desktop
      { width: 1440, height: 900 }, // Large desktop
    ];
    
    for (const viewport of viewports) {
      await page.setViewportSize(viewport);
      await page.goto('/dashboard');
      
      // Check that the page title is visible and readable
      const title = page.locator('h1');
      await expect(title).toBeVisible();
      await expect(title).toContainText('Dashboard');
      
      // Check that navigation is accessible
      if (viewport.width < 1024) {
        // Mobile: hamburger menu should be visible
        await expect(page.locator('[aria-label="Open menu"]')).toBeVisible();
      } else {
        // Desktop: sidebar should be visible
        await expect(page.locator('nav a').first()).toBeVisible();
      }
    }
  });
});
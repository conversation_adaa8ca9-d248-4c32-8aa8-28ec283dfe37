import { test, expect } from '@playwright/test';

test.describe('PWA Functionality', () => {
  test('should have proper PWA manifest', async ({ page }) => {
    await page.goto('/');
    
    // Check if manifest is linked
    const manifestLink = page.locator('link[rel="manifest"]');
    await expect(manifestLink).toHaveAttribute('href', '/manifest.json');
    
    // Check manifest content
    const response = await page.request.get('/manifest.json');
    const manifest = await response.json();
    
    expect(manifest.name).toBe('MobilePDF Pro');
    expect(manifest.short_name).toBe('MobilePDF');
    expect(manifest.display).toBe('standalone');
    expect(manifest.icons).toHaveLength(2);
  });

  test('should register service worker', async ({ page }) => {
    await page.goto('/');
    
    // Wait for service worker registration
    await page.waitForFunction(() => {
      return navigator.serviceWorker.getRegistration().then(reg => !!reg);
    });
    
    const swRegistered = await page.evaluate(async () => {
      const registration = await navigator.serviceWorker.getRegistration();
      return !!registration;
    });
    
    expect(swRegistered).toBe(true);
  });

  test('should be installable on mobile devices', async ({ page, browserName }) => {
    test.skip(browserName !== 'chromium', 'PWA installation only works on Chromium');
    
    await page.goto('/');
    
    // Check for installability indicators
    const themeColor = page.locator('meta[name="theme-color"]');
    await expect(themeColor).toHaveAttribute('content', '#1f2937');
    
    const appleTouchIcon = page.locator('link[rel="apple-touch-icon"]');
    await expect(appleTouchIcon).toHaveAttribute('href', '/icon-192x192.png');
  });

  test('should work offline', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Go offline
    await page.context().setOffline(true);
    
    // Navigate to dashboard (should work offline with cached resources)
    await page.click('text=Dashboard');
    await expect(page.locator('h1')).toContainText('Dashboard');
    
    // Go back online
    await page.context().setOffline(false);
  });
});
import { test, expect, Page } from '@playwright/test';
import path from 'path';
import fs from 'fs/promises';

// Helper to create a test PDF file
async function createTestPDF(sizeInMB: number): Promise<string> {
  const fileName = `test-${sizeInMB}mb.pdf`;
  const filePath = path.join(__dirname, '..', 'fixtures', fileName);
  
  // Ensure fixtures directory exists
  await fs.mkdir(path.dirname(filePath), { recursive: true });
  
  // Create a minimal PDF with the specified size
  const sizeInBytes = sizeInMB * 1024 * 1024;
  const pdfHeader = '%PDF-1.4\n';
  const pdfContent = `1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
>>
endobj

xref
0 4
0000000000 65535 f 
0000000015 00000 n 
0000000074 00000 n 
0000000131 00000 n 
trailer
<<
/Size 4
/Root 1 0 R
>>
startxref
184
%%EOF`;

  // Pad the content to reach the desired size
  const currentSize = pdfHeader.length + pdfContent.length;
  const padding = '\n'.repeat(Math.max(0, sizeInBytes - currentSize));
  const fullContent = pdfHeader + pdfContent + padding;
  
  await fs.writeFile(filePath, fullContent);
  return filePath;
}

test.describe('Large File Processing', () => {
  let page: Page;

  test.beforeEach(async ({ browser }) => {
    page = await browser.newPage();
    
    // Navigate to the PDF processing page
    await page.goto('/process'); // Adjust URL based on your routing
  });

  test.afterEach(async () => {
    await page.close();
  });

  test('should handle medium-sized files (50MB) successfully', async () => {
    // Create a 50MB test PDF
    const testFilePath = await createTestPDF(50);
    
    try {
      // Find the file input
      const fileInput = await page.locator('input[type="file"]');
      await expect(fileInput).toBeAttached();

      // Upload the file
      await fileInput.setInputFiles(testFilePath);

      // Wait for file validation
      await page.waitForTimeout(2000);

      // Check that no error message appears
      const errorMessage = page.locator('[role="alert"]');
      await expect(errorMessage).not.toBeVisible({ timeout: 5000 });

      // Check that processing can start
      const processButton = page.getByRole('button', { name: /process/i });
      if (await processButton.isVisible()) {
        await processButton.click();
        
        // Wait for processing to begin
        const progressIndicator = page.locator('[data-testid="processing-progress"]');
        await expect(progressIndicator).toBeVisible({ timeout: 10000 });
      }

    } finally {
      // Clean up test file
      await fs.unlink(testFilePath).catch(() => {});
    }
  });

  test('should show warning for very large files (100MB+)', async () => {
    // Create a 100MB test PDF
    const testFilePath = await createTestPDF(100);
    
    try {
      const fileInput = await page.locator('input[type="file"]');
      await fileInput.setInputFiles(testFilePath);

      // Wait for file validation
      await page.waitForTimeout(3000);

      // Check for memory warning or size warning
      const warningMessage = page.locator('text=/memory|size|large/i');
      
      // The file might be accepted with warnings or rejected
      // Either behavior is acceptable for 100MB files
      const isAccepted = await page.locator('[data-testid="file-accepted"]').isVisible({ timeout: 5000 });
      const isRejected = await warningMessage.isVisible({ timeout: 5000 });
      
      expect(isAccepted || isRejected).toBeTruthy();

    } finally {
      await fs.unlink(testFilePath).catch(() => {});
    }
  });

  test('should reject extremely large files (1GB+)', async () => {
    // For this test, we'll simulate a large file without actually creating it
    // by mocking the File API
    
    await page.evaluate(() => {
      // Mock a very large file
      const originalFile = window.File;
      const mockFile = class extends originalFile {
        constructor(fileBits: any[], fileName: string, options?: FilePropertyBag) {
          super(fileBits, fileName, options);
          // Override size property
          Object.defineProperty(this, 'size', {
            value: 1024 * 1024 * 1024 * 2, // 2GB
            writable: false
          });
        }
      };
      (window as any).File = mockFile;
    });

    // Create a small file but with mocked large size
    const smallTestFile = await createTestPDF(1);
    
    try {
      const fileInput = await page.locator('input[type="file"]');
      await fileInput.setInputFiles(smallTestFile);

      // Wait for validation
      await page.waitForTimeout(2000);

      // Should show error about file being too large
      const errorMessage = page.locator('text=/too large|exceeds.*limit|memory/i');
      await expect(errorMessage).toBeVisible({ timeout: 10000 });

    } finally {
      await fs.unlink(smallTestFile).catch(() => {});
    }
  });

  test('should show memory usage information during processing', async () => {
    const testFilePath = await createTestPDF(10);
    
    try {
      const fileInput = await page.locator('input[type="file"]');
      await fileInput.setInputFiles(testFilePath);

      await page.waitForTimeout(1000);

      // Start processing if available
      const processButton = page.getByRole('button', { name: /process/i });
      if (await processButton.isVisible()) {
        await processButton.click();

        // Check for memory usage display (if implemented)
        const memoryInfo = page.locator('[data-testid="memory-info"]');
        
        // Memory info might be visible during processing
        // This is optional depending on implementation
        if (await memoryInfo.isVisible({ timeout: 5000 })) {
          await expect(memoryInfo).toContainText(/memory|mb|%/i);
        }
      }

    } finally {
      await fs.unlink(testFilePath).catch(() => {});
    }
  });

  test('should handle browser memory pressure gracefully', async () => {
    // This test simulates low memory conditions
    await page.evaluate(() => {
      // Mock performance.memory to simulate low memory
      Object.defineProperty(performance, 'memory', {
        value: {
          usedJSHeapSize: 950000000, // 950MB used
          totalJSHeapSize: 980000000, // 980MB total
          jsHeapSizeLimit: 1000000000 // 1GB limit (high memory pressure)
        },
        configurable: true
      });
    });

    const testFilePath = await createTestPDF(20);
    
    try {
      const fileInput = await page.locator('input[type="file"]');
      await fileInput.setInputFiles(testFilePath);

      await page.waitForTimeout(2000);

      // Should show memory pressure warning
      const memoryWarning = page.locator('text=/memory.*pressure|insufficient.*memory/i');
      
      // The file might be rejected due to memory pressure
      const rejectionMessage = page.locator('text=/memory|available/i');
      const isRejected = await rejectionMessage.isVisible({ timeout: 5000 });
      
      if (isRejected) {
        // File should be rejected with memory-related message
        await expect(rejectionMessage).toBeVisible();
      }

    } finally {
      await fs.unlink(testFilePath).catch(() => {});
    }
  });

  test('should provide chunked processing for supported large files', async () => {
    const testFilePath = await createTestPDF(30);
    
    try {
      const fileInput = await page.locator('input[type="file"]');
      await fileInput.setInputFiles(testFilePath);

      await page.waitForTimeout(1000);

      const processButton = page.getByRole('button', { name: /process/i });
      if (await processButton.isVisible()) {
        await processButton.click();

        // Look for chunked processing indicators
        const progressBar = page.locator('[data-testid="progress-bar"]');
        
        if (await progressBar.isVisible({ timeout: 10000 })) {
          // Progress should update multiple times for chunked processing
          let progressValues: string[] = [];
          
          // Collect progress updates
          for (let i = 0; i < 10; i++) {
            const progressText = await page.locator('text=/\d+%/').textContent();
            if (progressText && !progressValues.includes(progressText)) {
              progressValues.push(progressText);
            }
            await page.waitForTimeout(500);
          }
          
          // Should have multiple progress updates for large files
          expect(progressValues.length).toBeGreaterThan(1);
        }
      }

    } finally {
      await fs.unlink(testFilePath).catch(() => {});
    }
  });
});
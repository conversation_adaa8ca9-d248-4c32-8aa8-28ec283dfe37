import { test, expect } from '@playwright/test';

test.describe('Visual Regression Tests for PDF Rendering', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('http://localhost:5173');
    await page.locator('[data-testid="pdf-processor"]').waitFor();
  });

  test('should render PDF pages consistently with text layout preservation', async ({ page }) => {
    // Create a test PDF with specific text layout
    const testPdfWithText = Buffer.from(
      '%PDF-1.4\n' +
      '1 0 obj<</Type/Catalog/Pages 2 0 R>>endobj\n' +
      '2 0 obj<</Type/Pages/Kids[3 0 R]/Count 1>>endobj\n' +
      '3 0 obj<</Type/Page/Parent 2 0 R/MediaBox[0 0 612 792]/Resources<</Font<</F1 4 0 R>>>>>/Contents 5 0 R>>endobj\n' +
      '4 0 obj<</Type/Font/Subtype/Type1/BaseFont/Helvetica>>endobj\n' +
      '5 0 obj<</Length 100>>stream\n' +
      'BT\n' +
      '/F1 12 Tf\n' +
      '100 700 Td\n' +
      '(Visual Regression Test Document) Tj\n' +
      '0 -50 Td\n' +
      '(This text should render consistently) Tj\n' +
      'ET\n' +
      'endstream\nendobj\n' +
      'xref\n0 6\n0000000000 65535 f \n0000000015 00000 n \n0000000074 00000 n \n0000000131 00000 n \n0000000300 00000 n \n0000000380 00000 n \ntrailer<</Size 6/Root 1 0 R>>\nstartxref\n500\n%%EOF',
      'utf8'
    );

    // Upload test PDF
    const fileChooserPromise = page.waitForEvent('filechooser');
    await page.locator('[data-testid="file-upload-button"]').click();
    const fileChooser = await fileChooserPromise;
    await fileChooser.setFiles([{
      name: 'text-layout-test.pdf',
      mimeType: 'application/pdf',
      buffer: testPdfWithText
    }]);

    // Wait for PDF to load and render
    await page.locator('[data-testid="pdf-viewer"]').waitFor();
    await page.locator('[data-testid="pdf-page-1"]').waitFor();

    // Take screenshot of rendered PDF
    const pdfViewer = page.locator('[data-testid="pdf-viewer"]');
    await expect(pdfViewer).toHaveScreenshot('pdf-text-layout-baseline.png', {
      threshold: 0.3, // Allow 30% pixel difference for font rendering variations
      maxDiffPixels: 1000
    });

    // Test text editing maintains layout
    await page.locator('[data-testid="edit-text-button"]').click();
    await page.locator('[data-testid="text-edit-input"]').fill('Edited text content');
    await page.locator('[data-testid="apply-text-edit"]').click();
    await page.locator('[data-testid="edit-complete"]').waitFor();

    // Screenshot after text editing
    await expect(pdfViewer).toHaveScreenshot('pdf-text-edited.png', {
      threshold: 0.4, // Higher threshold for edited content
      maxDiffPixels: 2000
    });
  });

  test('should maintain visual consistency during page operations', async ({ page }) => {
    // Create multi-page test PDF
    const multiPagePdf = Buffer.from(
      '%PDF-1.4\n' +
      '1 0 obj<</Type/Catalog/Pages 2 0 R>>endobj\n' +
      '2 0 obj<</Type/Pages/Kids[3 0 R 4 0 R]/Count 2>>endobj\n' +
      '3 0 obj<</Type/Page/Parent 2 0 R/MediaBox[0 0 612 792]/Resources<</Font<</F1 5 0 R>>>>>/Contents 6 0 R>>endobj\n' +
      '4 0 obj<</Type/Page/Parent 2 0 R/MediaBox[0 0 612 792]/Resources<</Font<</F1 5 0 R>>>>>/Contents 7 0 R>>endobj\n' +
      '5 0 obj<</Type/Font/Subtype/Type1/BaseFont/Helvetica>>endobj\n' +
      '6 0 obj<</Length 80>>stream\nBT\n/F1 14 Tf\n100 700 Td\n(Page 1 Content) Tj\nET\nendstream\nendobj\n' +
      '7 0 obj<</Length 80>>stream\nBT\n/F1 14 Tf\n100 700 Td\n(Page 2 Content) Tj\nET\nendstream\nendobj\n' +
      'xref\n0 8\n0000000000 65535 f \ntrailer<</Size 8/Root 1 0 R>>\nstartxref\n600\n%%EOF',
      'utf8'
    );

    const fileChooserPromise = page.waitForEvent('filechooser');
    await page.locator('[data-testid="file-upload-button"]').click();
    const fileChooser = await fileChooserPromise;
    await fileChooser.setFiles([{
      name: 'multi-page-test.pdf',
      mimeType: 'application/pdf',
      buffer: multiPagePdf
    }]);

    await page.locator('[data-testid="pdf-viewer"]').waitFor();

    // Baseline: Both pages visible
    const thumbnailView = page.locator('[data-testid="thumbnail-container"]');
    await expect(thumbnailView).toHaveScreenshot('multi-page-baseline.png', {
      threshold: 0.2
    });

    // Test page rotation visual consistency
    await page.locator('[data-testid="page-1-thumbnail"]').click();
    await page.locator('[data-testid="rotate-page-button"]').click();
    await page.locator('[data-testid="rotation-complete"]').waitFor();

    await expect(thumbnailView).toHaveScreenshot('page-rotated.png', {
      threshold: 0.3,
      maxDiffPixels: 3000
    });

    // Test page reordering visual consistency
    await page.locator('[data-testid="reorder-pages-button"]').click();
    await page.dragAndDrop(
      '[data-testid="page-2-thumbnail"]',
      '[data-testid="page-1-thumbnail"]'
    );
    await page.locator('[data-testid="reorder-complete"]').waitFor();

    await expect(thumbnailView).toHaveScreenshot('pages-reordered.png', {
      threshold: 0.3,
      maxDiffPixels: 2000
    });
  });

  test('should render annotations with pixel-perfect accuracy', async ({ page }) => {
    // Simple test PDF for annotation testing
    const simplePdf = Buffer.from(
      '%PDF-1.4\n1 0 obj<</Type/Catalog/Pages 2 0 R>>endobj\n2 0 obj<</Type/Pages/Kids[3 0 R]/Count 1>>endobj\n3 0 obj<</Type/Page/Parent 2 0 R/MediaBox[0 0 612 792]>>endobj\nxref\n0 4\ntrailer<</Size 4/Root 1 0 R>>\n%%EOF',
      'utf8'
    );

    const fileChooserPromise = page.waitForEvent('filechooser');
    await page.locator('[data-testid="file-upload-button"]').click();
    const fileChooser = await fileChooserPromise;
    await fileChooser.setFiles([{
      name: 'annotation-test.pdf',
      mimeType: 'application/pdf',
      buffer: simplePdf
    }]);

    await page.locator('[data-testid="pdf-viewer"]').waitFor();
    const pdfViewer = page.locator('[data-testid="pdf-viewer"]');

    // Baseline without annotations
    await expect(pdfViewer).toHaveScreenshot('no-annotations-baseline.png');

    // Add highlight annotation
    await page.locator('[data-testid="annotation-tools"]').waitFor();
    await page.locator('[data-testid="highlight-tool"]').click();
    
    // Simulate highlight area selection
    await page.locator('[data-testid="pdf-page-1"]').click({ position: { x: 100, y: 200 } });
    await page.mouse.down();
    await page.mouse.move(300, 250);
    await page.mouse.up();
    
    await page.locator('[data-testid="annotation-added"]').waitFor();

    // Screenshot with highlight
    await expect(pdfViewer).toHaveScreenshot('highlight-annotation.png', {
      threshold: 0.1, // Annotations should be very precise
      maxDiffPixels: 500
    });

    // Add text comment annotation
    await page.locator('[data-testid="comment-tool"]').click();
    await page.locator('[data-testid="pdf-page-1"]').click({ position: { x: 400, y: 300 } });
    await page.locator('[data-testid="comment-input"]').fill('Test comment annotation');
    await page.locator('[data-testid="save-comment"]').click();
    await page.locator('[data-testid="comment-saved"]').waitFor();

    // Screenshot with both annotations
    await expect(pdfViewer).toHaveScreenshot('multiple-annotations.png', {
      threshold: 0.15,
      maxDiffPixels: 1000
    });

    // Test annotation editing visual consistency
    await page.locator('[data-testid="comment-annotation"]').dblclick();
    await page.locator('[data-testid="comment-input"]').fill('Edited comment text');
    await page.locator('[data-testid="save-comment"]').click();
    await page.locator('[data-testid="comment-updated"]').waitFor();

    await expect(pdfViewer).toHaveScreenshot('edited-annotation.png', {
      threshold: 0.15,
      maxDiffPixels: 800
    });
  });

  test('should maintain visual quality during format conversion', async ({ page }) => {
    const testPdf = Buffer.alloc(1024 * 1024, 'PDF content for conversion test');
    
    const fileChooserPromise = page.waitForEvent('filechooser');
    await page.locator('[data-testid="file-upload-button"]').click();
    const fileChooser = await fileChooserPromise;
    await fileChooser.setFiles([{
      name: 'conversion-test.pdf',
      mimeType: 'application/pdf',
      buffer: testPdf
    }]);

    await page.locator('[data-testid="pdf-viewer"]').waitFor();
    
    // Baseline rendering
    const pdfViewer = page.locator('[data-testid="pdf-viewer"]');
    await expect(pdfViewer).toHaveScreenshot('conversion-baseline.png');

    // Test different export formats maintain visual fidelity
    const exportFormats = [
      { format: 'jpeg', quality: 85 },
      { format: 'png', quality: 100 },
      { format: 'tiff', quality: 90 }
    ];

    for (const exportFormat of exportFormats) {
      await page.locator('[data-testid="export-menu"]').click();
      await page.locator(`[data-testid="export-${exportFormat.format}"]`).click();
      
      // Set quality if applicable
      if (exportFormat.format === 'jpeg') {
        await page.locator('[data-testid="quality-slider"]').fill(exportFormat.quality.toString());
      }
      
      await page.locator('[data-testid="export-confirm"]').click();
      await page.locator('[data-testid="export-complete"]').waitFor();
      
      // Preview the exported version
      await page.locator('[data-testid="preview-export"]').click();
      await page.locator('[data-testid="export-preview"]').waitFor();
      
      const exportPreview = page.locator('[data-testid="export-preview"]');
      await expect(exportPreview).toHaveScreenshot(`exported-${exportFormat.format}-preview.png`, {
        threshold: exportFormat.format === 'jpeg' ? 0.4 : 0.2 // JPEG allows more variance
      });
      
      await page.locator('[data-testid="close-preview"]').click();
    }
  });

  test('should handle compression with minimal visual degradation', async ({ page }) => {
    // Create a PDF with various content types for compression testing
    const richContentPdf = Buffer.from(
      '%PDF-1.4\n' +
      '1 0 obj<</Type/Catalog/Pages 2 0 R>>endobj\n' +
      '2 0 obj<</Type/Pages/Kids[3 0 R]/Count 1>>endobj\n' +
      '3 0 obj<</Type/Page/Parent 2 0 R/MediaBox[0 0 612 792]/Resources<</Font<</F1 4 0 R>>>>>/Contents 5 0 R>>endobj\n' +
      '4 0 obj<</Type/Font/Subtype/Type1/BaseFont/Times-Roman>>endobj\n' +
      '5 0 obj<</Length 200>>stream\n' +
      'BT\n/F1 16 Tf\n100 700 Td\n(Compression Test Document) Tj\n0 -30 Td\n(This document contains text and graphics) Tj\n0 -30 Td\n(to test visual quality after compression) Tj\nET\n' +
      'q\n200 0 0 100 100 400 cm\n0.5 0.5 1 rg\nf\nQ\nendstream\nendobj\n' +
      'xref\n0 6\ntrailer<</Size 6/Root 1 0 R>>\n%%EOF',
      'utf8'
    );

    const fileChooserPromise = page.waitForEvent('filechooser');
    await page.locator('[data-testid="file-upload-button"]').click();
    const fileChooser = await fileChooserPromise;
    await fileChooser.setFiles([{
      name: 'compression-visual-test.pdf',
      mimeType: 'application/pdf',
      buffer: richContentPdf
    }]);

    await page.locator('[data-testid="pdf-viewer"]').waitFor();
    const pdfViewer = page.locator('[data-testid="pdf-viewer"]');

    // Baseline before compression
    await expect(pdfViewer).toHaveScreenshot('pre-compression-baseline.png');

    // Test different compression levels
    const compressionLevels = [
      { level: 'low', expectedQuality: 'high', threshold: 0.1 },
      { level: 'medium', expectedQuality: 'good', threshold: 0.2 },
      { level: 'high', expectedQuality: 'acceptable', threshold: 0.3 }
    ];

    for (const compression of compressionLevels) {
      await page.locator('[data-testid="compress-pdf-button"]').click();
      await page.locator(`[data-testid="compression-${compression.level}"]`).click();
      await page.locator('[data-testid="apply-compression"]').click();
      await page.locator('[data-testid="compression-complete"]').waitFor();

      // Visual comparison after compression
      await expect(pdfViewer).toHaveScreenshot(`compressed-${compression.level}.png`, {
        threshold: compression.threshold,
        maxDiffPixels: compression.level === 'high' ? 5000 : 2000
      });

      // Check file size indicator
      const fileSizeElement = page.locator('[data-testid="file-size-display"]');
      const fileSize = await fileSizeElement.textContent();
      console.log(`File size after ${compression.level} compression: ${fileSize}`);
    }
  });

  test('should render text with correct font metrics and spacing', async ({ page }) => {
    // Test PDF with specific font and spacing requirements
    const fontTestPdf = Buffer.from(
      '%PDF-1.4\n' +
      '1 0 obj<</Type/Catalog/Pages 2 0 R>>endobj\n' +
      '2 0 obj<</Type/Pages/Kids[3 0 R]/Count 1>>endobj\n' +
      '3 0 obj<</Type/Page/Parent 2 0 R/MediaBox[0 0 612 792]/Resources<</Font<</F1 4 0 R/F2 5 0 R>>>>>/Contents 6 0 R>>endobj\n' +
      '4 0 obj<</Type/Font/Subtype/Type1/BaseFont/Helvetica>>endobj\n' +
      '5 0 obj<</Type/Font/Subtype/Type1/BaseFont/Times-Roman>>endobj\n' +
      '6 0 obj<</Length 300>>stream\n' +
      'BT\n' +
      '/F1 12 Tf\n72 720 Td\n(Helvetica 12pt - Line 1) Tj\n' +
      '0 -18 Td\n(Helvetica 12pt - Line 2) Tj\n' +
      '/F2 14 Tf\n0 -25 Td\n(Times Roman 14pt - Line 3) Tj\n' +
      '/F1 10 Tf\n0 -20 Td\n(Helvetica 10pt - Smaller text) Tj\n' +
      '/F2 18 Tf\n0 -30 Td\n(Times Roman 18pt - Larger text) Tj\n' +
      'ET\n' +
      'endstream\nendobj\n' +
      'xref\n0 7\ntrailer<</Size 7/Root 1 0 R>>\n%%EOF',
      'utf8'
    );

    const fileChooserPromise = page.waitForEvent('filechooser');
    await page.locator('[data-testid="file-upload-button"]').click();
    const fileChooser = await fileChooserPromise;
    await fileChooser.setFiles([{
      name: 'font-metrics-test.pdf',
      mimeType: 'application/pdf',
      buffer: fontTestPdf
    }]);

    await page.locator('[data-testid="pdf-viewer"]').waitFor();
    const pdfViewer = page.locator('[data-testid="pdf-viewer"]');

    // Baseline font rendering
    await expect(pdfViewer).toHaveScreenshot('font-metrics-baseline.png', {
      threshold: 0.1 // Font rendering should be very consistent
    });

    // Test zoom levels maintain font quality
    const zoomLevels = [50, 75, 125, 150, 200];
    
    for (const zoom of zoomLevels) {
      await page.locator('[data-testid="zoom-control"]').fill(`${zoom}%`);
      await page.locator('[data-testid="apply-zoom"]').click();
      await page.locator('[data-testid="zoom-complete"]').waitFor();

      await expect(pdfViewer).toHaveScreenshot(`font-zoom-${zoom}percent.png`, {
        threshold: 0.15, // Allow some variance for different zoom levels
        maxDiffPixels: 2000
      });
    }

    // Reset zoom
    await page.locator('[data-testid="zoom-control"]').fill('100%');
    await page.locator('[data-testid="apply-zoom"]').click();
  });

  test('should maintain consistent rendering across different viewport sizes', async ({ page }) => {
    const testPdf = Buffer.from('%PDF-1.4\n%%EOF', 'utf8');

    // Test different viewport sizes
    const viewportSizes = [
      { width: 1920, height: 1080, name: 'desktop-large' },
      { width: 1366, height: 768, name: 'desktop-standard' },
      { width: 768, height: 1024, name: 'tablet-portrait' },
      { width: 1024, height: 768, name: 'tablet-landscape' },
      { width: 375, height: 667, name: 'mobile-small' },
      { width: 414, height: 896, name: 'mobile-large' }
    ];

    for (const viewport of viewportSizes) {
      await page.setViewportSize({ width: viewport.width, height: viewport.height });
      
      const fileChooserPromise = page.waitForEvent('filechooser');
      await page.locator('[data-testid="file-upload-button"]').click();
      const fileChooser = await fileChooserPromise;
      await fileChooser.setFiles([{
        name: `responsive-test-${viewport.name}.pdf`,
        mimeType: 'application/pdf',
        buffer: testPdf
      }]);

      await page.locator('[data-testid="pdf-viewer"]').waitFor();
      
      // Test that PDF viewer adapts to viewport
      const pdfContainer = page.locator('[data-testid="pdf-container"]');
      await expect(pdfContainer).toHaveScreenshot(`responsive-${viewport.name}.png`, {
        threshold: 0.2,
        fullPage: true
      });

      // Clean up for next iteration
      await page.locator('[data-testid="clear-file-button"]').click();
    }
  });

  test('should detect and highlight visual regressions in complex documents', async ({ page }) => {
    // Create a complex document with multiple elements
    const complexPdf = Buffer.from(
      '%PDF-1.4\n' +
      '1 0 obj<</Type/Catalog/Pages 2 0 R>>endobj\n' +
      '2 0 obj<</Type/Pages/Kids[3 0 R]/Count 1>>endobj\n' +
      '3 0 obj<</Type/Page/Parent 2 0 R/MediaBox[0 0 612 792]/Resources<</Font<</F1 4 0 R>>>>>/Contents 5 0 R>>endobj\n' +
      '4 0 obj<</Type/Font/Subtype/Type1/BaseFont/Helvetica>>endobj\n' +
      '5 0 obj<</Length 400>>stream\n' +
      'BT\n/F1 18 Tf\n100 750 Td\n(Complex Document Test) Tj\nET\n' +
      'q\n1 0 0 rg\n100 650 200 100 re\nf\nQ\n' +
      'q\n0 1 0 rg\n350 650 150 80 re\nf\nQ\n' +
      'q\n0 0 1 rg\n150 500 100 100 re\nf\nQ\n' +
      'q\n0.5 0.5 0.5 rg\n300 450 200 50 re\nf\nQ\n' +
      'BT\n/F1 12 Tf\n100 400 Td\n(This document contains:) Tj\n0 -20 Td\n(- Colored rectangles) Tj\n0 -20 Td\n(- Multiple text blocks) Tj\n0 -20 Td\n(- Various geometric shapes) Tj\nET\n' +
      'endstream\nendobj\n' +
      'xref\n0 6\ntrailer<</Size 6/Root 1 0 R>>\n%%EOF',
      'utf8'
    );

    const fileChooserPromise = page.waitForEvent('filechooser');
    await page.locator('[data-testid="file-upload-button"]').click();
    const fileChooser = await fileChooserPromise;
    await fileChooser.setFiles([{
      name: 'complex-regression-test.pdf',
      mimeType: 'application/pdf',
      buffer: complexPdf
    }]);

    await page.locator('[data-testid="pdf-viewer"]').waitFor();
    const pdfViewer = page.locator('[data-testid="pdf-viewer"]');

    // Establish baseline for complex document
    await expect(pdfViewer).toHaveScreenshot('complex-document-baseline.png', {
      threshold: 0.05 // Very strict for baseline
    });

    // Simulate various operations that could cause regressions
    const regressionTests = [
      {
        name: 'text-edit-regression',
        action: async () => {
          await page.locator('[data-testid="edit-text-button"]').click();
          await page.locator('[data-testid="apply-minor-edit"]').click();
          await page.locator('[data-testid="edit-complete"]').waitFor();
        }
      },
      {
        name: 'annotation-regression',
        action: async () => {
          await page.locator('[data-testid="add-annotation-button"]').click();
          await page.locator('[data-testid="annotation-type-highlight"]').click();
          await page.locator('[data-testid="pdf-page-1"]').click({ position: { x: 150, y: 600 } });
          await page.locator('[data-testid="annotation-complete"]').waitFor();
        }
      },
      {
        name: 'zoom-regression',
        action: async () => {
          await page.locator('[data-testid="zoom-in-button"]').click();
          await page.locator('[data-testid="zoom-out-button"]').click();
          await page.locator('[data-testid="zoom-complete"]').waitFor();
        }
      }
    ];

    for (const regressionTest of regressionTests) {
      await regressionTest.action();
      
      // Check for visual regressions after each operation
      await expect(pdfViewer).toHaveScreenshot(`${regressionTest.name}-result.png`, {
        threshold: 0.1, // Allow minor differences for legitimate changes
        maxDiffPixels: 1500
      });
    }

    // Final state should still maintain overall document integrity
    await expect(pdfViewer).toHaveScreenshot('complex-document-final-state.png', {
      threshold: 0.2, // More lenient for final state with accumulated changes
      maxDiffPixels: 3000
    });
  });
});
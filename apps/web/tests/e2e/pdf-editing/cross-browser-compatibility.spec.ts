import { test, expect, Browser, <PERSON>rows<PERSON><PERSON>ontex<PERSON>, <PERSON> } from '@playwright/test';
import { chromium, firefox, webkit } from '@playwright/test';

interface BrowserTestContext {
  browser: Browser;
  context: BrowserContext;
  page: Page;
  browserName: string;
  capabilities: {
    webAssembly: boolean;
    simd: boolean;
    threads: boolean;
    sharedArrayBuffer: boolean;
    serviceWorkers: boolean;
    webWorkers: boolean;
  };
}

test.describe('Cross-Browser PDF Processing Compatibility', () => {
  const browsers = ['chromium', 'firefox', 'webkit'];
  const testContexts: BrowserTestContext[] = [];

  test.beforeAll(async () => {
    // Initialize browsers for parallel testing
    for (const browserName of browsers) {
      let browser: Browser;
      
      switch (browserName) {
        case 'chromium':
          browser = await chromium.launch({
            args: [
              '--enable-features=SharedArrayBuffer,WebAssemblyThreads,WebAssemblySIMD',
              '--disable-web-security',
              '--disable-features=VizDisplayCompositor'
            ]
          });
          break;
        case 'firefox':
          browser = await firefox.launch({
            firefoxUserPrefs: {
              'dom.postMessage.sharedArrayBuffer.bypassCOOP_COEP.insecure.enabled': true,
              'javascript.options.shared_memory': true
            }
          });
          break;
        case 'webkit':
          browser = await webkit.launch();
          break;
        default:
          throw new Error(`Unsupported browser: ${browserName}`);
      }

      const context = await browser.newContext({
        permissions: ['clipboard-read', 'clipboard-write']
      });
      
      const page = await context.newPage();
      
      // Navigate to test app
      await page.goto('http://localhost:5173');
      
      // Detect browser capabilities
      const capabilities = await page.evaluate(() => {
        return {
          webAssembly: typeof WebAssembly !== 'undefined',
          simd: typeof WebAssembly !== 'undefined' && WebAssembly.validate(new Uint8Array([
            0x00, 0x61, 0x73, 0x6d, 0x01, 0x00, 0x00, 0x00, 0x01, 0x05, 0x01,
            0x60, 0x00, 0x01, 0x7b, 0x03, 0x02, 0x01, 0x00, 0x0a, 0x0a, 0x01,
            0x08, 0x00, 0x41, 0x00, 0xfd, 0x0f, 0x0b
          ])),
          threads: typeof SharedArrayBuffer !== 'undefined' && 
                   typeof Worker !== 'undefined' &&
                   typeof Atomics !== 'undefined',
          sharedArrayBuffer: typeof SharedArrayBuffer !== 'undefined',
          serviceWorkers: 'serviceWorker' in navigator,
          webWorkers: typeof Worker !== 'undefined'
        };
      });

      testContexts.push({
        browser,
        context,
        page,
        browserName,
        capabilities
      });
    }
  });

  test.afterAll(async () => {
    // Cleanup all browsers
    for (const ctx of testContexts) {
      await ctx.browser.close();
    }
  });

  test('should detect and report browser capabilities correctly', async () => {
    for (const ctx of testContexts) {
      test.step(`Testing capabilities in ${ctx.browserName}`, async () => {
        console.log(`${ctx.browserName} capabilities:`, ctx.capabilities);
        
        expect(ctx.capabilities.webAssembly).toBe(true);
        expect(ctx.capabilities.webWorkers).toBe(true);
        
        // Browser-specific expectations
        if (ctx.browserName === 'chromium') {
          expect(ctx.capabilities.simd).toBe(true);
          expect(ctx.capabilities.threads).toBe(true);
          expect(ctx.capabilities.sharedArrayBuffer).toBe(true);
        } else if (ctx.browserName === 'firefox') {
          expect(ctx.capabilities.simd).toBe(true);
          // Firefox may have threads disabled by default
          // expect(ctx.capabilities.threads).toBe(true);
        } else if (ctx.browserName === 'webkit') {
          // Safari/WebKit has more limited WASM features
          // expect(ctx.capabilities.simd).toBe(false);
          // expect(ctx.capabilities.threads).toBe(false);
        }
      });
    }
  });

  test('should load appropriate WASM builds based on browser capabilities', async () => {
    for (const ctx of testContexts) {
      await test.step(`Testing WASM build selection in ${ctx.browserName}`, async () => {
        // Trigger WASM initialization
        await ctx.page.locator('[data-testid="pdf-processor"]').waitFor();
        
        const wasmBuildInfo = await ctx.page.evaluate(() => {
          return new Promise((resolve) => {
            // Mock or access the actual WASM build selection logic
            const mockWasmSelector = {
              selectOptimalBuild: (capabilities: any) => {
                if (capabilities.simd && capabilities.threads) {
                  return '/assets/mupdf-threads-simd.wasm';
                } else if (capabilities.simd) {
                  return '/assets/mupdf-simd.wasm';
                } else {
                  return '/assets/mupdf-basic.wasm';
                }
              }
            };
            
            const selectedBuild = mockWasmSelector.selectOptimalBuild({
              simd: typeof WebAssembly !== 'undefined' && WebAssembly.validate(new Uint8Array([
                0x00, 0x61, 0x73, 0x6d, 0x01, 0x00, 0x00, 0x00, 0x01, 0x05, 0x01,
                0x60, 0x00, 0x01, 0x7b, 0x03, 0x02, 0x01, 0x00, 0x0a, 0x0a, 0x01,
                0x08, 0x00, 0x41, 0x00, 0xfd, 0x0f, 0x0b
              ])),
              threads: typeof SharedArrayBuffer !== 'undefined'
            });
            
            resolve({
              selectedBuild,
              capabilities: {
                simd: typeof WebAssembly !== 'undefined' && WebAssembly.validate(new Uint8Array([
                  0x00, 0x61, 0x73, 0x6d, 0x01, 0x00, 0x00, 0x00, 0x01, 0x05, 0x01,
                  0x60, 0x00, 0x01, 0x7b, 0x03, 0x02, 0x01, 0x00, 0x0a, 0x0a, 0x01,
                  0x08, 0x00, 0x41, 0x00, 0xfd, 0x0f, 0x0b
                ])),
                threads: typeof SharedArrayBuffer !== 'undefined'
              }
            });
          });
        });

        console.log(`${ctx.browserName} WASM build selection:`, wasmBuildInfo);
        
        expect(wasmBuildInfo).toHaveProperty('selectedBuild');
        expect(wasmBuildInfo.selectedBuild).toContain('mupdf');
        expect(wasmBuildInfo.selectedBuild).toContain('.wasm');
      });
    }
  });

  test('should handle PDF file upload consistently across browsers', async () => {
    const testPdfBuffer = Buffer.from(
      '%PDF-1.4\n1 0 obj<</Type/Catalog/Pages 2 0 R>>endobj\n2 0 obj<</Type/Pages/Kids[3 0 R]/Count 1>>endobj\n3 0 obj<</Type/Page/Parent 2 0 R/MediaBox[0 0 612 792]>>endobj\nxref\n0 4\n0000000000 65535 f \n0000000015 00000 n \n0000000074 00000 n \n0000000131 00000 n \ntrailer<</Size 4/Root 1 0 R>>\nstartxref\n196\n%%EOF',
      'utf8'
    );

    for (const ctx of testContexts) {
      await test.step(`Testing file upload in ${ctx.browserName}`, async () => {
        const fileChooserPromise = ctx.page.waitForEvent('filechooser');
        await ctx.page.locator('[data-testid="file-upload-button"]').click();
        const fileChooser = await fileChooserPromise;
        
        // Create a temporary file for testing
        const tempFile = {
          name: 'test.pdf',
          mimeType: 'application/pdf',
          buffer: testPdfBuffer
        };
        
        await fileChooser.setFiles([tempFile]);
        
        // Wait for file processing to start
        await ctx.page.locator('[data-testid="processing-indicator"]').waitFor();
        
        // Verify file was processed
        await ctx.page.locator('[data-testid="pdf-viewer"]').waitFor({ timeout: 10000 });
        
        const uploadSuccess = await ctx.page.locator('[data-testid="upload-success"]').isVisible();
        expect(uploadSuccess).toBe(true);
      });
    }
  });

  test('should provide consistent PDF processing performance across browsers', async () => {
    const performanceResults: Array<{
      browser: string;
      processingTime: number;
      memoryUsage: number;
      success: boolean;
    }> = [];

    for (const ctx of testContexts) {
      await test.step(`Testing processing performance in ${ctx.browserName}`, async () => {
        // Upload test file
        const testFile = {
          name: 'performance-test.pdf',
          mimeType: 'application/pdf',
          buffer: Buffer.alloc(1024 * 1024) // 1MB test file
        };

        const fileChooserPromise = ctx.page.waitForEvent('filechooser');
        await ctx.page.locator('[data-testid="file-upload-button"]').click();
        const fileChooser = await fileChooserPromise;
        await fileChooser.setFiles([testFile]);

        // Start performance monitoring
        const startTime = Date.now();
        
        // Trigger processing
        await ctx.page.locator('[data-testid="compress-pdf-button"]').click();
        
        // Wait for processing to complete
        await ctx.page.locator('[data-testid="processing-complete"]').waitFor({ timeout: 30000 });
        
        const processingTime = Date.now() - startTime;
        
        // Get memory usage info
        const memoryInfo = await ctx.page.evaluate(() => {
          return (performance as any).memory ? {
            usedJSHeapSize: (performance as any).memory.usedJSHeapSize,
            totalJSHeapSize: (performance as any).memory.totalJSHeapSize
          } : { usedJSHeapSize: 0, totalJSHeapSize: 0 };
        });

        const success = await ctx.page.locator('[data-testid="download-button"]').isVisible();

        performanceResults.push({
          browser: ctx.browserName,
          processingTime: processingTime,
          memoryUsage: memoryInfo.usedJSHeapSize,
          success: success
        });

        expect(success).toBe(true);
        expect(processingTime).toBeLessThan(30000); // Should complete within 30 seconds
      });
    }

    // Compare performance across browsers
    console.log('Cross-browser performance comparison:', performanceResults);
    
    const avgProcessingTime = performanceResults.reduce((sum, r) => sum + r.processingTime, 0) / performanceResults.length;
    const maxProcessingTime = Math.max(...performanceResults.map(r => r.processingTime));
    const minProcessingTime = Math.min(...performanceResults.map(r => r.processingTime));
    
    // Performance variance should not be excessive
    const performanceVariance = (maxProcessingTime - minProcessingTime) / avgProcessingTime;
    expect(performanceVariance).toBeLessThan(2.0); // Max 200% variance between browsers
  });

  test('should handle fallback gracefully when advanced features are unavailable', async () => {
    for (const ctx of testContexts) {
      await test.step(`Testing fallback mechanisms in ${ctx.browserName}`, async () => {
        // Simulate disabling advanced features
        await ctx.page.addInitScript(() => {
          // Mock disabled SharedArrayBuffer for fallback testing
          if (Math.random() > 0.5) { // Randomly disable for some tests
            Object.defineProperty(window, 'SharedArrayBuffer', { value: undefined });
          }
        });
        
        await ctx.page.reload();
        await ctx.page.locator('[data-testid="pdf-processor"]').waitFor();
        
        // Check if fallback mode was activated
        const fallbackActive = await ctx.page.evaluate(() => {
          return window.pdfProcessor?.fallbackMode || false;
        });
        
        // Upload and process a file to ensure functionality works even in fallback mode
        const testFile = {
          name: 'fallback-test.pdf',
          mimeType: 'application/pdf',
          buffer: Buffer.from('%PDF-1.4\n%%EOF', 'utf8')
        };

        const fileChooserPromise = ctx.page.waitForEvent('filechooser');
        await ctx.page.locator('[data-testid="file-upload-button"]').click();
        const fileChooser = await fileChooserPromise;
        await fileChooser.setFiles([testFile]);

        // Should still work, possibly with degraded performance
        const processingResult = await ctx.page.locator('[data-testid="processing-complete"]').waitFor({ timeout: 45000 });
        expect(processingResult).toBeTruthy();
        
        if (fallbackActive) {
          console.log(`${ctx.browserName} successfully used fallback mode`);
        }
      });
    }
  });

  test('should maintain UI responsiveness during processing across browsers', async () => {
    for (const ctx of testContexts) {
      await test.step(`Testing UI responsiveness in ${ctx.browserName}`, async () => {
        // Upload a larger file to ensure longer processing time
        const largeTestFile = {
          name: 'large-test.pdf',
          mimeType: 'application/pdf',
          buffer: Buffer.alloc(5 * 1024 * 1024) // 5MB
        };

        const fileChooserPromise = ctx.page.waitForEvent('filechooser');
        await ctx.page.locator('[data-testid="file-upload-button"]').click();
        const fileChooser = await fileChooserPromise;
        await fileChooser.setFiles([largeTestFile]);

        // Start processing
        await ctx.page.locator('[data-testid="compress-pdf-button"]').click();
        
        // While processing is happening, test UI responsiveness
        const startTime = Date.now();
        
        // Click on various UI elements to test responsiveness
        await ctx.page.locator('[data-testid="settings-button"]').click();
        const settingsResponseTime = Date.now() - startTime;
        
        await ctx.page.locator('[data-testid="help-button"]').click();
        const helpResponseTime = Date.now() - startTime - settingsResponseTime;
        
        // Close dialogs
        await ctx.page.locator('[data-testid="close-dialog"]').first().click();
        
        // UI should remain responsive (under 1 second for interactions)
        expect(settingsResponseTime).toBeLessThan(1000);
        expect(helpResponseTime).toBeLessThan(1000);
        
        // Processing should eventually complete
        await ctx.page.locator('[data-testid="processing-complete"]').waitFor({ timeout: 60000 });
      });
    }
  });

  test('should handle memory pressure consistently across browsers', async () => {
    const memoryPressureResults: Array<{
      browser: string;
      initialMemory: number;
      peakMemory: number;
      finalMemory: number;
      gcTriggered: boolean;
    }> = [];

    for (const ctx of testContexts) {
      await test.step(`Testing memory pressure handling in ${ctx.browserName}`, async () => {
        // Get initial memory baseline
        const initialMemory = await ctx.page.evaluate(() => {
          return (performance as any).memory ? (performance as any).memory.usedJSHeapSize : 0;
        });

        let peakMemory = initialMemory;
        let gcTriggered = false;

        // Monitor memory during intensive operations
        const memoryMonitor = setInterval(async () => {
          const currentMemory = await ctx.page.evaluate(() => {
            return (performance as any).memory ? (performance as any).memory.usedJSHeapSize : 0;
          });
          peakMemory = Math.max(peakMemory, currentMemory);
          
          // Check if garbage collection occurred (memory decreased significantly)
          if (currentMemory < peakMemory * 0.8) {
            gcTriggered = true;
          }
        }, 1000);

        // Perform memory-intensive operations
        const largeFiles = Array.from({ length: 3 }, (_, i) => ({
          name: `memory-test-${i}.pdf`,
          mimeType: 'application/pdf',
          buffer: Buffer.alloc(2 * 1024 * 1024) // 2MB each
        }));

        for (const file of largeFiles) {
          const fileChooserPromise = ctx.page.waitForEvent('filechooser');
          await ctx.page.locator('[data-testid="file-upload-button"]').click();
          const fileChooser = await fileChooserPromise;
          await fileChooser.setFiles([file]);
          
          await ctx.page.locator('[data-testid="compress-pdf-button"]').click();
          await ctx.page.locator('[data-testid="processing-complete"]').waitFor({ timeout: 30000 });
          
          // Clear previous file before loading next
          await ctx.page.locator('[data-testid="clear-file-button"]').click();
        }

        clearInterval(memoryMonitor);

        const finalMemory = await ctx.page.evaluate(() => {
          return (performance as any).memory ? (performance as any).memory.usedJSHeapSize : 0;
        });

        memoryPressureResults.push({
          browser: ctx.browserName,
          initialMemory,
          peakMemory,
          finalMemory,
          gcTriggered
        });

        // Memory should not grow unbounded
        const memoryGrowth = (peakMemory - initialMemory) / initialMemory;
        expect(memoryGrowth).toBeLessThan(5.0); // Less than 500% memory growth
        
        // Memory should be cleaned up reasonably well
        const memoryRetention = (finalMemory - initialMemory) / (peakMemory - initialMemory);
        expect(memoryRetention).toBeLessThan(0.8); // Less than 80% memory retention
      });
    }

    console.log('Memory pressure test results:', memoryPressureResults);
  });

  test('should support all core PDF operations across browsers', async () => {
    const coreOperations = [
      { name: 'text-editing', testId: 'edit-text-button' },
      { name: 'page-rotation', testId: 'rotate-page-button' },
      { name: 'page-deletion', testId: 'delete-page-button' },
      { name: 'annotation-adding', testId: 'add-annotation-button' },
      { name: 'document-compression', testId: 'compress-pdf-button' }
    ];

    const operationResults: Array<{
      browser: string;
      operation: string;
      supported: boolean;
      performant: boolean;
    }> = [];

    for (const ctx of testContexts) {
      await test.step(`Testing core operations support in ${ctx.browserName}`, async () => {
        // Upload test file first
        const testFile = {
          name: 'operations-test.pdf',
          mimeType: 'application/pdf',
          buffer: Buffer.alloc(1024 * 1024) // 1MB
        };

        const fileChooserPromise = ctx.page.waitForEvent('filechooser');
        await ctx.page.locator('[data-testid="file-upload-button"]').click();
        const fileChooser = await fileChooserPromise;
        await fileChooser.setFiles([testFile]);
        
        await ctx.page.locator('[data-testid="pdf-viewer"]').waitFor();

        for (const operation of coreOperations) {
          const startTime = Date.now();
          
          try {
            await ctx.page.locator(`[data-testid="${operation.testId}"]`).click();
            await ctx.page.locator('[data-testid="operation-complete"]').waitFor({ timeout: 15000 });
            
            const operationTime = Date.now() - startTime;
            
            operationResults.push({
              browser: ctx.browserName,
              operation: operation.name,
              supported: true,
              performant: operationTime < 10000 // Under 10 seconds
            });
          } catch (error) {
            operationResults.push({
              browser: ctx.browserName,
              operation: operation.name,
              supported: false,
              performant: false
            });
          }
        }
      });
    }

    // Analyze results
    const supportMatrix = operationResults.reduce((matrix, result) => {
      if (!matrix[result.operation]) {
        matrix[result.operation] = {};
      }
      matrix[result.operation][result.browser] = {
        supported: result.supported,
        performant: result.performant
      };
      return matrix;
    }, {} as Record<string, Record<string, { supported: boolean; performant: boolean }>>);

    console.log('Cross-browser operation support matrix:', supportMatrix);

    // All core operations should be supported in all browsers
    for (const operation of coreOperations) {
      const operationSupport = operationResults.filter(r => r.operation === operation.name);
      const supportedCount = operationSupport.filter(r => r.supported).length;
      
      expect(supportedCount).toBeGreaterThanOrEqual(browsers.length * 0.8); // At least 80% browser support
    }
  });
});
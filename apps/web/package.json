{"name": "web", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "test": "vitest", "type-check": "tsc --noEmit"}, "dependencies": {"@headlessui/react": "^1.7.17", "@heroicons/react": "^2.2.0", "@playwright/test": "^1.55.0", "@supabase/supabase-js": "^2.55.0", "@tanstack/react-query": "^5.85.5", "@trpc/client": "^11.4.4", "@trpc/react-query": "^11.4.4", "clsx": "^2.0.0", "pako": "^2.1.0", "pdf-lib": "^1.17.1", "pdfjs-dist": "^5.4.54", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.15.0", "workbox-window": "^7.0.0", "zustand": "^4.4.1"}, "devDependencies": {"@testing-library/jest-dom": "^6.0.0", "@testing-library/react": "^14.0.0", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.15", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "jsdom": "^22.1.0", "playwright": "^1.40.0", "postcss": "^8.4.28", "tailwindcss": "^3.4.0", "typescript": "^5.3.0", "vite": "^5.0.0", "vite-plugin-pwa": "^0.16.4", "vitest": "^1.0.0"}}
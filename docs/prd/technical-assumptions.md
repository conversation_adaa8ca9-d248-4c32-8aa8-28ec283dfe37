# Technical Assumptions

## Repository Structure: Monorepo
Turborepo monorepo with npm workspaces containing:
- `apps/web` - React frontend (Vite + TypeScript + TailwindCSS)
- `apps/api` - Next.js API server for user management
- `packages/pdf-engine` - Core PDF processing engine with WebAssembly
- `packages/shared` - Shared types and utilities
- `packages/ui` - Shared UI components

## Service Architecture: Hybrid Privacy-First Model

### **BREAKTHROUGH ARCHITECTURE:** Privacy-First with Premium Server Fallback

**Default Mode:** Client-Side Processing (Privacy + Instant + Free)
- MuPDF WASM + PDF.js fallback for 80% of use cases
- All processing happens in browser - files never leave device
- WebAssembly Workers for non-blocking background processing
- Offline-capable with progressive enhancement

**Premium Fallback:** Server-Side for Complex PDFs (Credit-Based)
- Advanced OCR with table recognition
- Professional-grade format conversion (Pandoc + LibreOffice Headless)
- Enterprise compression algorithms (Ghostscript + QPDF)
- ML-powered document analysis

**User Choice Philosophy:**
- Transparent about limitations and processing options
- User decides privacy vs performance trade-offs
- No forced cloud processing - always optional
- Clear cost indication before server-side operations

## Testing Requirements
Full testing pyramid including unit tests for PDF processing logic, integration tests for PWA functionality, and end-to-end mobile testing across target browsers. Manual testing convenience methods for mobile device validation.

## Core Technical Stack (Production-Grade)

### **Frontend Framework:**
```javascript
// React + Konva.js for optimal performance
React 18.2+ + Konva.js (2D rendering)
+ Zustand (state management with persistence)
+ Progressive WASM loading
+ Vite 5.0+ (build tooling)
+ TailwindCSS 3.4+ (styling)
```

### **PDF Processing Engine:**
```javascript
// Multi-tier processing strategy
Primary: MuPDF WASM (Fast, comprehensive, production-proven)
Fallback: PDF.js (Mozilla-backed for basic operations) 
Enhancement: Custom WASM modules for advanced algorithms
Streaming: fflate for secondary compression
```

### **Performance Architecture:**
- **Event-driven** with Worker-based background processing
- **Virtual page rendering** + memory pools + LRU caching
- **Error boundaries** + document recovery + auto-save
- **Touch gestures** + keyboard shortcuts + accessibility compliance

## 10x Performance Commitments

### **1. PDF Editing - 10x Better Than Market Leaders**
- **Speed:** 3-5x faster rendering than Adobe/SmallPDF
- **Reliability:** 99.9% uptime with offline-capable processing
- **UX:** Native touch gestures + progressive WASM loading

### **2. PDF Compression - Advanced Algorithm Implementation**
- **Intelligence:** Automatic algorithm selection per document type
- **Transparency:** Before/after size predictions + quality slider
- **Performance:** Streaming compression + progressive results

### **3. PDF Conversion - Hybrid Processing Pipeline**
- **Speed:** Instant simple conversions, fast complex processing
- **Accuracy:** Multiple conversion engines per format
- **Formats:** 20+ output formats vs competitors' 5-8
- **Quality:** Layout preservation + font embedding

## Additional Technical Assumptions and Requests
- **WebAssembly Performance:** Modern mobile browsers can handle 4GB+ file processing with proper memory management and streaming techniques
- **PWA Installation:** Users will accept PWA experience over native apps for cross-platform convenience and instant updates
- **Client-Side Security:** Sensitive document processing can be secured through client-side encryption and secure memory handling
- **Integration APIs:** Notion and Google Workspace APIs provide sufficient access for seamless PDF export functionality
- **Offline Storage:** Browser storage capabilities support reasonable file caching and processing history
- **Hybrid Processing:** 80% of use cases handled client-side, 20% server-side for complex operations
- **Credit System:** Server-side operations use transparent credit-based billing model

# Monitoring and Observability

## Monitoring Stack
- **Frontend Monitoring:** Vercel Analytics + Sentry for error tracking
- **Backend Monitoring:** Vercel Functions Logs + Supabase Dashboard
- **Error Tracking:** Sentry with performance monitoring
- **Performance Monitoring:** Web Vitals tracking + Lighthouse CI

## Key Metrics

**Frontend Metrics:**
- Core Web Vitals (LCP, FID, CLS)
- JavaScript errors and crash rates
- API response times from client perspective
- PWA installation rates and offline usage

**Backend Metrics:**
- Request rate and response times
- Error rate by endpoint
- Database query performance
- Subscription conversion rates

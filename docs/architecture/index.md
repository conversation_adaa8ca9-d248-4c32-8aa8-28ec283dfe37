# MobilePDF Pro Fullstack Architecture Document

## Table of Contents

- [MobilePDF Pro Fullstack Architecture Document](#table-of-contents)
  - [Introduction](./introduction.md)
    - [Starter Template Analysis](./introduction.md#starter-template-analysis)
    - [Change Log](./introduction.md#change-log)
  - [High Level Architecture](./high-level-architecture.md)
    - [Technical Summary](./high-level-architecture.md#technical-summary)
    - [Platform and Infrastructure Choice](./high-level-architecture.md#platform-and-infrastructure-choice)
    - [Repository Structure](./high-level-architecture.md#repository-structure)
    - [High Level Architecture Diagram](./high-level-architecture.md#high-level-architecture-diagram)
    - [Architectural Patterns](./high-level-architecture.md#architectural-patterns)
  - [Server-Side Processing Infrastructure](./server-side-processing.md)
    - [Architecture Overview](./server-side-processing.md#architecture-overview)
    - [Dedicated Compute Instance Design](./server-side-processing.md#dedicated-compute-instance-design)
    - [Processing Decision Matrix](./server-side-processing.md#processing-decision-matrix)
    - [Advanced Processing Services](./server-side-processing.md#advanced-processing-services)
    - [Queue Management and Load Balancing](./server-side-processing.md#queue-management-and-load-balancing)
  - [Credit System Architecture](./credit-system-architecture.md)
    - [System Architecture](./credit-system-architecture.md#system-architecture)
    - [Real-Time Credit Tracking](./credit-system-architecture.md#real-time-credit-tracking)
    - [Cost Calculation Engine](./credit-system-architecture.md#cost-calculation-engine)
    - [Transparent Billing Engine](./credit-system-architecture.md#transparent-billing-engine)
    - [Usage Analytics and Optimization](./credit-system-architecture.md#usage-analytics-and-optimization)
  - [MuPDF WASM Integration](./mupdf-wasm-integration.md)
    - [Core Integration Architecture](./mupdf-wasm-integration.md#core-integration-architecture)
    - [Progressive Loading Strategy](./mupdf-wasm-integration.md#progressive-loading-strategy)
    - [Advanced Memory Management](./mupdf-wasm-integration.md#advanced-memory-management)
    - [Comprehensive Fallback System](./mupdf-wasm-integration.md#comprehensive-fallback-system)
    - [Advanced Text Editing Capabilities](./mupdf-wasm-integration.md#advanced-text-editing-capabilities)
  - [Performance Benchmarking Framework](./performance-benchmarking.md)
    - [Benchmarking Architecture](./performance-benchmarking.md#benchmarking-architecture)
    - [Real-Time Performance Monitor](./performance-benchmarking.md#real-time-performance-monitor)
    - [Professional Quality Assessment](./performance-benchmarking.md#professional-quality-assessment)
    - [Competitive Benchmarking System](./performance-benchmarking.md#competitive-benchmarking-system)
    - [Mobile Performance Specialization](./performance-benchmarking.md#mobile-performance-specialization)
  - [Tech Stack](./tech-stack.md)
    - [Technology Stack Table](./tech-stack.md#technology-stack-table)
  - [Data Models](./data-models.md)
    - [User](./data-models.md#user)
      - [TypeScript Interface](./data-models.md#typescript-interface)
      - [Relationships](./data-models.md#relationships)
    - [ProcessingHistory](./data-models.md#processinghistory)
      - [TypeScript Interface](./data-models.md#typescript-interface)
      - [Relationships](./data-models.md#relationships)
    - [Subscription](./data-models.md#subscription)
      - [TypeScript Interface](./data-models.md#typescript-interface)
      - [Relationships](./data-models.md#relationships)
    - [CloudStorageConnection](./data-models.md#cloudstorageconnection)
      - [TypeScript Interface](./data-models.md#typescript-interface)
      - [Relationships](./data-models.md#relationships)
  - [API Specification](./api-specification.md)
    - [tRPC Router Definitions](./api-specification.md#trpc-router-definitions)
  - [Components](./components.md)
    - [PDF Processing Engine](./components.md#pdf-processing-engine)
    - [PWA Service Worker](./components.md#pwa-service-worker)
    - [Authentication Manager](./components.md#authentication-manager)
    - [File Management Service](./components.md#file-management-service)
    - [Subscription Manager](./components.md#subscription-manager)
    - [UI Component Library](./components.md#ui-component-library)
    - [Component Diagrams](./components.md#component-diagrams)
  - [External APIs](./external-apis.md)
    - [Google Drive API](./external-apis.md#google-drive-api)
    - [Dropbox API](./external-apis.md#dropbox-api)
    - [OneDrive Graph API](./external-apis.md#onedrive-graph-api)
    - [Notion API](./external-apis.md#notion-api)
    - [Stripe API](./external-apis.md#stripe-api)
  - [Core Workflows](./core-workflows.md)
  - [Database Schema](./database-schema.md)
  - [Frontend Architecture](./frontend-architecture.md)
    - [Component Architecture](./frontend-architecture.md#component-architecture)
      - [Component Organization](./frontend-architecture.md#component-organization)
      - [Component Template](./frontend-architecture.md#component-template)
    - [State Management Architecture](./frontend-architecture.md#state-management-architecture)
      - [State Structure](./frontend-architecture.md#state-structure)
      - [State Management Patterns](./frontend-architecture.md#state-management-patterns)
    - [Routing Architecture](./frontend-architecture.md#routing-architecture)
      - [Route Organization](./frontend-architecture.md#route-organization)
      - [Protected Route Pattern](./frontend-architecture.md#protected-route-pattern)
    - [Frontend Services Layer](./frontend-architecture.md#frontend-services-layer)
      - [API Client Setup](./frontend-architecture.md#api-client-setup)
      - [Service Example](./frontend-architecture.md#service-example)
  - [Backend Architecture](./backend-architecture.md)
    - [Service Architecture](./backend-architecture.md#service-architecture)
      - [Function Organization](./backend-architecture.md#function-organization)
      - [Function Template](./backend-architecture.md#function-template)
    - [Database Architecture](./backend-architecture.md#database-architecture)
      - [Data Access Layer](./backend-architecture.md#data-access-layer)
    - [Authentication and Authorization](./backend-architecture.md#authentication-and-authorization)
      - [Auth Flow](./backend-architecture.md#auth-flow)
      - [Middleware/Guards](./backend-architecture.md#middlewareguards)
  - [Unified Project Structure](./unified-project-structure.md)
  - [Development Workflow](./development-workflow.md)
    - [Local Development Setup](./development-workflow.md#local-development-setup)
      - [Prerequisites](./development-workflow.md#prerequisites)
      - [Initial Setup](./development-workflow.md#initial-setup)
      - [Development Commands](./development-workflow.md#development-commands)
    - [Environment Configuration](./development-workflow.md#environment-configuration)
      - [Required Environment Variables](./development-workflow.md#required-environment-variables)
  - [Deployment Architecture](./deployment-architecture.md)
    - [Deployment Strategy](./deployment-architecture.md#deployment-strategy)
    - [CI/CD Pipeline](./deployment-architecture.md#cicd-pipeline)
    - [Environments](./deployment-architecture.md#environments)
  - [Security and Performance](./security-and-performance.md)
    - [Security Requirements](./security-and-performance.md#security-requirements)
    - [Performance Optimization](./security-and-performance.md#performance-optimization)
  - [Testing Strategy](./testing-strategy.md)
    - [Testing Pyramid](./testing-strategy.md#testing-pyramid)
    - [Test Organization](./testing-strategy.md#test-organization)
      - [Frontend Tests](./testing-strategy.md#frontend-tests)
      - [Backend Tests](./testing-strategy.md#backend-tests)
      - [E2E Tests](./testing-strategy.md#e2e-tests)
  - [Coding Standards](./coding-standards.md)
    - [Critical Fullstack Rules](./coding-standards.md#critical-fullstack-rules)
    - [Naming Conventions](./coding-standards.md#naming-conventions)
  - [Error Handling Strategy](./error-handling-strategy.md)
    - [Error Flow](./error-handling-strategy.md#error-flow)
    - [Error Response Format](./error-handling-strategy.md#error-response-format)
    - [Frontend Error Handling](./error-handling-strategy.md#frontend-error-handling)
    - [Backend Error Handling](./error-handling-strategy.md#backend-error-handling)
  - [Monitoring and Observability](./monitoring-and-observability.md)
    - [Monitoring Stack](./monitoring-and-observability.md#monitoring-stack)
    - [Key Metrics](./monitoring-and-observability.md#key-metrics)
  - [Checklist Results Report](./checklist-results-report.md)

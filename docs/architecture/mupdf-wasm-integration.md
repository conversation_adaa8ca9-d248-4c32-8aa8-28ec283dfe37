# MuPDF WASM Integration Architecture

## Overview

Production-grade MuPDF WebAssembly integration providing industry-standard PDF processing capabilities with comprehensive fallback strategies, advanced memory management, and base-level text layout editing capabilities.

## Core Integration Architecture

### **MuPDF WASM Module Structure**

```typescript
// Core MuPDF WASM Interface
interface MuPDFModule {
  // Initialization
  initialize(): Promise<void>;
  cleanup(): void;
  getVersion(): string;
  
  // Document Management
  loadDocument(buffer: ArrayBuffer, password?: string): Promise<PDFDocument>;
  saveDocument(document: PDFDocument, options: SaveOptions): Promise<ArrayBuffer>;
  closeDocument(document: PDFDocument): void;
  
  // Page Operations
  getPageCount(document: PDFDocument): number;
  renderPage(document: PDFDocument, pageNum: number, options: RenderOptions): Promise<ImageData>;
  getPageSize(document: PDFDocument, pageNum: number): { width: number; height: number };
  
  // Text Operations (Base-Level Editing)
  extractTextBlocks(document: PDFDocument, pageNum: number): Promise<TextBlock[]>;
  getTextLayout(document: PDFDocument, pageNum: number): Promise<TextLayout>;
  modifyTextLayout(document: PDFDocument, pageNum: number, modifications: TextModification[]): Promise<void>;
  insertText(document: PDFDocument, pageNum: number, position: Position, text: string, style: TextStyle): Promise<void>;
  deleteText(document: PDFDocument, pageNum: number, textRange: TextRange): Promise<void>;
  
  // Advanced Editing
  addAnnotation(document: PDFDocument, pageNum: number, annotation: Annotation): Promise<void>;
  removeAnnotation(document: PDFDocument, annotationId: string): Promise<void>;
  addImage(document: PDFDocument, pageNum: number, image: ImageData, position: Rectangle): Promise<void>;
  
  // Form Operations
  getFormFields(document: PDFDocument): Promise<FormField[]>;
  setFormFieldValue(document: PDFDocument, fieldId: string, value: any): Promise<void>;
  
  // Security
  encrypt(document: PDFDocument, userPassword: string, ownerPassword: string, permissions: Permissions): Promise<void>;
  decrypt(document: PDFDocument, password: string): Promise<boolean>;
}

// Base-level text layout interfaces
interface TextLayout {
  lines: TextLine[];
  paragraphs: TextParagraph[];
  columns: TextColumn[];
  fonts: FontInfo[];
  styles: StyleInfo[];
}

interface TextModification {
  type: 'insert' | 'delete' | 'modify' | 'reflow';
  textRange?: TextRange;
  position?: Position;
  newText?: string;
  newStyle?: TextStyle;
  layoutAdjustment?: LayoutAdjustment;
}

interface LayoutAdjustment {
  preserveSpacing: boolean;
  adjustLineHeight: boolean;
  reflowParagraph: boolean;
  maintainJustification: boolean;
}
```

### **Progressive Loading Strategy**

```typescript
// Progressive WASM loading for optimal mobile performance
class ProgressiveMuPDFLoader {
  private wasmModule: MuPDFModule | null = null;
  private loadingPromise: Promise<MuPDFModule> | null = null;
  
  async loadMuPDF(): Promise<MuPDFModule> {
    if (this.wasmModule) {
      return this.wasmModule;
    }
    
    if (this.loadingPromise) {
      return this.loadingPromise;
    }
    
    this.loadingPromise = this.performProgressiveLoad();
    return this.loadingPromise;
  }
  
  private async performProgressiveLoad(): Promise<MuPDFModule> {
    try {
      // 1. Check WebAssembly support
      if (!this.hasWASMSupport()) {
        throw new Error('WebAssembly not supported');
      }
      
      // 2. Load core module with progress tracking
      const wasmUrl = this.selectOptimalWASMBuild();
      const wasmBytes = await this.loadWithProgress(wasmUrl);
      
      // 3. Initialize with memory management
      const module = await this.initializeWASM(wasmBytes);
      
      // 4. Warm up with small test document
      await this.warmUpModule(module);
      
      this.wasmModule = module;
      return module;
      
    } catch (error) {
      console.error('MuPDF WASM loading failed:', error);
      throw new MuPDFLoadingError(error.message);
    }
  }
  
  private selectOptimalWASMBuild(): string {
    const capabilities = this.detectBrowserCapabilities();
    
    if (capabilities.hasThreads && capabilities.hasSIMD) {
      return '/assets/mupdf-threads-simd.wasm';
    } else if (capabilities.hasSIMD) {
      return '/assets/mupdf-simd.wasm';
    } else if (capabilities.hasThreads) {
      return '/assets/mupdf-threads.wasm';
    } else {
      return '/assets/mupdf-basic.wasm';
    }
  }
}
```

### **Advanced Memory Management**

```typescript
// Sophisticated memory management for large PDF processing
class MuPDFMemoryManager {
  private memoryPool: MemoryPool;
  private documentCache: LRUCache<string, PDFDocument>;
  private renderCache: LRUCache<string, ImageData>;
  
  constructor() {
    this.memoryPool = new MemoryPool({
      initialSize: 64 * 1024 * 1024, // 64MB
      maxSize: 2 * 1024 * 1024 * 1024, // 2GB
      chunkSize: 16 * 1024 * 1024 // 16MB chunks
    });
    
    this.documentCache = new LRUCache({
      max: 5, // Maximum 5 documents in memory
      dispose: (document) => this.cleanupDocument(document)
    });
    
    this.renderCache = new LRUCache({
      max: 20, // Maximum 20 rendered pages
      maxAge: 5 * 60 * 1000 // 5 minutes
    });
  }
  
  async loadDocumentWithMemoryManagement(buffer: ArrayBuffer): Promise<PDFDocument> {
    // Check available memory
    const requiredMemory = this.estimateDocumentMemory(buffer);
    await this.ensureAvailableMemory(requiredMemory);
    
    // Load document with memory tracking
    const documentId = this.generateDocumentId(buffer);
    
    if (this.documentCache.has(documentId)) {
      return this.documentCache.get(documentId)!;
    }
    
    const document = await this.wasmModule.loadDocument(buffer);
    this.documentCache.set(documentId, document);
    
    // Track memory usage
    this.memoryPool.allocate(requiredMemory, documentId);
    
    return document;
  }
  
  private async ensureAvailableMemory(requiredBytes: number): Promise<void> {
    const availableMemory = this.memoryPool.getAvailableMemory();
    
    if (availableMemory < requiredBytes) {
      // Free least recently used documents
      const bytesToFree = requiredBytes - availableMemory;
      await this.freeMemory(bytesToFree);
    }
  }
  
  async processLargeDocument(
    document: PDFDocument, 
    operation: ProcessingOperation
  ): Promise<ProcessingResult> {
    // Streaming processing for large documents
    const pageCount = this.wasmModule.getPageCount(document);
    const batchSize = this.calculateOptimalBatchSize(document);
    
    const results: PageResult[] = [];
    
    for (let i = 0; i < pageCount; i += batchSize) {
      const batch = Array.from(
        { length: Math.min(batchSize, pageCount - i) }, 
        (_, index) => i + index
      );
      
      // Process batch with memory cleanup
      const batchResults = await this.processBatch(document, batch, operation);
      results.push(...batchResults);
      
      // Force garbage collection between batches
      if (i + batchSize < pageCount) {
        await this.performGarbageCollection();
      }
    }
    
    return this.combineResults(results);
  }
}
```

### **Comprehensive Fallback System**

```typescript
// Multi-tier fallback strategy
class PDFProcessingEngine {
  private mupdfLoader: ProgressiveMuPDFLoader;
  private pdfjsEngine: PDFJSEngine;
  private serverFallback: ServerProcessingClient;
  
  async processDocument(
    document: File, 
    operation: ProcessingOperation
  ): Promise<ProcessingResult> {
    const fallbackChain = this.buildFallbackChain(operation);
    
    for (const processor of fallbackChain) {
      try {
        const result = await this.attemptProcessing(processor, document, operation);
        
        // Log successful processing method
        await this.logProcessingMethod(processor.name, operation);
        
        return result;
        
      } catch (error) {
        console.warn(`${processor.name} failed:`, error);
        
        // Check if we should continue to next fallback
        if (this.shouldContinueFallback(error, processor)) {
          continue;
        } else {
          throw error;
        }
      }
    }
    
    throw new ProcessingFailedError('All processing methods failed');
  }
  
  private buildFallbackChain(operation: ProcessingOperation): ProcessingEngine[] {
    const chain: ProcessingEngine[] = [];
    
    // Primary: MuPDF WASM
    if (this.isMuPDFCapable(operation)) {
      chain.push({
        name: 'MuPDF WASM',
        process: (doc, op) => this.procesWithMuPDF(doc, op),
        capabilities: ['text_editing', 'advanced_rendering', 'form_editing', 'annotations']
      });
    }
    
    // Secondary: PDF.js
    if (this.isPDFJSCapable(operation)) {
      chain.push({
        name: 'PDF.js',
        process: (doc, op) => this.processWithPDFJS(doc, op),
        capabilities: ['basic_rendering', 'text_extraction', 'simple_annotations']
      });
    }
    
    // Tertiary: Server-side processing
    chain.push({
      name: 'Server Processing',
      process: (doc, op) => this.processWithServer(doc, op),
      capabilities: ['all_operations']
    });
    
    return chain;
  }
  
  private async procesWithMuPDF(
    document: File, 
    operation: ProcessingOperation
  ): Promise<ProcessingResult> {
    try {
      // Load MuPDF WASM module
      const mupdf = await this.mupdfLoader.loadMuPDF();
      
      // Convert File to ArrayBuffer
      const buffer = await document.arrayBuffer();
      
      // Load document with memory management
      const pdfDoc = await this.memoryManager.loadDocumentWithMemoryManagement(buffer);
      
      // Execute operation based on type
      switch (operation.type) {
        case 'text_editing':
          return await this.performTextEditing(mupdf, pdfDoc, operation);
        case 'rendering':
          return await this.performRendering(mupdf, pdfDoc, operation);
        case 'compression':
          return await this.performCompression(mupdf, pdfDoc, operation);
        case 'annotation':
          return await this.performAnnotation(mupdf, pdfDoc, operation);
        default:
          throw new UnsupportedOperationError(operation.type);
      }
      
    } catch (error) {
      // Convert WASM errors to actionable errors
      throw this.convertWASMError(error);
    }
  }
}
```

### **Advanced Text Editing Capabilities**

```typescript
// Base-level text layout modification
class AdvancedTextEditor {
  async modifyTextLayout(
    mupdf: MuPDFModule,
    document: PDFDocument,
    pageNum: number,
    modifications: TextLayoutModification[]
  ): Promise<void> {
    // Get current text layout
    const layout = await mupdf.getTextLayout(document, pageNum);
    
    for (const modification of modifications) {
      switch (modification.type) {
        case 'insert_text':
          await this.insertTextWithLayout(mupdf, document, pageNum, modification);
          break;
        case 'modify_text':
          await this.modifyExistingText(mupdf, document, pageNum, modification);
          break;
        case 'reflow_paragraph':
          await this.reflowParagraph(mupdf, document, pageNum, modification);
          break;
        case 'adjust_spacing':
          await this.adjustTextSpacing(mupdf, document, pageNum, modification);
          break;
      }
    }
    
    // Validate layout integrity
    await this.validateTextLayout(mupdf, document, pageNum);
  }
  
  private async insertTextWithLayout(
    mupdf: MuPDFModule,
    document: PDFDocument,
    pageNum: number,
    modification: TextLayoutModification
  ): Promise<void> {
    const { position, text, style, layoutOptions } = modification;
    
    // Analyze surrounding text context
    const context = await this.analyzeTextContext(mupdf, document, pageNum, position);
    
    // Calculate optimal text placement
    const placement = this.calculateTextPlacement(context, text, style);
    
    // Adjust surrounding content if needed
    if (layoutOptions.pushExistingContent) {
      await this.adjustSurroundingContent(mupdf, document, pageNum, placement);
    }
    
    // Insert text with proper styling
    await mupdf.insertText(document, pageNum, placement.position, text, placement.style);
    
    // Reflow if requested
    if (layoutOptions.reflowParagraph) {
      await this.reflowAffectedParagraphs(mupdf, document, pageNum, placement);
    }
  }
  
  private async modifyExistingText(
    mupdf: MuPDFModule,
    document: PDFDocument,
    pageNum: number,
    modification: TextLayoutModification
  ): Promise<void> {
    const { textRange, newText, newStyle, layoutOptions } = modification;
    
    // Extract current text properties
    const currentLayout = await mupdf.getTextLayout(document, pageNum);
    const affectedBlocks = this.findAffectedTextBlocks(currentLayout, textRange);
    
    // Calculate layout impact
    const layoutImpact = this.calculateLayoutImpact(affectedBlocks, newText, newStyle);
    
    // Perform modification
    await mupdf.deleteText(document, pageNum, textRange);
    await mupdf.insertText(document, pageNum, textRange.start, newText, newStyle);
    
    // Handle layout adjustments
    if (layoutImpact.requiresReflow) {
      await this.handleLayoutReflow(mupdf, document, pageNum, layoutImpact);
    }
  }
}
```

### **Error Handling and Recovery**

```typescript
// Comprehensive error handling for production reliability
class MuPDFErrorHandler {
  async handleWASMError(error: any, operation: ProcessingOperation): Promise<ProcessingResult> {
    const errorType = this.classifyError(error);
    
    switch (errorType) {
      case 'memory_exhausted':
        return await this.handleMemoryExhaustion(error, operation);
      case 'corrupt_document':
        return await this.handleCorruptDocument(error, operation);
      case 'unsupported_feature':
        return await this.handleUnsupportedFeature(error, operation);
      case 'wasm_crash':
        return await this.handleWASMCrash(error, operation);
      default:
        return await this.handleUnknownError(error, operation);
    }
  }
  
  private async handleMemoryExhaustion(
    error: any, 
    operation: ProcessingOperation
  ): Promise<ProcessingResult> {
    // Attempt memory cleanup and retry
    await this.memoryManager.performEmergencyCleanup();
    
    // Try with reduced memory profile
    const reducedOperation = this.createReducedMemoryOperation(operation);
    
    try {
      return await this.retryWithReducedProfile(reducedOperation);
    } catch (retryError) {
      // Fall back to server processing
      return await this.fallbackToServer(operation, 'memory_exhausted');
    }
  }
  
  private async handleCorruptDocument(
    error: any, 
    operation: ProcessingOperation
  ): Promise<ProcessingResult> {
    // Attempt document repair
    const repairResult = await this.attemptDocumentRepair(operation.document);
    
    if (repairResult.success) {
      return await this.retryWithRepairedDocument(operation, repairResult.document);
    }
    
    // Extract what we can and inform user
    const partialResult = await this.extractPartialContent(operation.document);
    return {
      success: false,
      partialResult,
      error: new CorruptDocumentError('Document contains corruption, partial processing completed')
    };
  }
}
```

### **Performance Optimization**

```typescript
// Advanced performance optimization for mobile devices
class MuPDFPerformanceOptimizer {
  private performanceProfile: PerformanceProfile;
  
  constructor() {
    this.performanceProfile = this.detectPerformanceProfile();
  }
  
  optimizeForDevice(operation: ProcessingOperation): ProcessingConfiguration {
    const config: ProcessingConfiguration = {
      renderingDPI: this.selectOptimalDPI(),
      batchSize: this.calculateOptimalBatchSize(),
      memoryLimit: this.determineMemoryLimit(),
      useWebWorkers: this.shouldUseWebWorkers(),
      enableSIMD: this.canUseSIMD(),
      compressionLevel: this.selectCompressionLevel()
    };
    
    // Mobile-specific optimizations
    if (this.performanceProfile.isMobile) {
      config.renderingDPI = Math.min(config.renderingDPI, 150);
      config.batchSize = Math.max(1, Math.floor(config.batchSize / 2));
      config.useProgressiveRendering = true;
    }
    
    // Low-memory device optimizations
    if (this.performanceProfile.memoryConstrained) {
      config.enableStreamingProcessing = true;
      config.aggressiveGarbageCollection = true;
      config.renderCache = false;
    }
    
    return config;
  }
  
  async measureProcessingPerformance(
    operation: ProcessingOperation
  ): Promise<PerformanceMetrics> {
    const startTime = performance.now();
    const startMemory = this.getCurrentMemoryUsage();
    
    try {
      const result = await this.executeOperation(operation);
      
      const endTime = performance.now();
      const endMemory = this.getCurrentMemoryUsage();
      
      return {
        processingTime: endTime - startTime,
        memoryUsage: endMemory - startMemory,
        success: true,
        operationsPerSecond: this.calculateOPS(operation, endTime - startTime),
        qualityScore: await this.assessOutputQuality(result)
      };
      
    } catch (error) {
      return {
        processingTime: performance.now() - startTime,
        memoryUsage: 0,
        success: false,
        error: error.message
      };
    }
  }
}
```

This MuPDF WASM integration provides production-grade PDF processing with advanced text editing capabilities, comprehensive fallback strategies, and optimized performance for mobile devices.
# Error Handling Strategy

## Error Flow
```mermaid
sequenceDiagram
    participant UI as Frontend UI
    participant Store as State Store
    participant API as tRPC Client
    participant Server as Edge Function
    participant External as External API

    UI->>API: API call with error
    API->>Server: tRPC request
    Server->>External: External API call
    External--xServer: API error
    Server--xAPI: TRPCError with code
    API--xStore: Typed error object
    Store--xUI: Error state update
    UI->>UI: Show error message
    UI->>UI: Offer retry/fallback
```

## Error Response Format
```typescript
interface ApiError {
  error: {
    code: string;
    message: string;
    details?: Record<string, any>;
    timestamp: string;
    requestId: string;
  };
}
```

## Frontend Error Handling
```typescript
import { TRPCClientError } from '@trpc/client';
import { toast } from '~/components/ui/toast';

export function useErrorHandler() {
  return {
    handleError: (error: unknown) => {
      if (error instanceof TRPCClientError) {
        switch (error.data?.code) {
          case 'UNAUTHORIZED':
            window.location.href = '/login';
            break;
          case 'FORBIDDEN':
            toast.error('You need a premium subscription for this feature');
            break;
          case 'TOO_MANY_REQUESTS':
            toast.error('Please slow down and try again');
            break;
          default:
            toast.error(error.message || 'Something went wrong');
        }
      } else {
        toast.error('An unexpected error occurred');
        console.error('Unexpected error:', error);
      }
    }
  };
}
```

## Backend Error Handling
```typescript
import { TRPCError } from '@trpc/server';
import { ZodError } from 'zod';

export function handleServiceError(error: unknown): never {
  if (error instanceof ZodError) {
    throw new TRPCError({
      code: 'BAD_REQUEST',
      message: 'Invalid input data',
      cause: error,
    });
  }

  if (error instanceof TRPCError) {
    throw error;
  }

  console.error('Unexpected service error:', error);
  
  throw new TRPCError({
    code: 'INTERNAL_SERVER_ERROR',
    message: 'An unexpected error occurred',
  });
}
```

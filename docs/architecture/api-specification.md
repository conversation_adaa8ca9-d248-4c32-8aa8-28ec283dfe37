# API Specification

## tRPC Router Definitions

```typescript
import { z } from 'zod';
import { createTRPCRouter, publicProcedure, protectedProcedure } from '~/server/api/trpc';

// Auth Router
export const authRouter = createTRPCRouter({
  register: publicProcedure
    .input(z.object({
      email: z.string().email(),
      password: z.string().min(8),
      name: z.string().min(1)
    }))
    .output(z.object({
      user: z.object({
        id: z.string(),
        email: z.string(),
        name: z.string()
      }),
      session: z.object({
        access_token: z.string(),
        expires_at: z.string()
      })
    }))
    .mutation(async ({ input }) => {
      // Supabase Auth registration
    }),

  login: publicProcedure
    .input(z.object({
      email: z.string().email(),
      password: z.string()
    }))
    .output(z.object({
      user: z.object({
        id: z.string(),
        email: z.string(),
        name: z.string(),
        subscription_tier: z.enum(['free', 'premium'])
      }),
      session: z.object({
        access_token: z.string(),
        expires_at: z.string()
      })
    }))
    .mutation(async ({ input }) => {
      // Supabase Auth login
    })
});

// User Router
export const userRouter = createTRPCRouter({
  getProfile: protectedProcedure
    .output(z.object({
      id: z.string(),
      email: z.string(),
      name: z.string(),
      avatar_url: z.string().nullable(),
      subscription_tier: z.enum(['free', 'premium']),
      created_at: z.string(),
      updated_at: z.string()
    }))
    .query(async ({ ctx }) => {
      // Get user profile
    }),

  updateProfile: protectedProcedure
    .input(z.object({
      name: z.string().optional(),
      avatar_url: z.string().nullable().optional()
    }))
    .output(z.object({ success: z.boolean() }))
    .mutation(async ({ input, ctx }) => {
      // Update user profile
    })
});

// Processing History Router
export const historyRouter = createTRPCRouter({
  getHistory: protectedProcedure
    .input(z.object({
      limit: z.number().min(1).max(100).default(20),
      offset: z.number().min(0).default(0),
      operation_type: z.enum(['compress', 'sign', 'batch', 'watermark']).optional()
    }))
    .output(z.object({
      items: z.array(z.object({
        id: z.string(),
        original_filename: z.string(),
        operation_type: z.enum(['compress', 'sign', 'batch', 'watermark']),
        file_size_before: z.number(),
        file_size_after: z.number(),
        processing_time_ms: z.number(),
        created_at: z.string()
      })),
      total: z.number(),
      has_more: z.boolean()
    }))
    .query(async ({ input, ctx }) => {
      // Get paginated history
    }),

  recordProcessing: protectedProcedure
    .input(z.object({
      original_filename: z.string(),
      operation_type: z.enum(['compress', 'sign', 'batch', 'watermark']),
      settings: z.record(z.any()),
      processing_time_ms: z.number(),
      file_size_before: z.number(),
      file_size_after: z.number()
    }))
    .output(z.object({
      id: z.string(),
      created_at: z.string()
    }))
    .mutation(async ({ input, ctx }) => {
      // Record processing session
    })
});

// Subscription Router
export const subscriptionRouter = createTRPCRouter({
  getSubscription: protectedProcedure
    .output(z.object({
      id: z.string(),
      status: z.enum(['active', 'canceled', 'past_due', 'trial']),
      current_period_start: z.string(),
      current_period_end: z.string(),
      cancel_at_period_end: z.boolean()
    }).nullable())
    .query(async ({ ctx }) => {
      // Get subscription status
    }),

  createCheckoutSession: protectedProcedure
    .input(z.object({
      price_id: z.string(),
      success_url: z.string(),
      cancel_url: z.string()
    }))
    .output(z.object({
      checkout_url: z.string()
    }))
    .mutation(async ({ input, ctx }) => {
      // Create Stripe checkout session
    }),

  cancelSubscription: protectedProcedure
    .input(z.object({
      cancel_at_period_end: z.boolean().default(true)
    }))
    .output(z.object({ success: z.boolean() }))
    .mutation(async ({ input, ctx }) => {
      // Cancel subscription
    })
});

// Cloud Storage Router
export const cloudStorageRouter = createTRPCRouter({
  getConnections: protectedProcedure
    .output(z.array(z.object({
      id: z.string(),
      provider: z.enum(['google_drive', 'dropbox', 'onedrive']),
      provider_user_id: z.string(),
      created_at: z.string(),
      last_used_at: z.string().nullable()
    })))
    .query(async ({ ctx }) => {
      // Get cloud storage connections
    }),

  initiateConnection: protectedProcedure
    .input(z.object({
      provider: z.enum(['google_drive', 'dropbox', 'onedrive']),
      redirect_uri: z.string()
    }))
    .output(z.object({
      auth_url: z.string(),
      state: z.string()
    }))
    .mutation(async ({ input, ctx }) => {
      // Start OAuth flow
    }),

  completeConnection: protectedProcedure
    .input(z.object({
      provider: z.enum(['google_drive', 'dropbox', 'onedrive']),
      code: z.string(),
      state: z.string()
    }))
    .output(z.object({
      connection_id: z.string(),
      success: z.boolean()
    }))
    .mutation(async ({ input, ctx }) => {
      // Complete OAuth and store tokens
    })
});

// Root App Router
export const appRouter = createTRPCRouter({
  auth: authRouter,
  user: userRouter,
  history: historyRouter,
  subscription: subscriptionRouter,
  cloudStorage: cloudStorageRouter
});

export type AppRouter = typeof appRouter;
```

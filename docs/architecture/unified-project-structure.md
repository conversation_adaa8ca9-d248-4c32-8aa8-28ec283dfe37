# Unified Project Structure

```plaintext
mobilepdf-pro/
├── .github/                    # CI/CD workflows
│   └── workflows/
│       ├── ci.yaml
│       ├── deploy.yaml
│       └── release.yaml
├── apps/                       # Application packages
│   ├── web/                    # Frontend PWA application
│   │   ├── src/
│   │   │   ├── components/     # UI components
│   │   │   ├── pages/          # Page components/routes
│   │   │   ├── hooks/          # Custom React hooks
│   │   │   ├── services/       # API client services
│   │   │   ├── stores/         # Zustand state stores
│   │   │   ├── styles/         # Global styles/themes
│   │   │   ├── utils/          # Frontend utilities
│   │   │   ├── workers/        # Web Workers for PDF processing
│   │   │   └── types/          # Frontend-specific types
│   │   ├── public/             # Static assets
│   │   ├── tests/              # Frontend tests
│   │   └── package.json
│   └── api/                    # Backend serverless functions
│       ├── src/
│       │   ├── routers/         # tRPC API routes
│       │   ├── services/        # Business logic services
│       │   ├── middleware/      # API middleware
│       │   ├── utils/           # Backend utilities
│       │   └── trpc.ts          # tRPC configuration
│       ├── tests/               # Backend tests
│       └── package.json
├── packages/                   # Shared packages
│   ├── shared/                 # Shared types/utilities
│   │   ├── src/
│   │   │   ├── types/          # TypeScript interfaces
│   │   │   ├── constants/      # Shared constants
│   │   │   └── utils/          # Shared utilities
│   │   └── package.json
│   ├── ui/                     # Shared UI components
│   ├── pdf-engine/             # WebAssembly PDF processing
│   └── config/                 # Shared configuration
├── infrastructure/             # Infrastructure as Code
│   ├── vercel/                 # Vercel deployment config
│   ├── supabase/               # Supabase configuration
│   └── monitoring/             # Monitoring setup
├── scripts/                    # Build/deploy scripts
├── docs/                       # Documentation
├── .env.example               # Environment template
├── package.json               # Root package.json
├── turbo.json                 # Turborepo configuration
└── README.md                  # Project overview
```

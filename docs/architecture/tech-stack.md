# Tech Stack

## Technology Stack Table

| Category | Technology | Version | Purpose | Rationale |
|----------|------------|---------|---------|-----------|
| Frontend Language | TypeScript | 5.3+ | Type-safe development across entire stack | Essential for WebAssembly interop and shared types between frontend/backend |
| Frontend Framework | React | 18.2+ | Component-based UI with PWA capabilities | Mature ecosystem, excellent PWA support, optimal mobile performance |
| UI Component Library | Headless UI + Tailwind CSS | 1.7+ / 3.4+ | Accessible, customizable mobile-first components | Headless components ensure accessibility, Tailwind enables rapid mobile-responsive design |
| State Management | Zustand | 4.4+ | Lightweight state management for PWA | Simple API, excellent TypeScript support, minimal bundle impact for mobile |
| Backend Language | TypeScript | 5.3+ | Consistent language across fullstack | Shared types and utilities reduce development friction |
| Backend Framework | Next.js API Routes | 14.0+ | Serverless functions with edge deployment | Seamless integration with frontend, automatic edge optimization |
| API Style | REST + tRPC | tRPC 10.0+ | Type-safe API calls with REST fallbacks | End-to-end type safety while maintaining standard REST for integrations |
| Database | PostgreSQL (Supabase) | 15+ | Relational data for users/subscriptions | ACID compliance for billing, real-time subscriptions, excellent JSON support |
| Cache | Vercel Edge Cache | Built-in | Static asset and API response caching | Automatic edge caching reduces mobile latency globally |
| File Storage | Browser APIs + Cloud Integration | Native | Client-side storage with cloud sync | Privacy-first approach, no server storage required |
| Authentication | Supabase Auth | 2.0+ | JWT-based auth with social providers | Built-in security, mobile-optimized flows, subscription integration |
| Frontend Testing | Vitest + Testing Library | 1.0+ / 14.0+ | Fast unit and integration testing | Vite-based speed, excellent React component testing |
| Backend Testing | Vitest | 1.0+ | API endpoint and function testing | Consistent testing approach across frontend/backend |
| E2E Testing | Playwright | 1.40+ | Cross-browser mobile testing | Excellent mobile device emulation and PWA testing |
| Build Tool | Vite | 5.0+ | Fast development and production builds | Optimal for PWA, excellent WebAssembly support |
| Bundler | Rollup (via Vite) | 4.0+ | Efficient production bundling | Tree-shaking for minimal mobile bundle sizes |
| IaC Tool | Vercel CLI | Latest | Infrastructure as code for deployment | Declarative configuration, integrated with platform |
| CI/CD | GitHub Actions | Latest | Automated testing and deployment | Free tier sufficient, excellent ecosystem integration |
| Monitoring | Vercel Analytics + Sentry | Latest / 7.0+ | Performance and error tracking | Real user monitoring for mobile performance optimization |
| Logging | Vercel Functions Logs | Built-in | Serverless function monitoring | Integrated platform logging, sufficient for minimal backend |
| CSS Framework | Tailwind CSS | 3.4+ | Utility-first responsive design | Excellent mobile-first approach, small production bundles |
| PDF Processing Engine | MuPDF WASM | Latest | Production-grade PDF editing and rendering | Industry-standard PDF engine with comprehensive editing capabilities |
| OCR Engine | Tesseract.js | 4.0+ | Client-side optical character recognition | Accurate text recognition for scanned documents |
| Canvas Rendering | Konva.js | 9.0+ | Advanced 2D graphics and interactive editing | High-performance canvas library for PDF editing UI |
| Text Processing | Web Fonts API | Native | Font management and text rendering | Browser-native font handling for PDF text editing |
| Image Processing | Canvas API + Custom | Native | Image optimization and format conversion | Client-side image processing for compression |

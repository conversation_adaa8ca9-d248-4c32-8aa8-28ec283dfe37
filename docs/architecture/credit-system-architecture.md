# Credit System Architecture

## Overview

The credit system provides transparent, pay-as-you-go billing for server-side processing operations, completely separate from subscription management, enabling users to maintain full control over their processing costs.

## System Architecture

### **Core Components**

```typescript
// Credit System Core Interfaces
interface CreditSystem {
  balance: CreditBalance;
  transactions: CreditTransaction[];
  calculator: CostCalculator;
  monitor: UsageMonitor;
  billing: BillingEngine;
}

interface CreditBalance {
  userId: string;
  currentBalance: number;
  reservedCredits: number; // For pending operations
  lastUpdated: Date;
  autoRechargeEnabled: boolean;
  autoRechargeThreshold: number;
  autoRechargeAmount: number;
}

interface CreditTransaction {
  id: string;
  userId: string;
  type: TransactionType;
  amount: number;
  description: string;
  operationId?: string;
  timestamp: Date;
  metadata: TransactionMetadata;
}

enum TransactionType {
  PURCHASE = 'purchase',
  CONSUMPTION = 'consumption',
  REFUND = 'refund',
  AUTO_RECHARGE = 'auto_recharge',
  BONUS = 'bonus'
}
```

### **Real-Time Credit Tracking**

```typescript
interface CreditTracker {
  checkBalance(userId: string): Promise<number>;
  reserveCredits(userId: string, amount: number): Promise<ReservationResult>;
  consumeCredits(reservationId: string, actualAmount: number): Promise<boolean>;
  releaseReservation(reservationId: string): Promise<boolean>;
  getUsageHistory(userId: string, timeframe: TimeFrame): Promise<UsageHistory>;
}

interface ReservationResult {
  reservationId: string;
  reserved: boolean;
  currentBalance: number;
  reservedAmount: number;
  expiresAt: Date;
}

// Redis-based real-time tracking
class RedisCreditTracker implements CreditTracker {
  private redis: Redis;
  
  async checkBalance(userId: string): Promise<number> {
    const balance = await this.redis.hget(`user:${userId}:credits`, 'balance');
    return parseFloat(balance || '0');
  }

  async reserveCredits(userId: string, amount: number): Promise<ReservationResult> {
    const reservationId = generateUUID();
    
    // Atomic reservation using Redis transaction
    const multi = this.redis.multi();
    multi.hget(`user:${userId}:credits`, 'balance');
    multi.hget(`user:${userId}:credits`, 'reserved');
    
    const results = await multi.exec();
    const currentBalance = parseFloat(results[0][1] as string || '0');
    const currentReserved = parseFloat(results[1][1] as string || '0');
    
    if (currentBalance >= amount) {
      // Reserve credits
      const reserveMulti = this.redis.multi();
      reserveMulti.hset(`user:${userId}:credits`, 'reserved', currentReserved + amount);
      reserveMulti.setex(`reservation:${reservationId}`, 3600, JSON.stringify({
        userId,
        amount,
        createdAt: new Date()
      }));
      
      await reserveMulti.exec();
      
      return {
        reservationId,
        reserved: true,
        currentBalance,
        reservedAmount: amount,
        expiresAt: new Date(Date.now() + 3600000) // 1 hour
      };
    }
    
    return {
      reservationId: '',
      reserved: false,
      currentBalance,
      reservedAmount: 0,
      expiresAt: new Date()
    };
  }
}
```

## Cost Calculation Engine

### **Dynamic Pricing Model**

```typescript
interface CostCalculator {
  calculateOperationCost(operation: ProcessingOperation, document: DocumentMetadata): Promise<CostEstimate>;
  getProcessingRates(): Promise<ProcessingRates>;
  estimateDocumentComplexity(document: DocumentMetadata): Promise<ComplexityScore>;
}

interface CostEstimate {
  baseOperation: number;
  documentComplexity: number;
  fileSize: number;
  additionalServices: number;
  total: number;
  breakdown: CostBreakdown[];
  confidence: number; // 0-1
}

interface ProcessingRates {
  baseRates: {
    ocr_per_page: number;
    compression_per_mb: number;
    conversion_per_page: number;
    ml_analysis_per_document: number;
  };
  complexityMultipliers: {
    simple: number;    // 1.0x
    moderate: number;  // 1.5x
    complex: number;   // 2.0x
    enterprise: number; // 3.0x
  };
  fileSizeMultipliers: {
    small: number;     // <10MB: 1.0x
    medium: number;    // 10-50MB: 1.2x
    large: number;     // 50-200MB: 1.5x
    xlarge: number;    // >200MB: 2.0x
  };
}

// Intelligent cost calculation
class SmartCostCalculator implements CostCalculator {
  async calculateOperationCost(
    operation: ProcessingOperation, 
    document: DocumentMetadata
  ): Promise<CostEstimate> {
    const rates = await this.getProcessingRates();
    const complexity = await this.estimateDocumentComplexity(document);
    
    let baseCost = 0;
    
    // Base operation costs
    switch (operation.type) {
      case 'advanced_ocr':
        baseCost = rates.baseRates.ocr_per_page * document.pageCount;
        break;
      case 'professional_compression':
        baseCost = rates.baseRates.compression_per_mb * (document.fileSizeMB || 1);
        break;
      case 'format_conversion':
        baseCost = rates.baseRates.conversion_per_page * document.pageCount;
        break;
      case 'ml_analysis':
        baseCost = rates.baseRates.ml_analysis_per_document;
        break;
    }
    
    // Apply complexity multiplier
    const complexityMultiplier = this.getComplexityMultiplier(complexity, rates);
    const fileSizeMultiplier = this.getFileSizeMultiplier(document.fileSizeMB || 1, rates);
    
    const total = baseCost * complexityMultiplier * fileSizeMultiplier;
    
    return {
      baseOperation: baseCost,
      documentComplexity: baseCost * (complexityMultiplier - 1),
      fileSize: baseCost * complexityMultiplier * (fileSizeMultiplier - 1),
      additionalServices: 0,
      total,
      breakdown: this.generateCostBreakdown(baseCost, complexityMultiplier, fileSizeMultiplier),
      confidence: complexity.confidence
    };
  }
}
```

## Transparent Billing Engine

### **User-Friendly Cost Display**

```typescript
interface BillingDisplay {
  estimatedCost: number;
  actualCost?: number;
  costBreakdown: CostItem[];
  comparisonData: CostComparison;
  savingsFromClientSide?: number;
}

interface CostItem {
  category: string;
  description: string;
  quantity: number;
  unitPrice: number;
  total: number;
}

interface CostComparison {
  competitorCosts: {
    adobe: number;
    smallpdf: number;
    others: number;
  };
  savings: number;
  savingsPercentage: number;
}

// Transparent cost presentation
class TransparentBillingEngine {
  async generateCostDisplay(
    operation: ProcessingOperation,
    document: DocumentMetadata
  ): Promise<BillingDisplay> {
    const estimate = await this.costCalculator.calculateOperationCost(operation, document);
    const comparison = await this.generateCompetitorComparison(operation, document);
    
    return {
      estimatedCost: estimate.total,
      costBreakdown: [
        {
          category: 'Base Processing',
          description: `${operation.type} for ${document.pageCount} pages`,
          quantity: document.pageCount,
          unitPrice: estimate.baseOperation / document.pageCount,
          total: estimate.baseOperation
        },
        {
          category: 'Complexity Adjustment',
          description: 'Document complexity multiplier',
          quantity: 1,
          unitPrice: estimate.documentComplexity,
          total: estimate.documentComplexity
        },
        {
          category: 'File Size Adjustment',
          description: `Processing ${document.fileSizeMB}MB file`,
          quantity: 1,
          unitPrice: estimate.fileSize,
          total: estimate.fileSize
        }
      ],
      comparisonData: comparison,
      savingsFromClientSide: this.calculateClientSideSavings(operation)
    };
  }

  private async generateCompetitorComparison(
    operation: ProcessingOperation,
    document: DocumentMetadata
  ): Promise<CostComparison> {
    // Competitor cost analysis
    const competitorCosts = {
      adobe: this.estimateAdobeCost(operation, document),
      smallpdf: this.estimateSmallPDFCost(operation, document),
      others: this.estimateOthersCost(operation, document)
    };
    
    const ourCost = await this.costCalculator.calculateOperationCost(operation, document);
    const averageCompetitorCost = Object.values(competitorCosts).reduce((a, b) => a + b, 0) / 3;
    
    return {
      competitorCosts,
      savings: averageCompetitorCost - ourCost.total,
      savingsPercentage: ((averageCompetitorCost - ourCost.total) / averageCompetitorCost) * 100
    };
  }
}
```

## Usage Analytics and Optimization

### **Smart Usage Patterns**

```typescript
interface UsageAnalyzer {
  analyzeUserPatterns(userId: string): Promise<UsagePattern>;
  recommendOptimizations(userId: string): Promise<OptimizationSuggestion[]>;
  predictMonthlyUsage(userId: string): Promise<UsagePrediction>;
}

interface UsagePattern {
  mostCommonOperations: OperationFrequency[];
  peakUsageTimes: TimeSlot[];
  averageOperationCost: number;
  clientSideSuccessRate: number;
  serverSideUsageReasons: string[];
}

interface OptimizationSuggestion {
  type: 'cost_saving' | 'performance' | 'workflow';
  title: string;
  description: string;
  potentialSavings?: number;
  implementationSteps: string[];
}

// AI-powered usage optimization
class SmartUsageAnalyzer implements UsageAnalyzer {
  async analyzeUserPatterns(userId: string): Promise<UsagePattern> {
    const history = await this.getUsageHistory(userId, { last: 30, unit: 'days' });
    
    return {
      mostCommonOperations: this.analyzeOperationFrequency(history),
      peakUsageTimes: this.identifyPeakTimes(history),
      averageOperationCost: this.calculateAverageOperationCost(history),
      clientSideSuccessRate: this.calculateClientSideSuccessRate(history),
      serverSideUsageReasons: this.analyzeServerSideReasons(history)
    };
  }

  async recommendOptimizations(userId: string): Promise<OptimizationSuggestion[]> {
    const pattern = await this.analyzeUserPatterns(userId);
    const suggestions: OptimizationSuggestion[] = [];
    
    // Cost optimization suggestions
    if (pattern.clientSideSuccessRate < 0.8) {
      suggestions.push({
        type: 'cost_saving',
        title: 'Optimize for Client-Side Processing',
        description: 'Many of your operations could be processed locally for free',
        potentialSavings: this.calculateClientSideProcessingSavings(pattern),
        implementationSteps: [
          'Enable "Try Client-Side First" in settings',
          'Consider splitting large documents before processing',
          'Use browser-based tools for simple operations'
        ]
      });
    }
    
    // Performance optimization suggestions
    if (pattern.averageOperationCost > 0.10) {
      suggestions.push({
        type: 'performance',
        title: 'Batch Similar Operations',
        description: 'Process multiple documents together to reduce per-operation costs',
        potentialSavings: this.calculateBatchProcessingSavings(pattern),
        implementationSteps: [
          'Collect similar documents before processing',
          'Use batch processing features when available',
          'Schedule non-urgent operations for off-peak times'
        ]
      });
    }
    
    return suggestions;
  }
}
```

## Auto-Recharge and Budget Management

### **Smart Budget Controls**

```typescript
interface BudgetManager {
  setBudgetLimits(userId: string, limits: BudgetLimits): Promise<boolean>;
  checkBudgetStatus(userId: string): Promise<BudgetStatus>;
  handleAutoRecharge(userId: string): Promise<RechargeResult>;
  sendBudgetAlerts(userId: string, alertType: AlertType): Promise<void>;
}

interface BudgetLimits {
  dailyLimit?: number;
  weeklyLimit?: number;
  monthlyLimit?: number;
  autoRecharge: {
    enabled: boolean;
    threshold: number;
    amount: number;
    maxAutoRecharges?: number;
  };
}

interface BudgetStatus {
  currentPeriodSpending: {
    daily: number;
    weekly: number;
    monthly: number;
  };
  remainingBudget: {
    daily?: number;
    weekly?: number;
    monthly?: number;
  };
  autoRechargeStatus: {
    nextRechargeAt?: Date;
    remainingAutoRecharges?: number;
  };
  alerts: BudgetAlert[];
}

// Intelligent budget management
class SmartBudgetManager implements BudgetManager {
  async handleAutoRecharge(userId: string): Promise<RechargeResult> {
    const balance = await this.creditTracker.checkBalance(userId);
    const limits = await this.getBudgetLimits(userId);
    
    if (!limits.autoRecharge.enabled) {
      return { success: false, reason: 'Auto-recharge disabled' };
    }
    
    if (balance <= limits.autoRecharge.threshold) {
      // Check if we haven't exceeded auto-recharge limits
      const rechargeCount = await this.getMonthlyRechargeCount(userId);
      if (limits.autoRecharge.maxAutoRecharges && 
          rechargeCount >= limits.autoRecharge.maxAutoRecharges) {
        await this.sendBudgetAlerts(userId, AlertType.AUTO_RECHARGE_LIMIT_REACHED);
        return { success: false, reason: 'Monthly auto-recharge limit reached' };
      }
      
      // Process auto-recharge
      const result = await this.processRecharge(userId, limits.autoRecharge.amount);
      
      if (result.success) {
        await this.logTransaction(userId, {
          type: TransactionType.AUTO_RECHARGE,
          amount: limits.autoRecharge.amount,
          description: 'Automatic credit recharge'
        });
      }
      
      return result;
    }
    
    return { success: false, reason: 'Balance above threshold' };
  }
}
```

## Integration with Processing Pipeline

### **Seamless Credit Flow**

```typescript
// Credit system integration with processing pipeline
class ProcessingCreditIntegration {
  async processWithCredits(
    userId: string,
    operation: ProcessingOperation,
    document: PDFDocument
  ): Promise<ProcessingResult> {
    try {
      // 1. Calculate estimated cost
      const costEstimate = await this.costCalculator.calculateOperationCost(operation, document.metadata);
      
      // 2. Reserve credits
      const reservation = await this.creditTracker.reserveCredits(userId, costEstimate.total);
      if (!reservation.reserved) {
        throw new InsufficientCreditsError(costEstimate.total, reservation.currentBalance);
      }
      
      // 3. Process document
      const startTime = Date.now();
      const result = await this.processingEngine.process(operation, document);
      const actualTime = Date.now() - startTime;
      
      // 4. Calculate actual cost based on actual processing time
      const actualCost = this.calculateActualCost(costEstimate, actualTime);
      
      // 5. Consume actual credits
      await this.creditTracker.consumeCredits(reservation.reservationId, actualCost);
      
      // 6. Log transaction
      await this.logProcessingTransaction(userId, operation, actualCost, result);
      
      // 7. Update user analytics
      await this.usageAnalyzer.recordProcessing(userId, operation, actualCost, actualTime);
      
      return result;
      
    } catch (error) {
      // Release reservation on error
      if (reservation?.reservationId) {
        await this.creditTracker.releaseReservation(reservation.reservationId);
      }
      throw error;
    }
  }
}
```

## Monitoring and Reporting

### **Real-Time Dashboard**

```typescript
interface CreditDashboard {
  getCurrentBalance(): Promise<number>;
  getUsageMetrics(timeframe: TimeFrame): Promise<UsageMetrics>;
  getCostBreakdown(timeframe: TimeFrame): Promise<CostBreakdown>;
  getOptimizationRecommendations(): Promise<OptimizationSuggestion[]>;
  exportUsageReport(timeframe: TimeFrame): Promise<UsageReport>;
}

interface UsageMetrics {
  totalCreditsUsed: number;
  operationCounts: Record<string, number>;
  averageCostPerOperation: number;
  clientVsServerProcessing: {
    clientSide: number;
    serverSide: number;
    savingsFromClientSide: number;
  };
  costTrends: CostTrend[];
}

// Real-time dashboard implementation
class RealTimeCreditDashboard implements CreditDashboard {
  async getCurrentBalance(): Promise<number> {
    return this.creditTracker.checkBalance(this.userId);
  }

  async getUsageMetrics(timeframe: TimeFrame): Promise<UsageMetrics> {
    const transactions = await this.getTransactions(timeframe);
    
    return {
      totalCreditsUsed: this.sumConsumption(transactions),
      operationCounts: this.countOperations(transactions),
      averageCostPerOperation: this.calculateAverageCost(transactions),
      clientVsServerProcessing: this.analyzeProcessingMix(transactions),
      costTrends: this.calculateCostTrends(transactions)
    };
  }
}
```

This credit system architecture provides complete transparency, user control, and cost optimization while seamlessly integrating with the hybrid processing pipeline.
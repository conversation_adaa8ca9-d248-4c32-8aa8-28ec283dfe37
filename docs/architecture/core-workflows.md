# Core Workflows

```mermaid
sequenceDiagram
    participant U as User
    participant P<PERSON> as PWA Frontend
    participant SW as Service Worker
    participant WASM as PDF Engine
    participant API as tRPC API
    participant Auth as Supabase Auth
    participant Cloud as Cloud Storage

    Note over U, Cloud: PDF Processing with Cloud Save Workflow

    U->>PWA: Upload PDF file
    PWA->>SW: Check offline status
    SW-->>PWA: Online/Offline status
    
    PWA->>WASM: Process PDF (compress/sign)
    WASM-->>PWA: Processed PDF data
    
    PWA->>API: Record processing history
    API->>Auth: Validate JWT token
    Auth-->>API: Token valid
    API-->>PWA: History recorded
    
    U->>PWA: Save to Google Drive
    PWA->>API: Get cloud storage token
    API->>Auth: Validate user
    API-->>PWA: Encrypted access token
    
    PWA->>Cloud: Upload processed PDF
    Cloud-->>PWA: File URL and metadata
    
    PWA->>U: Share link and completion
    
    Note over U, Cloud: Error Handling Flow
    
    WASM--xPWA: Processing error
    PWA->>SW: Cache for retry
    SW-->>PWA: Queued for later
    PWA->>U: Show error + retry option
```

# Data Models

## User

**Purpose:** Represents registered users with authentication and profile information

**Key Attributes:**
- id: string (UUID) - Primary identifier from Supabase Auth
- email: string - Authentication email, unique constraint
- name: string - Display name for UI personalization
- avatar_url: string | null - Profile image URL
- subscription_tier: 'free' | 'premium' - Current subscription level
- created_at: timestamp - Account creation date
- updated_at: timestamp - Last profile modification

### TypeScript Interface
```typescript
interface User {
  id: string;
  email: string;
  name: string;
  avatar_url: string | null;
  subscription_tier: 'free' | 'premium';
  created_at: string;
  updated_at: string;
}
```

### Relationships
- One-to-many with ProcessingHistory
- One-to-one with Subscription
- One-to-many with CloudStorageConnection

## ProcessingHistory

**Purpose:** Tracks PDF processing operations for user history and analytics

**Key Attributes:**
- id: string (UUID) - Unique processing session identifier
- user_id: string - Foreign key to User
- original_filename: string - User-provided filename for reference
- operation_type: 'compress' | 'sign' | 'batch' | 'watermark' - Processing operation
- settings: JSON - Processing parameters used (target size, quality, etc.)
- processing_time_ms: number - Performance tracking
- file_size_before: number - Original file size in bytes
- file_size_after: number - Processed file size in bytes
- created_at: timestamp - Processing completion time

### TypeScript Interface
```typescript
interface ProcessingHistory {
  id: string;
  user_id: string;
  original_filename: string;
  operation_type: 'compress' | 'sign' | 'batch' | 'watermark';
  settings: {
    target_size?: number;
    quality_level?: number;
    signature_position?: { x: number; y: number };
    watermark_text?: string;
    batch_count?: number;
  };
  processing_time_ms: number;
  file_size_before: number;
  file_size_after: number;
  created_at: string;
}
```

### Relationships
- Many-to-one with User
- No file storage relations (privacy-first design)

## Subscription

**Purpose:** Manages user subscription status and billing information

**Key Attributes:**
- id: string (UUID) - Subscription identifier
- user_id: string - Foreign key to User, unique constraint
- stripe_customer_id: string - Stripe customer reference
- stripe_subscription_id: string | null - Active subscription ID
- status: 'active' | 'canceled' | 'past_due' | 'trial' - Current state
- current_period_start: timestamp - Billing period start
- current_period_end: timestamp - Billing period end
- cancel_at_period_end: boolean - Cancellation flag
- created_at: timestamp - Subscription creation
- updated_at: timestamp - Last status change

### TypeScript Interface
```typescript
interface Subscription {
  id: string;
  user_id: string;
  stripe_customer_id: string;
  stripe_subscription_id: string | null;
  status: 'active' | 'canceled' | 'past_due' | 'trial';
  current_period_start: string;
  current_period_end: string;
  cancel_at_period_end: boolean;
  created_at: string;
  updated_at: string;
}
```

### Relationships
- One-to-one with User
- External relation to Stripe Customer/Subscription objects

## CloudStorageConnection

**Purpose:** Stores OAuth tokens for cloud storage integrations (encrypted)

**Key Attributes:**
- id: string (UUID) - Connection identifier
- user_id: string - Foreign key to User
- provider: 'google_drive' | 'dropbox' | 'onedrive' - Storage service
- provider_user_id: string - External user identifier
- access_token: string - Encrypted OAuth access token
- refresh_token: string | null - Encrypted OAuth refresh token
- token_expires_at: timestamp | null - Token expiration
- created_at: timestamp - Connection established
- last_used_at: timestamp | null - Last successful API call

### TypeScript Interface
```typescript
interface CloudStorageConnection {
  id: string;
  user_id: string;
  provider: 'google_drive' | 'dropbox' | 'onedrive';
  provider_user_id: string;
  access_token: string; // Encrypted in database
  refresh_token: string | null; // Encrypted in database
  token_expires_at: string | null;
  created_at: string;
  last_used_at: string | null;
}
```

### Relationships
- Many-to-one with User
- External relations to cloud storage APIs

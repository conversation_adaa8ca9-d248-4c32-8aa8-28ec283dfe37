# Backend Architecture

## Service Architecture

### Function Organization
```
apps/api/src/
├── routers/             # tRPC router definitions
│   ├── auth.ts         # Authentication endpoints
│   ├── user.ts         # User management
│   ├── history.ts      # Processing history
│   ├── subscription.ts # Billing management
│   └── cloudStorage.ts # Cloud integrations
├── middleware/         # Request middleware
│   ├── auth.ts        # JWT verification
│   ├── rateLimit.ts   # Rate limiting
│   └── validation.ts  # Request validation
├── services/          # Business logic
│   ├── auth.service.ts
│   ├── subscription.service.ts
│   └── cloudStorage.service.ts
├── utils/             # Backend utilities
│   ├── encryption.ts  # Token encryption
│   ├── webhook.ts     # Stripe webhook handling
│   └── email.ts       # Email notifications
└── trpc.ts           # tRPC configuration
```

### Function Template
```typescript
import { z } from 'zod';
import { createTRPCRouter, protectedProcedure } from '~/server/api/trpc';
import { TRPCError } from '@trpc/server';

export const exampleRouter = createTRPCRouter({
  getExample: protectedProcedure
    .input(z.object({
      id: z.string().uuid(),
    }))
    .output(z.object({
      id: z.string(),
      data: z.any(),
    }))
    .query(async ({ input, ctx }) => {
      try {
        const result = await ctx.db
          .from('examples')
          .select('*')
          .eq('id', input.id)
          .eq('user_id', ctx.user.id)
          .single();

        if (!result.data) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Example not found',
          });
        }

        return {
          id: result.data.id,
          data: result.data,
        };
      } catch (error) {
        if (error instanceof TRPCError) throw error;
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to fetch example',
          cause: error,
        });
      }
    }),
});
```

## Database Architecture

### Data Access Layer
```typescript
import { createClient } from '@supabase/supabase-js';
import type { Database } from '~/types/database';

export class DatabaseService {
  private client = createClient<Database>(
    process.env.SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!
  );

  async getUserWithSubscription(userId: string) {
    const { data, error } = await this.client
      .from('users')
      .select(`
        *,
        subscription:subscriptions(*)
      `)
      .eq('id', userId)
      .single();

    if (error) throw error;
    return data;
  }

  async createProcessingHistory(
    userId: string,
    history: Omit<ProcessingHistory, 'id' | 'user_id' | 'created_at'>
  ) {
    const { data, error } = await this.client
      .from('processing_history')
      .insert({
        user_id: userId,
        ...history,
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  }
}
```

## Authentication and Authorization

### Auth Flow
```mermaid
sequenceDiagram
    participant Client as PWA Client
    participant API as Edge Function
    participant Auth as Supabase Auth
    participant DB as Database

    Client->>API: Login request with credentials
    API->>Auth: Verify credentials
    Auth-->>API: User data + JWT
    API->>DB: Update user last_login
    API-->>Client: JWT + user profile

    Note over Client, DB: Subsequent API calls

    Client->>API: API request with JWT
    API->>Auth: Verify JWT signature
    Auth-->>API: Decoded user claims
    API->>DB: Execute authorized query
    DB-->>API: Query results
    API-->>Client: Response data
```

### Middleware/Guards
```typescript
import { TRPCError } from '@trpc/server';
import { createMiddleware } from '~/server/api/trpc';

export const authMiddleware = createMiddleware(async ({ ctx, next }) => {
  const token = ctx.req.headers.authorization?.replace('Bearer ', '');
  
  if (!token) {
    throw new TRPCError({
      code: 'UNAUTHORIZED',
      message: 'Authentication required',
    });
  }

  try {
    const { data: user, error } = await ctx.supabase.auth.getUser(token);
    
    if (error || !user) {
      throw new TRPCError({
        code: 'UNAUTHORIZED',
        message: 'Invalid token',
      });
    }

    return next({
      ctx: {
        ...ctx,
        user: user.user,
      },
    });
  } catch (error) {
    throw new TRPCError({
      code: 'UNAUTHORIZED',
      message: 'Token verification failed',
    });
  }
});
```

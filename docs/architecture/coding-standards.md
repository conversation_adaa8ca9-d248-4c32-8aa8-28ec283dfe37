# Coding Standards

## Critical Fullstack Rules

- **Type Sharing:** Always define types in packages/shared and import from there
- **API Calls:** Never make direct HTTP calls - use the tRPC client service layer
- **Environment Variables:** Access only through config objects, never process.env directly in frontend
- **Error Handling:** All API routes must use the standard tRPC error format
- **State Updates:** Never mutate state directly - use proper Zustand patterns
- **File Processing:** PDF operations must run in Web Workers to prevent UI blocking
- **Authentication:** Always verify JWT tokens server-side, never trust client-side auth state
- **Cloud Storage:** Encrypt all OAuth tokens before database storage
- **Performance:** Lazy load components and routes to maintain mobile performance

## Naming Conventions

| Element | Frontend | Backend | Example |
|---------|----------|---------|---------|
| Components | PascalCase | - | `UserProfile.tsx` |
| Hooks | camelCase with 'use' | - | `useAuth.ts` |
| API Routes | - | kebab-case | `/api/user-profile` |
| Database Tables | - | snake_case | `user_profiles` |
| tRPC Procedures | camelCase | camelCase | `getProcessingHistory` |
| Environment Variables | UPPER_SNAKE_CASE | UPPER_SNAKE_CASE | `SUPABASE_URL` |
| TypeScript Interfaces | PascalCase | PascalCase | `ProcessingOptions` |
| Zustand Stores | camelCase with Store | - | `useAuthStore` |

# High Level Architecture

## Technical Summary

MobilePDF Pro employs a **hybrid privacy-first architecture** combining client-side WebAssembly processing with dedicated server-side compute instances for advanced operations. The default mode processes 80% of use cases entirely in-browser using production-grade MuPDF WASM for complete privacy, while complex operations (OCR with table recognition, professional compression, ML document analysis) utilize dedicated compute infrastructure with transparent credit-based billing. The frontend leverages modern React with TypeScript and advanced PDF editing capabilities including base-level text layout modification, while the backend provides both user management and high-performance PDF processing services. Critical integration points include progressive WASM loading, intelligent processing decision routing, credit consumption tracking, and real-time performance benchmarking against industry standards. This hybrid approach achieves superior mobile performance, maintains privacy by default, and enables professional-grade document editing without compromise.

## Platform and Infrastructure Choice

**Frontend Platform:** Vercel Edge Network (Global CDN)
**User Management:** Supabase Auth + PostgreSQL (US-East, EU, Asia replicas)
**Client Processing:** Browser WebAssembly + Service Workers
**Server Processing:** Dedicated compute instances (AWS EC2/DigitalOcean)
**Credit System:** Independent tracking service with Redis caching
**Payment Processing:** Stripe (subscriptions) + separate credit billing
**Performance Monitoring:** Custom benchmarking service + Real User Monitoring
**Storage:** Browser-native + Optional cloud sync (privacy-preserving)

## Repository Structure

**Structure:** Monorepo with workspace-based package management
**Monorepo Tool:** npm workspaces (native, simple, sufficient for project scope)
**Package Organization:** 
- `apps/web` - PWA frontend application with hybrid processing orchestration
- `apps/api` - User management and credit system APIs
- `apps/processing-server` - Dedicated compute instances for advanced PDF operations
- `packages/shared` - Shared TypeScript types and utilities
- `packages/pdf-engine` - Production MuPDF WASM integration with fallback strategies
- `packages/ui` - Mobile-first component library with professional editing tools
- `packages/credit-system` - Transparent cost tracking and billing logic
- `packages/performance-monitor` - Benchmarking and optimization framework

## High Level Architecture Diagram

```mermaid
graph TB
    User[Mobile/Desktop User] --> PWA[PWA Frontend<br/>Hybrid Processing Orchestrator]
    
    subgraph "Client-Side Processing (Default - 80%)"
        PWA --> SW[Service Worker<br/>Offline + Caching]
        PWA --> WASM[MuPDF WASM Engine<br/>Production PDF Processing]
        PWA --> Router[Processing Router<br/>Client vs Server Decision]
    end
    
    subgraph "Server-Side Processing (Premium - 20%)"
        Router -->|Complex Operations| Queue[Processing Queue<br/>Redis + Bull]
        Queue --> Compute[Dedicated Compute<br/>AWS EC2 Fleet]
        Compute --> OCR[Advanced OCR<br/>Tesseract + Table Recognition]
        Compute --> Compress[Professional Compression<br/>Ghostscript + QPDF]
        Compute --> ML[ML Document Analysis<br/>Custom Models]
    end
    
    subgraph "User & Credit Management"
        PWA --> API[User Management API<br/>Vercel Edge Functions]
        API --> Auth[Supabase Auth<br/>JWT + Social Login]
        API --> UserDB[(User Database<br/>Supabase PostgreSQL)]
        API --> Credits[Credit System<br/>Independent Tracking]
        Credits --> Redis[(Redis Cache<br/>Real-time Credit Balance)]
    end
    
    subgraph "Billing & Payments"
        API --> Stripe[Stripe API<br/>Subscription Management]
        Credits --> Billing[Credit Billing<br/>Transparent Cost Calculation]
    end
    
    subgraph "Performance & Monitoring"
        PWA --> Monitor[Performance Monitor<br/>Real-time Benchmarking]
        Compute --> Monitor
        Monitor --> Metrics[(Metrics Database<br/>Performance Analytics)]
    end
    
    subgraph "External Integrations"
        PWA --> CloudStorage[Cloud Storage APIs<br/>Google Drive, Dropbox, OneDrive]
        PWA --> NotionAPI[Notion API<br/>Document Export]
        PWA --> GoogleDocs[Google Docs API<br/>Seamless Integration]
    end
    
    SW --> Cache[Browser Cache<br/>Progressive Assets]
    WASM --> Memory[Optimized Memory<br/>4GB+ File Handling]
    
    CDN[Global CDN<br/>Vercel Edge] --> PWA
    
    style PWA fill:#e1f5fe
    style WASM fill:#f3e5f5
    style Compute fill:#fff3e0
    style Credits fill:#e8f5e8
    style Router fill:#f9f9f9
```

## Architectural Patterns

- **Hybrid Privacy-First Pattern:** Client-side default with transparent server-side options - _Rationale:_ Maintains privacy while enabling professional-grade processing when needed
- **Progressive Processing Pattern:** Intelligent routing between client WASM and dedicated compute - _Rationale:_ Optimizes cost, performance, and privacy based on document complexity and user choice
- **Production-Grade WASM Integration:** MuPDF with comprehensive fallback strategies - _Rationale:_ Industry-standard PDF processing with reliability guarantees for professional workflows
- **Transparent Credit System:** Separate billing for compute-intensive operations - _Rationale:_ User control over costs with clear value proposition for premium processing
- **Performance-First Mobile Architecture:** Touch-optimized with sub-200ms response targets - _Rationale:_ Superior mobile experience through native-like performance and gesture optimization
- **Advanced PDF Editing Pattern:** Base-level text layout modification and professional tools - _Rationale:_ Competitive differentiation through comprehensive editing capabilities
- **Offline-Capable Hybrid:** Service worker caching with background compute sync - _Rationale:_ Mobile reliability with seamless premium feature access
- **Real-Time Performance Monitoring:** Continuous benchmarking against industry standards - _Rationale:_ Measurable quality assurance and competitive advantage validation
- **Component-Based Professional UI:** Mobile-first components with desktop enhancement - _Rationale:_ Consistent professional experience across all device types
- **Graceful Degradation Pattern:** Full functionality progression from basic to professional - _Rationale:_ Accessibility and performance optimization for varied device capabilities

# Testing Strategy

## Testing Pyramid
```
        E2E Tests
       /        \
   Integration Tests
  /            \
Frontend Unit  Backend Unit
```

## Test Organization

### Frontend Tests
```
apps/web/tests/
├── components/          # Component unit tests
├── hooks/              # Custom hook tests
├── services/           # API client tests
├── utils/              # Utility function tests
├── integration/        # Page-level integration tests
└── e2e/               # End-to-end user flows
```

### Backend Tests
```
apps/api/tests/
├── routers/            # tRPC router tests
├── services/           # Business logic tests
├── middleware/         # Middleware tests
├── utils/              # Utility function tests
└── integration/        # API integration tests
```

### E2E Tests
```
tests/e2e/
├── auth/               # Authentication flows
├── processing/         # PDF processing workflows
├── subscription/       # Billing and subscription
├── integrations/       # Cloud storage integrations
└── mobile/            # Mobile-specific tests
```

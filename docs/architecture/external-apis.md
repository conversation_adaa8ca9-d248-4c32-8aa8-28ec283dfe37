# External APIs

## Google Drive API

- **Purpose:** Cloud storage integration for processed PDF saving and sharing
- **Documentation:** https://developers.google.com/drive/api/v3/reference
- **Base URL(s):** https://www.googleapis.com/drive/v3, https://www.googleapis.com/upload/drive/v3
- **Authentication:** OAuth 2.0 with scope: https://www.googleapis.com/auth/drive.file
- **Rate Limits:** 1,000 requests per 100 seconds per user

**Key Endpoints Used:**
- `POST /files` - Upload processed PDFs to user's Drive
- `GET /files/{fileId}` - Retrieve file metadata for sharing
- `POST /files/{fileId}/permissions` - Share files with specific permissions

**Integration Notes:** Files uploaded with MobilePDF Pro ownership, shared folders for team accounts

## Dropbox API

- **Purpose:** Alternative cloud storage for users preferring Dropbox ecosystem
- **Documentation:** https://www.dropbox.com/developers/documentation/http/documentation
- **Base URL(s):** https://api.dropboxapi.com/2, https://content.dropboxapi.com/2
- **Authentication:** OAuth 2.0 with scope: files.content.write, sharing.write
- **Rate Limits:** 600 requests per minute

**Key Endpoints Used:**
- `POST /files/upload` - Upload processed PDFs to Dropbox
- `POST /sharing/create_shared_link_with_settings` - Generate sharing links
- `POST /files/list_folder` - Browse user's folder structure

**Integration Notes:** Content upload API used for files >150MB, chunked uploads for large PDFs

## OneDrive Graph API

- **Purpose:** Microsoft 365 integration for enterprise users
- **Documentation:** https://docs.microsoft.com/en-us/graph/api/resources/onedrive
- **Base URL(s):** https://graph.microsoft.com/v1.0
- **Authentication:** OAuth 2.0 with scope: Files.ReadWrite, Sites.ReadWrite.All
- **Rate Limits:** 10,000 requests per 10 minutes per application

**Key Endpoints Used:**
- `PUT /me/drive/items/{parent-id}:/{filename}:/content` - Upload files
- `POST /me/drive/items/{item-id}/createLink` - Create sharing links
- `GET /me/drive/root/children` - List folders for organization

**Integration Notes:** Supports both personal OneDrive and SharePoint integration for teams

## Notion API

- **Purpose:** Export Notion pages as professionally formatted PDFs with branding
- **Documentation:** https://developers.notion.com/reference/intro
- **Base URL(s):** https://api.notion.com/v1
- **Authentication:** OAuth 2.0 with scope: read, Internal integration tokens for workspace
- **Rate Limits:** 3 requests per second per integration

**Key Endpoints Used:**
- `GET /pages/{page_id}` - Retrieve page content and properties
- `GET /blocks/{block_id}/children` - Get page blocks for content rendering
- `POST /search` - Find pages based on user query

**Integration Notes:** Custom PDF renderer required for Notion block types, template application during export

## Stripe API

- **Purpose:** Subscription billing and payment processing
- **Documentation:** https://stripe.com/docs/api
- **Base URL(s):** https://api.stripe.com/v1
- **Authentication:** Bearer token with restricted keys for client, secret keys for server
- **Rate Limits:** 100 requests per second in live mode

**Key Endpoints Used:**
- `POST /checkout/sessions` - Create subscription checkout sessions
- `GET /subscriptions/{subscription_id}` - Retrieve subscription status
- `POST /subscriptions/{subscription_id}` - Modify or cancel subscriptions
- `POST /webhook_endpoints` - Handle subscription lifecycle events

**Integration Notes:** Webhook signature verification required, customer portal integration for self-service

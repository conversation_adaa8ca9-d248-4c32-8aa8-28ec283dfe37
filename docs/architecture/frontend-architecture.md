# Frontend Architecture

## Component Architecture

### Component Organization
```
src/
├── components/           # Reusable UI components
│   ├── ui/              # Base design system components
│   ├── forms/           # Form-specific components
│   ├── layout/          # Layout and navigation
│   └── domain/          # Business logic components
├── pages/               # Route-level page components
├── hooks/               # Custom React hooks
├── stores/              # Zustand state stores
├── services/            # API and external service clients
├── utils/               # Frontend utilities
└── types/               # Frontend-specific types
```

### Component Template
```typescript
import { forwardRef } from 'react';
import { cn } from '~/utils/cn';

interface ComponentProps {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export const Component = forwardRef<HTMLDivElement, ComponentProps>(
  ({ children, variant = 'primary', size = 'md', className, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          'base-styles',
          {
            'variant-primary': variant === 'primary',
            'variant-secondary': variant === 'secondary',
            'size-sm': size === 'sm',
            'size-md': size === 'md',
            'size-lg': size === 'lg',
          },
          className
        )}
        {...props}
      >
        {children}
      </div>
    );
  }
);

Component.displayName = 'Component';
```

## State Management Architecture

### State Structure
```typescript
interface AppState {
  // Auth state
  auth: {
    user: User | null;
    isLoading: boolean;
    session: Session | null;
  };
  
  // PDF processing state
  processing: {
    currentFile: File | null;
    progress: number;
    result: ProcessedResult | null;
    history: ProcessingHistoryItem[];
    isProcessing: boolean;
  };
  
  // Subscription state
  subscription: {
    current: Subscription | null;
    isLoading: boolean;
    usage: UsageMetrics;
  };
  
  // Cloud storage state
  cloudStorage: {
    connections: CloudConnection[];
    activeProvider: CloudProvider | null;
    uploadProgress: number;
  };
  
  // UI state
  ui: {
    sidebarOpen: boolean;
    theme: 'light' | 'dark' | 'system';
    notifications: Notification[];
  };
}
```

### State Management Patterns
- **Slice-based stores:** Separate Zustand stores for each domain (auth, processing, subscription)
- **Optimistic updates:** UI updates immediately, syncs with server asynchronously
- **Persistence:** Critical state (auth, theme) persisted to localStorage with encryption
- **Computed values:** Derived state using Zustand computed patterns
- **DevTools integration:** Redux DevTools support for debugging state changes

## Routing Architecture

### Route Organization
```
/                        # Landing page (public)
/app                     # Main application (protected)
├── /dashboard           # Processing dashboard
├── /history             # Processing history
├── /settings            # User settings
│   ├── /profile         # Profile management
│   ├── /subscription    # Billing management
│   └── /integrations    # Cloud storage connections
├── /process             # PDF processing interface
│   ├── /compress        # Compression workflow
│   ├── /sign            # Signature workflow
│   └── /batch           # Batch processing
└── /share/:id           # Public file sharing (public)
```

### Protected Route Pattern
```typescript
import { Navigate, useLocation } from 'react-router-dom';
import { useAuthStore } from '~/stores/auth';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredTier?: 'free' | 'premium';
}

export function ProtectedRoute({ children, requiredTier }: ProtectedRouteProps) {
  const { user, isLoading } = useAuthStore();
  const location = useLocation();

  if (isLoading) {
    return <LoadingSpinner />;
  }

  if (!user) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  if (requiredTier === 'premium' && user.subscription_tier !== 'premium') {
    return <Navigate to="/upgrade" replace />;
  }

  return <>{children}</>;
}
```

## Frontend Services Layer

### API Client Setup
```typescript
import { createTRPCReact } from '@trpc/react-query';
import { httpBatchLink } from '@trpc/client';
import type { AppRouter } from '~/server/api/root';

export const api = createTRPCReact<AppRouter>();

export function TRPCProvider({ children }: { children: React.ReactNode }) {
  const [queryClient] = useState(() => new QueryClient({
    defaultOptions: {
      queries: {
        staleTime: 5 * 60 * 1000, // 5 minutes
        retry: (failureCount, error) => {
          if (error.data?.code === 'UNAUTHORIZED') return false;
          return failureCount < 3;
        },
      },
    },
  }));

  const [trpcClient] = useState(() =>
    api.createClient({
      links: [
        httpBatchLink({
          url: '/api/trpc',
          headers() {
            const token = useAuthStore.getState().session?.access_token;
            return token ? { authorization: `Bearer ${token}` } : {};
          },
        }),
      ],
    })
  );

  return (
    <api.Provider client={trpcClient} queryClient={queryClient}>
      <QueryClientProvider client={queryClient}>
        {children}
      </QueryClientProvider>
    </api.Provider>
  );
}
```

### Service Example
```typescript
// Cloud storage service
export class CloudStorageService {
  private connections = new Map<CloudProvider, CloudConnection>();

  async uploadFile(
    file: Uint8Array, 
    filename: string, 
    provider: CloudProvider
  ): Promise<UploadResult> {
    const connection = this.connections.get(provider);
    if (!connection) {
      throw new Error(`No connection for provider: ${provider}`);
    }

    try {
      switch (provider) {
        case 'google_drive':
          return await this.uploadToGoogleDrive(file, filename, connection);
        case 'dropbox':
          return await this.uploadToDropbox(file, filename, connection);
        case 'onedrive':
          return await this.uploadToOneDrive(file, filename, connection);
        default:
          throw new Error(`Unsupported provider: ${provider}`);
      }
    } catch (error) {
      // Refresh token if needed
      if (this.isTokenExpired(error)) {
        await this.refreshConnection(provider);
        return this.uploadFile(file, filename, provider);
      }
      throw error;
    }
  }
}
```

# Story 1.2.2: Sidebar Panel Navigation System

## Status
**Ready for Development**

## Story
**As a** business user,  
**I want** a comprehensive sidebar panel system with thumbnails, outlines, search, and comments,  
**so that** I can efficiently navigate, search, and manage large PDF documents like professional desktop applications.

## Reference Analysis
Based on comprehensive analysis of PDF.js Express demo interface. See:
- `docs/ux-analysis-pdfjs-express-demo.md` - Complete sidebar panel analysis with screenshots
- `docs/current-architecture-analysis.md` - Implementation gap analysis for navigation features

## Acceptance Criteria

### AC1: Thumbnails Panel
- **Given** a PDF document is loaded
- **When** the user opens the thumbnails panel
- **Then** page thumbnail previews display in a scrollable list
- **And** the current page thumbnail is highlighted with a visual indicator
- **And** clicking any thumbnail navigates to that page instantly
- **And** thumbnails load progressively as the user scrolls through the list
- **And** page numbers are clearly visible on each thumbnail

**Reference Screenshots:**
- `.playwright-mcp/pdfjs-sidebar-thumbnails-panel.png`

### AC2: Document Outlines Panel
- **Given** a PDF document with bookmarks/outlines is loaded
- **When** the user opens the outlines panel  
- **Then** a hierarchical tree structure displays document sections
- **And** expandable/collapsible sections show nested bookmark levels
- **And** clicking any outline item navigates to the corresponding page location
- **And** current location is highlighted in the outline structure
- **And** empty state message displays for documents without outlines

**Reference Screenshots:**
- `.playwright-mcp/pdfjs-sidebar-outlines-panel.png`

### AC3: Search Panel with Highlighting
- **Given** a PDF document is loaded
- **When** the user opens the search panel and enters a search query
- **Then** full-text search results display with match count (e.g., "5 matches")
- **And** search results highlight in the document with yellow background
- **And** previous/next navigation buttons move between search results
- **And** current result index displays (e.g., "2 of 5")
- **And** case-sensitive search option is available
- **And** search clearing removes all highlighting

**Reference Screenshots:**
- `.playwright-mcp/pdfjs-search-functionality.png`

### AC4: Comments Panel
- **Given** a PDF document with annotations is loaded
- **When** the user opens the comments panel
- **Then** all annotations and comments display in chronological order
- **And** each comment shows author, timestamp, and annotation type
- **And** clicking a comment navigates to its location in the document
- **And** comment threading and replies are supported
- **And** empty state message displays for documents without comments

**Reference Screenshots:**
- `.playwright-mcp/pdfjs-comments-panel.png`

### AC5: Panel System Architecture
- **Given** the sidebar is open
- **When** the user interacts with the panel system
- **Then** only one panel is active at a time
- **And** panel tabs display at the top with clear icons and labels
- **And** panels collapse/expand smoothly with animation transitions
- **And** panel state persists during document navigation
- **And** sidebar can be completely collapsed to maximize document viewing area

### AC6: Mobile Responsive Panels
- **Given** the application loads on mobile devices
- **When** the user accesses sidebar panels
- **Then** panels adapt to mobile layout with bottom sheet or overlay pattern
- **And** touch-optimized controls for panel navigation
- **And** swipe gestures work for panel switching
- **And** proper spacing for thumb-friendly interactions

**Reference Screenshots:**
- `.playwright-mcp/pdfjs-mobile-viewport-overview.png`

## Tasks / Subtasks

### Phase 1: Panel Architecture (Week 1-2)
- [ ] **Create Sidebar Panel System** (AC5, AC6)
  - [ ] Build SidebarPanelContainer component with tab navigation
  - [ ] Implement panel switching state management
  - [ ] Add smooth animation transitions between panels
  - [ ] Create responsive mobile bottom-sheet panel layout
  - [ ] Ensure proper accessibility with keyboard navigation

- [ ] **Build Thumbnails Panel** (AC1)
  - [ ] Create ThumbnailsPanel component with virtual scrolling
  - [ ] Implement thumbnail generation from PDF pages
  - [ ] Add current page highlighting with visual indicators
  - [ ] Build click-to-navigate functionality
  - [ ] Optimize thumbnail loading for performance

### Phase 2: Search and Navigation (Week 2-3)
- [ ] **Implement Search Panel** (AC3)
  - [ ] Create SearchPanel component with input and results display
  - [ ] Build full-text search functionality with PDF text extraction
  - [ ] Implement search result highlighting with yellow background
  - [ ] Add previous/next navigation through search results
  - [ ] Create case-sensitive search option toggle
  - [ ] Build search result count and index display

- [ ] **Build Outlines Panel** (AC2)
  - [ ] Create OutlinesPanel component with tree structure
  - [ ] Extract bookmark/outline data from PDF documents
  - [ ] Implement expandable/collapsible hierarchical navigation
  - [ ] Add current location highlighting in outline tree
  - [ ] Create empty state for documents without outlines

### Phase 3: Comments and Integration (Week 3)
- [ ] **Create Comments Panel** (AC4)
  - [ ] Build CommentsPanel component with comment list display
  - [ ] Integrate with annotation system for comment data
  - [ ] Implement click-to-navigate from comments to document locations
  - [ ] Add comment threading and reply functionality
  - [ ] Create chronological sorting with author and timestamp display

- [ ] **Mobile Optimization** (AC6)
  - [ ] Implement mobile bottom sheet panel layout
  - [ ] Add swipe gesture navigation between panels
  - [ ] Optimize touch targets for mobile interaction
  - [ ] Test responsive behavior across device sizes
  - [ ] Ensure performance on mobile devices

## Technical Requirements

### Component Architecture
```typescript
// New sidebar panel components
components/pdf-editor/sidebar-panels/
├── SidebarPanelContainer.tsx    // Panel system container
├── ThumbnailsPanel.tsx          // Page thumbnail navigation
├── OutlinesPanel.tsx            // Document outline/bookmarks
├── SearchPanel.tsx              // Full-text search with highlighting
├── CommentsPanel.tsx            // Annotation/comment management
└── MobilePanelSheet.tsx         // Mobile bottom sheet layout
```

### State Management Updates
```typescript
interface PDFEditorState {
  // Sidebar panel system
  activeSidebarPanel: 'thumbnails' | 'outlines' | 'search' | 'comments' | null;
  sidebarCollapsed: boolean;
  
  // Thumbnails state
  thumbnails: PageThumbnail[];
  thumbnailsLoaded: boolean;
  
  // Search functionality
  searchQuery: string;
  searchResults: SearchResult[];
  currentSearchIndex: number;
  searchCaseSensitive: boolean;
  
  // Document outline
  documentOutlines: OutlineItem[];
  currentOutlineLocation: string | null;
  
  // Comments system  
  comments: CommentItem[];
  activeComment: string | null;
  
  // Mobile responsive
  isMobileLayout: boolean;
  mobileSheetOpen: boolean;
}

interface SearchResult {
  pageNumber: number;
  textContent: string;
  boundingBox: Rectangle;
  highlightId: string;
}

interface OutlineItem {
  id: string;
  title: string;
  level: number;
  pageNumber: number;
  children: OutlineItem[];
  expanded: boolean;
}
```

### Search Implementation
```typescript
// Full-text search functionality
class PDFTextSearchEngine {
  async extractText(pdfDocument: PDFDocument): Promise<PageText[]>
  async searchText(query: string, caseSensitive: boolean): Promise<SearchResult[]>
  highlightSearchResults(results: SearchResult[]): void
  clearHighlights(): void
  navigateToResult(index: number): void
}

interface PageText {
  pageNumber: number;
  textContent: string;
  textItems: TextItem[];
}
```

### Mobile Panel Architecture
```typescript
// Mobile bottom sheet implementation  
interface MobileSheetProps {
  isOpen: boolean;
  onClose: () => void;
  snapPoints: number[]; // [0.3, 0.6, 0.9] for 30%, 60%, 90% height
  children: React.ReactNode;
}
```

## Testing Requirements

### Unit Tests
- [ ] SidebarPanelContainer tab switching and state management
- [ ] ThumbnailsPanel virtual scrolling and navigation
- [ ] SearchPanel text search and result highlighting
- [ ] OutlinesPanel tree navigation and expansion
- [ ] CommentsPanel comment display and navigation
- [ ] Mobile responsive panel behavior

### Integration Tests
- [ ] Panel integration with PDF viewer navigation
- [ ] Search highlighting integration with document display
- [ ] Thumbnail navigation with page changes
- [ ] Outline navigation with document scrolling
- [ ] Comment navigation with annotation system
- [ ] Mobile panel behavior with touch interactions

### E2E Tests (Playwright)
- [ ] Complete sidebar panel workflow testing
- [ ] Search functionality with various document types
- [ ] Thumbnail navigation with large documents
- [ ] Mobile panel interactions and gestures
- [ ] Cross-browser compatibility for panel animations
- [ ] Accessibility testing for panel keyboard navigation

## Performance Requirements

### Optimization Strategies
- **Virtual Scrolling**: Implement virtual scrolling for thumbnail lists with large documents
- **Lazy Loading**: Progressive thumbnail generation as user scrolls
- **Search Indexing**: Efficient text extraction and indexing for fast search
- **Memory Management**: Proper cleanup of thumbnails and search highlights
- **Mobile Performance**: Optimized rendering for mobile devices

### Performance Metrics
- [ ] **Panel Opening**: < 200ms panel switching transitions
- [ ] **Search Speed**: < 1s search results for documents up to 100 pages
- [ ] **Thumbnail Loading**: < 500ms for visible thumbnail generation
- [ ] **Mobile Responsiveness**: 60fps animations on mobile devices
- [ ] **Memory Usage**: Efficient memory management without leaks

## Success Metrics

### User Experience
- [ ] **Professional Navigation**: Matches PDF.js Express sidebar functionality
- [ ] **Search Accuracy**: Reliable text search with accurate highlighting
- [ ] **Mobile Usability**: Intuitive mobile panel interactions
- [ ] **Performance**: Smooth panel operations on large documents

### Technical Quality
- [ ] **Component Reusability**: Modular panel components for future extensions
- [ ] **Accessibility**: Full keyboard navigation and screen reader support  
- [ ] **Cross-browser Support**: Consistent behavior across modern browsers
- [ ] **Code Quality**: TypeScript coverage >95% with comprehensive testing

## Dependencies
- **Prerequisite**: Story 1.2.1 (Professional PDF Viewer Interface) must be completed
- **Integration**: Coordinates with existing PDF viewer and annotation systems
- **Enables**: Story 1.2.3 (Advanced Annotation System) will use comments panel

## Definition of Done
- [ ] All acceptance criteria validated with automated tests
- [ ] Panel system architecture supports future feature extensions
- [ ] Mobile responsive behavior tested on multiple devices
- [ ] Search performance benchmarked with large documents
- [ ] Accessibility audit completed with WCAG 1.2.1 compliance
- [ ] Visual design matches professional PDF editor standards
- [ ] Code review completed with performance optimization
- [ ] Documentation updated with panel system architecture
- [ ] User testing conducted with positive navigation feedback

---
**Story Points**: 21 (High complexity due to search indexing, thumbnail generation, and mobile responsive panels)  
**Priority**: High (Essential for professional document navigation experience)  
**Risk Level**: Medium (Text extraction and search performance considerations)